import api from '@api/api';
import Button from '@components/common/Button/Button';
import Checkbox from '@components/common/Input/Checkbox';
import Input from '@components/common/Input/Input';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import SelectBox from '@components/common/SelectBox/SelectBox';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';

import joinClassName from '@utils/joinClassName';
import { useEffect, useState } from 'react';
import { useSaveMenuStore } from '@store/useSaveMenuStore';

interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

interface MenuItem {
  id: number;
  name: string;
  desc: string | null;
  url: string | null;
  depth: number;
  sortNo: number;
  useYn: 'Y' | 'N';
  dateInfo: DateInfo;
  childs: MenuItem[];
}

type MenuDetailType = { title: string; required?: boolean; value: number | string | JSX.Element }[];

const defaultMenuItem: MenuItem = {
  id: 0,
  name: '',
  desc: null,
  url: null,
  depth: 0,
  sortNo: 0,
  useYn: 'Y',
  dateInfo: {
    createUser: '',
    createDate: '',
    updateUser: '',
    updateDate: '',
  },
  childs: [],
}; // menudetail 초기값

const MenuDetail = ({ categoryId }) => {
  const [menuDetail, setMenuDetail] = useState<MenuItem>(defaultMenuItem);
  const [isMenuVisible, setIsMenuVisible] = useState(true);
  const [menuName, setMenuName] = useState(''); // 메뉴 이름
  const [pageURL, setPageURL] = useState(''); // 페이지 URL
  const [useYN, setUseYN] = useState({ label: '', value: '' }); // 사용 여부
  const [menuType, setMenuType] = useState(''); // 메뉴 타입
  const [menuDesc, setMenuDesc] = useState(''); // 설명
  const [relatedPageName, setRelatedPageName] = useState({}); // 관련페이지 이름
  const [relatedPageURL, setRelatedPageURL] = useState({}); // 관련페이지 URL
  const [relatedPageChecked, setRelatedPageChecked] = useState({}); // 관련 페이지 checkbox 상태
  const [relatedPageAllChecked, setRelatedPageAllChecked] = useState(false);

  const [alertContents, setAlertContents] = useState({
    alertState: false,
    content: '',
    onConfirm: () => {},
  });
  const [confirmContents, setConfirmContents] = useState({
    confirmState: false,
    content: '',
    onConfirm: () => {},
    onCancle: () => {},
  });
  const { setIsSaved } = useSaveMenuStore();

  /**메뉴 상세 가져오기 */
  const getMenuDetail = async (id) => {
    const apiUrl = `/api-admin/user/menus/${id}`;
    const response = await api.get(apiUrl);

    // 데이터 통신 성공
    if (response.status === 200) {
      const data = response.data.data;

      setMenuDetail(data);
      setMenuName(data.name || '');
      setPageURL(data.url || '');
      setMenuDesc(data.desc || '');
      setUseYN({
        label: data.useYn,
        value: data.useYn,
      });

      handleSetRelatedInputState(data.childs);
    } else if (response.status === 400) {
      setAlertContents({
        alertState: true,
        content: '메뉴 정보를 불러올 수 없습니다',
        onConfirm: () => {
          setAlertContents((prev) => ({ ...prev, alertState: false }));
        },
      });
    }
  };

  useEffect(() => {
    setIsSaved(false);

    if (categoryId > 0) {
      // click이 되었을 때만
      getMenuDetail(categoryId);
      setIsMenuVisible(true);
    } else {
      setIsMenuVisible(false);
    }
  }, [categoryId]);

  /**관련페이지 input 배열 state 관리 */
  const handleSetRelatedInputState = (childs) => {
    const relatedPageNameObj = {};
    const relatedPageUrlObj = {};
    const relatedPageCheckedObj = {};

    childs.forEach((childMenu, idx) => {
      relatedPageNameObj[`pageName${idx}`] = childMenu.name;
      relatedPageUrlObj[`pageUrl${idx}`] = childMenu.url;
      relatedPageCheckedObj[`pageChecked-${childMenu.id > 0 ? childMenu.id : `add${idx}`}`] = false; // idx로만 놓을 경우 실제 id와 겹치는 현상 발생할 수 있음
    });

    setRelatedPageName(relatedPageNameObj);
    setRelatedPageURL(relatedPageUrlObj);
    setRelatedPageChecked(relatedPageCheckedObj);
  };

  useEffect(() => {
    handleSetRelatedInputState(menuDetail.childs);
  }, [menuDetail.childs]);

  /**관련 페이지 각 name에 맞는 input value 상태 업데이트 */
  const updateRelatedPageInputField = (field, e) => {
    const { name, value } = e.target;

    const setStateFunction = field === 'name' ? setRelatedPageName : setRelatedPageURL; // input state 선택

    setStateFunction((prevState) => {
      const updatedState = { ...prevState, [name]: value };

      // menu detail 값 업데이트
      setMenuDetail((prevMenuDetail) => {
        const updatedChilds = prevMenuDetail.childs.map((childMenu, idx) => {
          const key = `${field === 'name' ? 'pageName' : 'pageUrl'}${idx}`;

          if (updatedState[key] === '') {
            return {
              ...childMenu,
              [field]: '',
            };
          } // 값이 빈값일 경우

          if (updatedState[key]) {
            return {
              ...childMenu,
              [field]: updatedState[key], // name or url value 업데이트
            };
          }

          return childMenu;
        });

        return {
          ...prevMenuDetail,
          childs: updatedChilds,
        };
      });

      return updatedState;
    });
  };

  /**input 입력 핸들링 */
  const handleGetRelatedPageName = (e) => updateRelatedPageInputField('name', e);
  const handleGetRelatedPageURL = (e) => updateRelatedPageInputField('url', e);

  /**관련페이지 추가 */
  const handleAddRelatedPage = () => {
    const newData = {
      ...defaultMenuItem,
      depth: menuDetail.depth + 1,
      sortNo: menuDetail.childs.length + 1,
    };

    setMenuDetail((prevMenuDetail) => ({
      ...prevMenuDetail,
      childs: [...prevMenuDetail.childs, newData],
    }));
  };

  /**관련 페이지 삭제 */
  const handleRelatedPageChecked = (id) => {
    setRelatedPageChecked((prev) => ({
      ...prev,
      [`pageChecked-${id}`]: !prev[`pageChecked-${id}`],
    }));
  }; // check 상태 감지

  useEffect(() => {
    const isAllChecked =
      Object.keys(relatedPageChecked).length !== 0 && Object.values(relatedPageChecked).every(Boolean);

    isAllChecked ? setRelatedPageAllChecked(true) : setRelatedPageAllChecked(false);
  }, [relatedPageChecked]);

  const handleRelatedPageDeletePop = () => {
    setConfirmContents({
      confirmState: true,
      content: '선택된 메뉴를 삭제하시겠습니까?',
      onConfirm: () => {
        setConfirmContents((prev) => ({ ...prev, confirmState: false }));
        handleRelatedPageDelete();
      },
      onCancle: () => {
        setConfirmContents((prev) => ({ ...prev, confirmState: false }));
      },
    });
  }; // 삭제 confirm

  const handleRelatedPageDelete = async () => {
    // id 없는 row 삭제
    const deltedPageLocalData = Object.keys(relatedPageChecked)
      .filter((key) => relatedPageChecked[key] === true && key.split('-')[1].startsWith('add'))
      .map((localMenu) => parseInt(localMenu.replace(/\D/g, ''), 10));

    // DB에 저장된 row 삭제
    const deletedPageDBData = Object.keys(relatedPageChecked)
      .filter((key) => relatedPageChecked[key] === true && !key.split('-')[1].startsWith('add'))
      .map((checkedId) => parseInt(checkedId.split('-')[1]));

    const updateData = (prevMenuDetail, hasDBData) => {
      if (hasDBData) {
        return {
          ...prevMenuDetail,
          childs: prevMenuDetail.childs.filter(
            (menu, idx) => !deltedPageLocalData.includes(idx) && !deletedPageDBData.includes(menu.id)
          ),
        };
      } else {
        return {
          ...prevMenuDetail,
          childs: prevMenuDetail.childs.filter((_, idx) => !deltedPageLocalData.includes(idx)),
        };
      }
    }; // 새로고침 안하고 menu childs 업데이트된 것 화면에 렌더링하기 위함

    if (deltedPageLocalData.length > 0 && deletedPageDBData.length === 0) {
      // DB에 저장되지 않은 데이터만 삭제하는 경우
      setAlertContents({
        alertState: true,
        content: '메뉴가 삭제되었습니다',
        onConfirm: () => {
          setAlertContents((prev) => ({ ...prev, alertState: false }));
          setMenuDetail(updateData(menuDetail, false));
          setRelatedPageAllChecked(false);
        },
      });
    } else {
      // API 통신이 필요한 경우
      const apiUrl = '/api-admin/user/menus/delete';
      const response = await api.post(apiUrl, { ids: deletedPageDBData });

      if (response.status === 200) {
        setAlertContents({
          alertState: true,
          content: '메뉴가 삭제되었습니다',
          onConfirm: () => {
            setAlertContents((prev) => ({ ...prev, alertState: false }));
            setMenuDetail(updateData(menuDetail, true));
            setIsSaved(true);
            setRelatedPageAllChecked(false);
          },
        });
      }
    }
  };

  const handleRelatedPageAllDelete = (e) => {
    const relatedPageCheckedObj = {};

    menuDetail.childs.forEach((childMenu, idx) => {
      const key = `pageChecked-${childMenu.id > 0 ? childMenu.id : `add${idx}`}`;
      relatedPageCheckedObj[key] = e.target.checked;
    });

    setRelatedPageChecked(relatedPageCheckedObj);
  };

  /** 저장 */
  const handleSaveMenuInfo = async () => {
    const childsUpdatedData = menuDetail.childs.map((childMenu, idx) => {
      const pageNameKey = `pageName${idx}`;
      const urlKey = `pageUrl${idx}`;

      return {
        id: childMenu.id > 0 ? childMenu.id : null,
        name: relatedPageName[pageNameKey] ? relatedPageName[pageNameKey].trim() : '',
        url: relatedPageURL[urlKey] ? relatedPageURL[urlKey].trim() : '',
        useYn: childMenu.useYn,
      };
    });

    const postData = {
      name: menuName.trim(),
      desc: menuDesc ? menuDesc.trim() : '',
      url: pageURL ? pageURL.trim() : '',
      useYn: useYN.value,
      childs: childsUpdatedData,
    };

    // 필수값 체크
    const childsHasNoRequiredValue = postData.childs.map((child) => Boolean(child.name));
    const hasNoRequiredValue = [postData.name, postData.useYn, ...childsHasNoRequiredValue]
      .map((required) => Boolean(required))
      .includes(false);

    if (hasNoRequiredValue) {
      setAlertContents({
        alertState: true,
        content: '필수값을 입력해 주세요',
        onConfirm: () => {
          setAlertContents((prev) => ({ ...prev, alertState: false }));
        },
      });
    } else {
      const apiUrl = `/api-admin/user/menus/${categoryId}`;
      const response = await api.post(apiUrl, postData);

      if (response.status === 200) {
        setAlertContents({
          alertState: true,
          content: '변경사항이 저장되었습니다',
          onConfirm: () => {
            setAlertContents((prev) => ({ ...prev, alertState: false })); // 저장 성공
            getMenuDetail(categoryId); // data refresh
            setIsSaved(true); // 카테고리 리스트 refresh 위한 저장 상태 전송
          },
        });
      }
    }
  };

  // 메뉴 정보
  const menuInfo: MenuDetailType = [
    { title: '메뉴 ID', value: menuDetail.id },
    {
      title: '메뉴 이름',
      required: true,
      value: (
        <Input
          inputSize="small"
          placeholder="메뉴 이름을 입력해 주세요"
          value={menuName}
          onChange={(e) => setMenuName(e.target.value)}
        />
      ),
    },
    {
      title: '페이지 URL',
      value: (
        <Input
          inputSize="small"
          placeholder="페이지 URL을 입력해 주세요"
          value={pageURL}
          onChange={(e) => setPageURL(e.target.value)}
        />
      ),
    },
    {
      title: '사용 여부',
      required: true,
      value: (
        <SelectBox
          options={[
            { label: 'Y', value: 'Y' },
            { label: 'N', value: 'N' },
          ]}
          setSelectedValue={setUseYN}
          selectedValue={{ label: useYN.label, value: useYN.value }}
          defaultValue={{ label: useYN.label, value: useYN.value }}
        />
      ),
    },
    {
      title: '메뉴 타입',
      value: (
        <Input
          inputSize="small"
          placeholder="메뉴 타입을 입력해 주세요"
          value={menuType}
          onChange={(e) => setMenuType(e.target.value)}
        />
      ),
    },
    {
      title: '설명',
      value: (
        <Input
          inputSize="small"
          placeholder="메뉴 설명을 입력해 주세요"
          value={menuDesc}
          onChange={(e) => setMenuDesc(e.target.value)}
        />
      ),
    },
    { title: '등록자', value: menuDetail.dateInfo.createUser },
    { title: '생성 일시', value: menuDetail.dateInfo.createDate },
    { title: '최종 수정자', value: menuDetail.dateInfo.updateUser },
    { title: '최종 수정일시', value: menuDetail.dateInfo.updateDate },
  ];

  // 관련 페이지
  const menuRelatedPageHeader = [
    {
      title: (
        <Checkbox
          label="all"
          value="all"
          checked={relatedPageAllChecked}
          onChange={(e) => handleRelatedPageAllDelete(e)}
        />
      ),
    },
    {
      title: 'ID',
    },
    {
      title: '페이지 이름',
      required: true,
    },
    {
      title: 'URL',
    },
  ];

  return (
    <div className="user_menu_info_wrap">
      {!isMenuVisible && <div className="user_menu_info_nothing">메뉴 정보를 조회할 메뉴를 선택해 주세요.</div>}
      <TableContainer className="user_menu_info">
        <colgroup id="user_menu_info_table">
          <col className="user_info_title" />
          <col />
        </colgroup>
        <TableBody>
          {menuInfo.map((menu, index) => (
            <tr key={index}>
              <th className={menu.required && 'menu_required'}>{menu.title}</th>
              <td>{isMenuVisible && menu.value}</td>
            </tr>
          ))}
        </TableBody>
      </TableContainer>
      {isMenuVisible && (
        <>
          <div className="user_menu_info_child_menu">
            <h3 className="c_sub_title">관련 페이지</h3>
            <div className="user_menu_info_child_menu_button">
              <Button text="삭제" onClick={handleRelatedPageDeletePop} fill="outlined" />
              <Button text="추가" onClick={handleAddRelatedPage} />
            </div>
            <TableContainer>
              <colgroup id="user_info_related_table">
                <col className="related_check" />
                <col className="related_id" />
                <col className="related_page_name" />
                <col />
              </colgroup>
              <TableHeader>
                <tr className="c_table_row">
                  {menuRelatedPageHeader.map((menuRelatedTitle, idx) => (
                    <th
                      className={joinClassName('c_table_cell', menuRelatedTitle.required && 'menu_required')}
                      key={`menuDetail-${idx}`}
                    >
                      {menuRelatedTitle.title}
                    </th>
                  ))}
                </tr>
              </TableHeader>
              <TableBody>
                {menuDetail.childs.length > 0 ? (
                  menuDetail.childs.map((childMenu, idx) => (
                    <tr className="related_table_row" key={`related-${idx}`}>
                      <td>
                        <Checkbox
                          label={childMenu.name}
                          value={childMenu.id}
                          checked={
                            relatedPageChecked[`pageChecked-${childMenu.id > 0 ? childMenu.id : `add${idx}`}`]
                              ? relatedPageChecked[`pageChecked-${childMenu.id > 0 ? childMenu.id : `add${idx}`}`]
                              : false
                          }
                          onChange={() => handleRelatedPageChecked(childMenu.id > 0 ? childMenu.id : `add${idx}`)}
                        />
                      </td>
                      <td>{childMenu.id > 0 ? childMenu.id : ''}</td>
                      <td>
                        <Input
                          placeholder="페이지 이름을 입력해 주세요"
                          name={`pageName${idx}`}
                          value={relatedPageName[`pageName${idx}`] ? relatedPageName[`pageName${idx}`] : ''}
                          onChange={(e) => handleGetRelatedPageName(e)}
                        />
                      </td>
                      <td>
                        <Input
                          placeholder="페이지 URL을 입력해 주세요"
                          name={`pageUrl${idx}`}
                          value={relatedPageURL[`pageUrl${idx}`] ? relatedPageURL[`pageUrl${idx}`] : ''}
                          onChange={(e) => handleGetRelatedPageURL(e)}
                        />
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr className="related_no_page">
                    <td colSpan={4}>조회된 데이터가 없습니다</td>
                  </tr>
                )}
              </TableBody>
            </TableContainer>
          </div>
          <Button text="저장" onClick={handleSaveMenuInfo} className="menu_save_button" />
        </>
      )}
      <Alert
        isOpenAlert={alertContents.alertState}
        children={alertContents.content}
        onConfirm={alertContents.onConfirm}
      />
      <Confirm
        isOpenConfirm={confirmContents.confirmState}
        children={confirmContents.content}
        onLeftButton={confirmContents.onCancle}
        onRightButton={confirmContents.onConfirm}
      />
    </div>
  );
};

export default MenuDetail;
