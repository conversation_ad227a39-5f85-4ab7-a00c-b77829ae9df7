import { create } from 'zustand';
import { StoreApi, UseBoundStore } from 'zustand';

interface TabStore {
  activeTab: string;
  setActiveTab: (tabId: string) => void;
}

type TabStoreType = UseBoundStore<StoreApi<TabStore>>;

const stores: { [key: string]: TabStoreType } = {};

export const createTabStore = (containerId: string) => {
  if (!stores[containerId]) {
    stores[containerId] = create<TabStore>((set) => ({
      activeTab: '',
      setActiveTab: (tabId) => set(() => ({ activeTab: tabId })),
    }));
  }
  return stores[containerId];
};
