// 활성 상태 코드
export type TActive = 'Y' | 'N';

// 계정 목록 조회 쿼리 파라미터
export interface UserListParams {
  page: number | null;
  size: number | null;
  adminId: string | null;
  name: string | null;
  lockYn: string | null;
}

// 계정 목록 조회 검색 폼 값
export interface ListFormValues {
  lockYn: { label: string; value: string } | null; // 계정 잠금 여부
  name: string; // 관리자 이름
  adminId: string; // 관리자 아이디
}

// 날짜 정보
export interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

// 계정 목록 아이템
export interface AdminListItem {
  id: number;
  adminId: string;
  name: string;
  delYn: TActive; // 삭제 여부
  dateInfo: DateInfo;
}

// 계정 목록 페이지네이션 정보
export interface PaginationData {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

// Detail 페이지 타입
export type pageType = 'add' | 'detail' | 'edit';

// Detail 페이지 폼 값
export interface DetailFormValues {
  adminId: string;
  name: string;
  password: string;
  passwordConfirm: string;
  activeYn: TActive;
  delYn: TActive;
  roleIds: number[];
  loginFailureCnt: number | null;
}

// 테이블 속성
export interface BodyRowProps {
  title: string | React.ReactNode;
  children?: React.ReactNode;
  required?: boolean;
}

// 관리자 계정 상세
export interface UserDetailItem {
  id: number | string;
  adminId: string;
  name: string;
  activeYn: TActive;
  roleIds: number[];
}

// 권한
export interface Role {
  id: number;
  name: string;
}
