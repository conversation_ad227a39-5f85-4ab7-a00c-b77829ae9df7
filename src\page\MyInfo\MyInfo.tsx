import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { updateMyInfo, updateMyInfoPw } from '@api/admin/adminUsersApi';
import { useMyInfoStore } from './store/useMyInfoStore';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useLogStore } from '@store/useLogStore';
import { getFrontInfo } from '@components/common/Log/utils/getFrontInfo';
import { useLogger } from '@components/common/Log/hooks/useLogger';

import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import FormInput from '@components/common/Form/FormInput';
import joinClassName from '@utils/joinClassName';

const MyInfo = () => {
  const { id, adminId, name, activeYn, roleIds, setMyInfo } = useMyInfoStore();
  const { setAlertState } = useAlertStore();
  const { setConfirmState } = useConfirmStore();
  const { setLogParams } = useLogStore();
  const { screenLog } = useLogger();

  // 이름 수정
  const [isEditName, setIsEditName] = useState<boolean>(false);
  const [editedName, setEditedName] = useState<string>('');

  // 비밀번호 수정
  const [isEditPassword, setIsEditPassword] = useState<boolean>(false);
  const [isEditPasswordChk, setIsEditPasswordChk] = useState<boolean>(false);
  const [hasPasswordError, setHasPasswordError] = useState<boolean>(false);
  const [hasPasswordChkError, setHasPasswordChkError] = useState<boolean>(false);

  const methods = useForm({ mode: 'onChange' });

  // 페이지 접근 시 screen loggin
  useEffect(() => {
    setLogParams((prev) => ({
      ...prev,
      menuName: '프로필 수정',
      logType: 'screen',
      ...getFrontInfo(),
    }));
    screenLog();
  }, []);

  const handleChangeName = (e) => {
    const editedNameVal = e.target.value;

    setEditedName(editedNameVal);
    methods.setValue('name', editedNameVal);
  };

  const handleChangePassword = (e) => {
    const passwordVal = e.target.value;

    setIsEditPassword(!!passwordVal.trim());
    methods.setValue('password', passwordVal.trim());
  };

  const handleChangePasswordChk = (e) => {
    const passwordChkVal = e.target.value;

    setIsEditPasswordChk(!!passwordChkVal.trim());
    methods.setValue('passwordCheck', passwordChkVal.trim());
  };

  useEffect(() => {
    // 취소 시 editedName 초기화
    if (!isEditName && !!editedName.trim()) {
      setEditedName('');
    } else if (!isEditName) {
      methods.clearErrors('name');
    }
  }, [isEditName]);

  useEffect(() => {
    // 비밀번호 변경 value 없을 때 error 초기화
    if (!isEditPasswordChk && !isEditPassword) {
      methods.clearErrors('password');
      methods.clearErrors('passwordCheck');
      setHasPasswordError(false);
      setHasPasswordChkError(false);
    }
  }, [isEditPassword, isEditPasswordChk]);

  const handleSubmit = async () => {
    const isValid = await methods.trigger();

    if (isValid) {
      setConfirmState({
        isOpen: true,
        className: 'my_info_update',
        confirmType: null,
        title: '프로필 수정',
        content: (
          <>
            <p>프로필을 변경하시겠습니까?</p>
            <div>
              <p>변경 사항: {[isEditName && '이름', isEditPassword && '비밀번호'].filter(Boolean).join(', ')}</p>
            </div>
          </>
        ),
        onCancel: () => {
          setConfirmState((prev) => ({ ...prev, isOpen: false }));
        },
        onConfirm: () => {
          setConfirmState((prev) => ({ ...prev, isOpen: false }));
          postProfileUpdate();
        },
      });
    }
  };

  const handleMyInfoResultControl = (editedField: string, statusCode: number, message: string) => {
    if (statusCode === 200) {
      setAlertState({
        isOpen: true,
        content: `${editedField} 변경이 완료되었습니다`,
        onConfirm: () => {
          window.location.reload();

          setAlertState({
            isOpen: false,
            content: '',
            onConfirm: () => {},
          });
        },
      });
    } else {
      setAlertState({
        isOpen: true,
        title: `${editedField} 변경에 실패하였습니다`,
        content: message,
        onConfirm: () => {
          setAlertState({
            isOpen: false,
            content: '',
            onConfirm: () => {},
          });
        },
      });
    }
  };

  const postProfileUpdate = async () => {
    const passwordVal = methods.getValues('password');
    const passwordCheckVal = methods.getValues('passwordCheck');

    const postNameData = {
      name: isEditName ? editedName.trim() : name,
      activeYn,
      roleIds,
    };

    const postPwData = {
      id,
      password: passwordVal as string,
      passwordConfirm: passwordCheckVal as string,
    };

    // 이름/비밀번호 변경
    if (isEditName && isEditPassword && isEditPasswordChk) {
      const nameResponse = await updateMyInfo(postNameData);

      // 이름 수정 성공
      if (nameResponse.status === 200) {
        const pwResponse = await updateMyInfoPw(postPwData);
        handleMyInfoResultControl('비밀번호', pwResponse.status, pwResponse.data.message);

        if (pwResponse.status !== 200) {
          setIsEditName(false);
          setMyInfo({
            id: id,
            name: editedName.trim(),
            adminId,
            activeYn,
            roleIds,
          });
        }
      }
    }
    // 이름 변경
    else if (isEditName && !isEditPassword) {
      const response = await updateMyInfo(postNameData);
      handleMyInfoResultControl('이름', response.status, response.data.message);
    }
    // 비밀번호 변경
    else if (!isEditName && isEditPassword) {
      const response = await updateMyInfoPw(postPwData);
      handleMyInfoResultControl('비밀번호', response.status, response.data.message);
    }
  };

  const handleGetErrorState = async () => {
    await methods.trigger();

    const errorFields = methods.formState.errors;

    errorFields?.password ? setHasPasswordError(true) : setHasPasswordError(false);
    errorFields?.passwordCheck ? setHasPasswordChkError(true) : setHasPasswordChkError(false);
  };

  useEffect(() => {
    const password = methods.getValues('password');
    const passwordCheck = methods.getValues('passwordCheck');

    if (password || passwordCheck) {
      handleGetErrorState();
    }
  }, [methods.watch('password'), methods.watch('passwordCheck')]);

  return (
    <div className="my_info">
      <div className="my_info_contents">
        <Form methods={methods} onSubmit={handleSubmit}>
          <TableContainer>
            <TableBody>
              <TableBodyRow
                rowData={{
                  title: 'ID',
                  contents: adminId,
                }}
              />
              <TableBodyRow
                rowData={{
                  title: '이름',
                  contents: (
                    <>
                      <div className="my_name_cell">
                        <FormInput
                          name="name"
                          onChange={handleChangeName}
                          disabled={!isEditName}
                          value={!isEditName ? name : editedName}
                          rules={{
                            required: isEditName ? '* 한 글자 이상 입력해 주세요' : false,
                          }}
                        />
                      </div>
                      <Button
                        text={!isEditName ? '수정' : '취소'}
                        onClick={() => setIsEditName((prev) => !prev)}
                        color={!isEditName ? 'primary' : 'grayscale'}
                      />
                    </>
                  ),
                }}
              />
              <TableBodyRow
                rowData={{
                  title: '비밀번호 변경',
                  contents: (
                    <div className="password_change_cell">
                      <div className="password_input">
                        <FormInput
                          name="password"
                          type="password"
                          className={joinClassName(hasPasswordError && 'error')}
                          placeholder="변경할 비밀번호를 입력해 주세요"
                          onChange={handleChangePassword}
                          value={methods.watch('password') || ''}
                          rules={{
                            pattern: {
                              value: /^(?=.*[a-zA-Z])(?=.*[!@#$%^*+=-])(?=.*[0-9]).{8,20}$/,
                              message: '* 영문, 숫자, 특수문자 포함 8~20자로 입력해주세요',
                            },
                          }}
                          autoComplete="off"
                        />
                        <span className="desc">
                          비밀번호는 영문, 숫자, 특수문자를 포함하여 8글자 이상 20자 이하 길이로 등록해 주세요.
                        </span>
                      </div>
                      <div className="password_confirm">
                        <FormInput
                          name="passwordCheck"
                          type="password"
                          className={joinClassName(hasPasswordChkError && 'error')}
                          placeholder="변경할 비밀번호를 다시 입력해 주세요"
                          onChange={handleChangePasswordChk}
                          value={methods.watch('passwordCheck') || ''}
                          rules={{
                            validate: (value) => {
                              const password = methods.watch('password');
                              return password === value || '* 비밀번호가 일치하지 않습니다';
                            },
                          }}
                          autoComplete="off"
                        />
                        <span className="desc">동일한 패스워드를 한번 더 입력하세요.</span>
                      </div>
                    </div>
                  ),
                }}
              />
            </TableBody>
          </TableContainer>
          <Button
            clickLog={{ buttonSection: '프로필 수정' }}
            text="저장"
            className="my_info_save_btn"
            type="submit"
            disabled={!isEditName && !isEditPassword && !isEditPasswordChk}
          />
        </Form>
      </div>
    </div>
  );
};

export default MyInfo;
