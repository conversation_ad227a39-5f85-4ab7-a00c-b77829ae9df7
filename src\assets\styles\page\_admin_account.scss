@use '@styles/utils/mixin' as m;

.manage_admin_account {
  .c_table_wrapper {
    width: 100%;
    margin-bottom: 1.25rem;
    max-height: 650px;
    overflow-y: auto;
    .c_table {
      width: 100%;
      td,
      th {
        text-align: center;
      }
    }
  }

  .c_table_container_header {
    .left_cont {
      @include m.flex(center);
      gap: 1rem;
    }

    .right_cont {
      @include m.flex(center);
      gap: 0.25rem;
    }
  }

  // admin_account_list

  .admin_account_list {
    .ag-theme-quartz {
      .ag-header-custom-login_failure {
        text-align: center;
        span {
          font-size: 0.75rem;
          color: var(--g_07);
          @include m.bp_medium() {
            font-size: 0.625rem;
          }
        }
      }

      .ag-header-cell-comp-wrapper {
        @include m.flex(center, center, column);
      }
      .ag-header-cell-label {
        justify-content: center;
      }
    }
  }

  .c_grid_container {
    .ag-theme-quartz {
      .ag-header-cell-comp-wrapper {
        @include m.flex(center, center, column);
        text-align: center;
      }
    }
  }

  .button_wrapper {
    @include m.flex(center, space-between);
    gap: 1rem;
    margin-bottom: 1.25rem;

    .right {
      @include m.flex(center);
      gap: 0.5rem;
    }
  }
}

// admin_account_password_modal
.admin_account_password_modal {
  input {
    width: 350px;
  }

  table {
    tbody {
      tr {
        td {
          text-align: left;
        }
      }
    }
  }

  .button_wrapper {
    margin-top: 1rem;
    @include m.flex(center, end);
    gap: 0.25rem;
  }
}
