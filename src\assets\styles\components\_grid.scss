@use '@styles/utils/mixin' as m;

.c_grid_container {
  position: relative;
  height: calc(100vh - 360px);
  margin-bottom: 1rem;

  @include m.bp_large() {
    height: calc(100vh - 330px);
  }

  @include m.bp_medium() {
    height: calc(100vh - 300px);
  }

  &.c_grid_container_reset {
    width: 100%;
    height: 100%;
    margin-bottom: 0;
  }

  .ag-checkbox-edit {
    justify-content: center;
  }

  .ag-header-group-cell {
    .ag-header-cell-comp-wrapper {
      justify-content: center;
      align-items: center;
    }
  }
}

.ag-theme-quartz {
  .ag-header-cell-label {
    justify-content: center;
  }

  @include m.bp_large() {
    font-size: 0.75rem !important;
  }

  @include m.bp_medium() {
    font-size: 0.625rem !important;
  }
}

.c_grid_loading {
  height: calc(100vh - 360px);

  @include m.bp_large() {
    height: calc(100vh - 330px);
  }

  @include m.bp_medium() {
    height: calc(100vh - 300px);
  }
}
