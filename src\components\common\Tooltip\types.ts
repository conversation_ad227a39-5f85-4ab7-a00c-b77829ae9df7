import { ReactNode } from 'react';
import { ITooltip, TooltipRefProps } from 'react-tooltip';
// import type { Middleware } from '@floating-ui/react';

/**
 * 문서용 타입입니다.
 * 실제 사용은 Partial<ITooltip>를 따르며, 이 타입은 설명 및 가이드 참고용입니다.
 */

export interface TooltipProps extends Partial<ITooltip> {
  id?: string; // 각 툴팁을 고유하게 식별하기 위한 ID (ex: "tooltip-1")   //* 없으면 툴팁아이콘만 사용 가능

  content?: string; // 툴팁에 표시할 텍스트 콘텐츠 (ex: "툴팁 내용")

  render?: (props: { content: string; activeAnchor: HTMLElement }) => React.ReactNode;
  // 툴팁 내용을 동적으로 렌더링하는 함수 (ex: render={({ content, activeAnchor }) => <div><img src={TestImg} alt="test-img"style={{ width: '100px', height: 'auto', marginBottom: '10px' }}/></div>
  //*  툴팁에 이미지 삽입시 사용.

  children?: ReactNode; // 툴팁을 적용할 대상 요소

  ref?: React.Ref<TooltipRefProps>; // 툴팁 요소에 대한 React ref (ex: ref={tooltipRef})

  className?: string; // 툴팁 컨테이너의 사용자 정의 클래스명 (ex: "custom-tooltip")

  classNameArrow?: string; // 툴팁 화살표 요소의 사용자 정의 클래스명 (ex: "custom-arrow")

  place?:
    | 'top'
    | 'top-start'
    | 'top-end'
    | 'right'
    | 'right-start'
    | 'right-end'
    | 'bottom'
    | 'bottom-start'
    | 'bottom-end'
    | 'left'
    | 'left-start'
    | 'left-end';
  // 툴팁의 위치 (ex: "bottom") //* default: 'top'

  offset?: number; // 툴팁과 앵커 요소 사이의 간격 (ex: 10) //* default: 10

  anchorSelect?: string; // 툴팁을 트리거하는 앵커 요소의 CSS 선택자 (ex: ".tooltip-target")

  variant?: 'dark' | 'light' | 'success' | 'warning' | 'error' | 'info';
  // 툴팁의 스타일 테마 //* default: 'dark'

  wrapper?: keyof JSX.IntrinsicElements; // 툴팁을 감싸는 HTML 태그 (ex: "span") //* default: 'div'

  openOnClick?: boolean; // 클릭 시 툴팁을 열지 여부 //* default: false

  positionStrategy?: 'absolute' | 'fixed'; // 툴팁의 위치 전략 //* default: 'absolute'

  position?: DOMRect | { x: number; y: number }; // 툴팁의 위치를 수동으로 지정 (ex: { x: 100, y: 50 })

  delayShow?: number; // 툴팁이 표시되기 전 지연 시간 (ms) //* default: 0

  delayHide?: number; // 툴팁이 사라지기 전 지연 시간 (ms) //* default: 0

  float?: boolean; // 마우스 이동 시 툴팁이 따라다니도록 설정 //* default: false

  hidden?: boolean; // 툴팁을 강제로 숨길지 여부  //* default: false

  noArrow?: boolean; // 툴팁의 화살표 표시 여부 //* default: false

  clickable?: boolean; // 툴팁 내부 요소 클릭 가능 여부 //* default: false

  openEvents?: Record<string, boolean>; // 툴팁을 여는 이벤트 목록 (ex: { hover: true, focus: true }) //* default: { hover: true, focus: true }

  closeEvents?: Record<string, boolean>; // 툴팁을 닫는 이벤트 목록 (ex: { click: true }) //* default: { click: true }

  globalCloseEvents?: Record<string, boolean>; // 전역적으로 툴팁을 닫는 이벤트 목록 (ex: { escape: true }) //* default: { escape: true }

  imperativeModeOnly?: boolean; // imperative 모드에서만 툴팁을 제어하도록 설정 //* default: false

  style?: React.CSSProperties; // 툴팁 컨테이너의 인라인 스타일 (ex: { backgroundColor: "blue" })

  isOpen?: boolean; // 툴팁의 현재 열림 상태를 제어 //* default: false

  defaultIsOpen?: boolean; // 초기에 툴팁을 표시할지 여부 //* default: false

  setIsOpen?: (isOpen: boolean) => void; // 툴팁의 열림 상태를 외부에서 변경하는 함수 (ex: setIsOpen(true))

  afterShow?: () => void; // 툴팁이 표시된 후 실행할 콜백 함수 (ex: () => console.log("툴팁 표시됨"))

  afterHide?: () => void; // 툴팁이 사라진 후 실행할 콜백 함수 (ex: () => console.log("툴팁 숨겨짐"))

  disableTooltip?: (anchorRef: HTMLElement | null) => boolean;
  // 특정 앵커 요소에 대해 툴팁을 비활성화할지 여부를 반환하는 함수 (ex: (ref) => !!ref?.classList.contains("disabled"))

  // middlewares?: Middleware[]; // Floating UI의 미들웨어 배열 (ex: [offset(10)])

  border?: string; // 툴팁 테두리 스타일 (ex: "1px solid #ccc")

  opacity?: number; // 툴팁의 투명도 (ex: 0.8) //* default: 0.9

  arrowColor?: string; // 툴팁 화살표 색상 (ex: "#ff0000")

  disableStyleInjection?: boolean | 'core';
  // 툴팁의 기본 스타일 주입을 비활성화할지 여부 //* default: false

  role?: 'tooltip' | 'dialog'; // ARIA 역할 설정 //* default: 'tooltip'
}
