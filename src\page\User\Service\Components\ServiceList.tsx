import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Input from '@components/common/Input/Input';
import TableBody from '@components/common/Table/TableBody';
import TableCell from '@components/common/Table/TableCell';
import TableContainer from '@components/common/Table/TableContainer';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import TableHeader from '@components/common/Table/TableHeader';
import TableRow from '@components/common/Table/TableRow';
import { useConfirm } from '@hooks/useConfirm';
import { useUserServiceAPI } from '@page/User/Service/hooks/useUserServiceAPI';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { searchByKeywordAndKey } from '@utils/searchByKeywordAndKey';
import { useEffect, useState } from 'react';
import ServiceModal from './ServiceModal';

const ServiceList = () => {
  // constant
  const tableHeader = [
    'NO',
    '서비스 명',
    'URL',
    'Method',
    '활성화',
    '등록자',
    '등록일시',
    '수정자',
    '수정일시',
    '수정',
    '삭제',
  ];

  const { serviceList, serviceGroupDetail, setModalType, setIsOpenServiceModal, setServiceDetail } = useServiceStore();

  const { openConfirm } = useConfirm();
  const { getServiceList, deleteServiceData } = useUserServiceAPI();

  // state
  const [serviceName, setServiceName] = useState('');
  const [searchResultServiceList, setSearchResultServiceList] = useState(null);

  // functions
  const handleServiceName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServiceName(e.target.value);
  };

  const handleSearchServiceByKeyword = () => {
    const searchResult = searchByKeywordAndKey(serviceList, serviceName, 'name');
    setSearchResultServiceList(searchResult);
  };

  const handleKeydownSearchService = (e: React.KeyboardEvent<HTMLButtonElement | HTMLInputElement>) => {
    e.key === 'Enter' && handleSearchServiceByKeyword();
  };

  const handleRegisterService = () => {
    setModalType('add');
    setIsOpenServiceModal(true);
    setServiceDetail(null);
  };

  const handleModifyService = (serviceInfo) => {
    setModalType('edit');
    setIsOpenServiceModal(true);
    setServiceDetail(serviceInfo);
  };

  const handleDeleteService = (serviceInfo) => {
    openConfirm(
      '삭제하시겠습니까?',
      () => {
        deleteServiceData(serviceInfo.id);
      },
      'delete'
    );
  };

  // 상위에서 서비스 그룹선택 변경시 검색 초기화
  useEffect(() => {
    setServiceName('');
    setSearchResultServiceList(null);
    getServiceList();
  }, [serviceGroupDetail]);

  return (
    <>
      <ControlBox>
        <Input
          placeholder="서비스명 입력"
          value={serviceName}
          onChange={handleServiceName}
          onKeyDown={handleKeydownSearchService}
        />
        <Button text="조회" onClick={handleSearchServiceByKeyword} onKeyDown={handleKeydownSearchService} />
      </ControlBox>
      <TableContainerHeader
        leftChildren={<>총 {serviceList.length}개</>}
        rightChildren={<Button text="서비스 등록" onClick={handleRegisterService} />}
      />
      <TableContainer className="service_list">
        <colgroup>
          <col width={'3%'} />
          <col width={'13%'} />
          <col width={'7%'} />
          <col width={'17%'} />
          <col width={'6%'} />
          <col width={'7%'} />
          <col width={'14%'} />
          <col width={'7%'} />
          <col width={'14%'} />
          <col width={'6%'} />
          <col width={'6%'} />
        </colgroup>
        <TableHeader>
          <TableRow cellData={tableHeader} tag="th" />
        </TableHeader>
        <TableBody>
          {serviceList || (searchResultServiceList && serviceList) ? (
            (searchResultServiceList || serviceList)?.map((item, idx) => (
              <tr key={item.id}>
                <TableCell>{serviceList.length - idx}</TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.url}</TableCell>
                <TableCell>{item.method}</TableCell>
                <TableCell>{item.useYn}</TableCell>
                <TableCell>{item.dateInfo.createUser}</TableCell>
                <TableCell>{item.dateInfo.createDate}</TableCell>
                <TableCell>{item.dateInfo.updateUser}</TableCell>
                <TableCell>{item.dateInfo.updateDate}</TableCell>
                <td>
                  <Button
                    text="수정"
                    onClick={() => {
                      handleModifyService(item);
                    }}
                  />
                </td>
                <td>
                  <Button
                    text="삭제"
                    color="red"
                    onClick={() => {
                      handleDeleteService(item);
                    }}
                  />
                </td>
              </tr>
            ))
          ) : (
            <tr className="no_result">
              <td colSpan={11}>조회된 결과가 없습니다.</td>
            </tr>
          )}
        </TableBody>
      </TableContainer>
      <ServiceModal />
    </>
  );
};

export default ServiceList;
