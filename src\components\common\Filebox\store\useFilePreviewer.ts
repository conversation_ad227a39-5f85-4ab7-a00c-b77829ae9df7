import { create } from 'zustand'

interface FilePreviewerStore {
  previewImageInfo: {
    name: string;
    url: string;
    size: number | string;
    type: string;
  } | null;
  setPreviewImageInfo: (image: { name: string; url: string; size: number | string; type: string }) => void;

  previewModalState: boolean;
  setPreviewModalState: (state: boolean) => void;
}
export const useFilePreviewer = create<FilePreviewerStore>((set) => ({
  previewImageInfo: null,
  setPreviewImageInfo: (image) => set({ previewImageInfo: image }),

  previewModalState: false,
  setPreviewModalState: (state) => {
    if (!state) {
      set({ previewImageInfo: null });
    }
    set({ previewModalState: state });
  },
}));
