import { useEffect, useState } from 'react';
import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';

interface PaginationProps {
  totalCount: number;
  itemCountPerPage?: number;
  pageCount?: number;
  currentPage: number;
  onPageChange: (pageNo: number) => void;
  disabled?: boolean;
}

const Pagination = ({
  totalCount, // 전체 item의 개수 (*)
  itemCountPerPage = 10, // 한 페이지에 보여질 item 개수 (*)
  pageCount = 5, // 한 그룹으로 보여질 페이지 번호 개수
  currentPage, // 현재 페이지(*)
  onPageChange, // 페이지 전환 함수(*)
  disabled = false,
}: PaginationProps) => {
  const totalPage = Math.max(Math.ceil(totalCount / itemCountPerPage), 1); // 전체 페이지 개수
  const [startPage, setStartPage] = useState(1); // 현재 보여 줄 페이지 그룹의 시작 번호

  const noPrev = currentPage <= 1; // 이전 페이지로 이동 가능한지 여부
  const noNext = currentPage >= totalPage; // 다음 페이지로 이동 가능한지 여부

  // 페이지 이동 처리 함수
  // type: 이동할 페이지 타입 (맨 앞으로, 이전, 다음, 맨 뒤로)
  const handlePageMove = (type: 'double_prev' | 'prev' | 'next' | 'double_next') => {
    switch (type) {
      case 'double_prev': // 1페이지로 이동
        onPageChange(1);
        break;
      case 'prev': // 이전 페이지 그룹으로 이동
        onPageChange(Math.max(startPage - pageCount, 1));
        break;
      case 'next': // 다음 페이지 그룹으로 이동
        onPageChange(Math.min(startPage + pageCount, totalPage));
        break;
      case 'double_next': // 마지막 페이지로 이동
        onPageChange(Math.min(totalPage));
        break;
    }
  };

  // 페이지 그룹값 셋팅
  useEffect(() => {
    const calculatedEndPage = Math.min(startPage + pageCount - 1, totalPage);

    if (totalPage > pageCount) {
      if (currentPage === 1) {
        setStartPage(1);
      } else if (currentPage === totalPage) {
        setStartPage(Math.max(Math.floor(totalCount / itemCountPerPage) + 1, totalPage));
      } else if (currentPage > calculatedEndPage) {
        setStartPage((prev) => Math.min(prev + pageCount, totalPage));
      } else if (currentPage < startPage) {
        setStartPage((prev) => Math.max(prev - pageCount, 1)); // 최소 페이지 1로 설정
      }
    }
  }, [currentPage, pageCount, startPage, totalPage]);

  return (
    <div className="c_pagination">
      <div className="c_pagination_inner">
        <div className="prev_btn_wrapper">
          <IconButton
            text="double prev"
            fill="unfilled"
            color="grayscale"
            icon="double_arrow_left"
            size='smallest'
            disabled={noPrev || disabled}
            iconOnly
            onClick={() => handlePageMove('double_prev')}
          />
          <IconButton
            text="prev"
            fill="unfilled"
            color="grayscale"
            icon="arrow_left"
            size='smallest'
            disabled={noPrev || disabled}
            iconOnly
            onClick={() => handlePageMove('prev')}
          />
        </div>
        {/* 페이지 그룹 */}
        <div className="c_pagination_num_list">
          {Array.from({ length: Math.min(pageCount, totalPage - startPage + 1) }).map((_, i) => (
            <Button
              key={startPage + i}
              fill="unfilled"
              color="grayscale"
              text={(startPage + i).toString()}
              onClick={() => onPageChange(startPage + i)}
              className={`page_num${currentPage === startPage + i ? ' current' : ''}`}
              disabled={disabled}
            />
          ))}
        </div>
        <div className="next_btn_wrapper">
          <IconButton
            text="next"
            fill="unfilled"
            color="grayscale"
            icon="arrow_right"
            size='smallest'
            disabled={noNext || disabled}
            iconOnly
            onClick={() => handlePageMove('next')}
          />
          <IconButton
            text="double next"
            fill="unfilled"
            color="grayscale"
            icon="double_arrow_right"
            size='smallest'
            disabled={noNext || disabled}
            iconOnly
            onClick={() => handlePageMove('double_next')}
          />
        </div>
      </div>
    </div>
  );
};

export default Pagination;
