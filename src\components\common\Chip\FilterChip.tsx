import joinClassName from '@utils/joinClassName';
import { FilterChipProps } from './types';

const FilterChip = ({ label, size = 'medium', active, className, onClick }: FilterChipProps) => {
  const activeClass = active ? 'active' : '';
  const chipClass = joinClassName('c_chip', 'c_chip_filter', size, activeClass, className);

  return (
    <div className={chipClass} onClick={onClick}>
      <span>{label}</span>
    </div>
  );
};

export default FilterChip;
