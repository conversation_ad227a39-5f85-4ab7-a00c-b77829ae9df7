import { ColDef, ColGroupDef, GridReadyEvent } from '@ag-grid-community/core';
import { useState, useEffect, useRef } from 'react';
import Grid from '@components/common/Grid/Grid';
import { useAuthStore } from '@page/Admin/Role/store/useAuthStore';

type PageAuth = {
  pageId: number;
  pageName: string;
  readRoleYn: boolean;
  regRoleYn: boolean;
  modRoleYn: boolean;
  delRoleYn: boolean;
};

const PageAuthList = () => {
  const { pageAuthListData, setPageAuthListData } = useAuthStore();

  // 체크박스 상태 변경 핸들러 함수
  const handleCellValueChanged = (params: any) => {
    const { data, colDef } = params;
    const field = colDef.field as keyof PageAuth;
    const pageId = data.pageId;

    // 기존 권한 업데이트
    const updatedList = pageAuthListData.map((auth) =>
      auth.pageId === pageId ? { ...auth, [field]: params.value } : auth
    );

    setPageAuthListData(updatedList);
  };

  const [GRID_COLUMNS] = useState<(ColDef | ColGroupDef)[]>([
    {
      headerName: '페이지 이름',
      field: 'pageName',
      flex: 1,
      filter: true,
      floatingFilter: true,
    },
    {
      headerName: '페이지 권한',
      children: [
        {
          headerName: '읽기',
          field: 'readRoleYn',
          flex: 1,
          minWidth: 90,
          cellRenderer: 'agCheckboxCellRenderer', // 체크박스 렌더링을 위한 기본 렌더러
          cellEditor: 'agCheckboxCellEditor', // 체크박스 편집을 위한 기본 에디터f
          editable: true,
          filter: true,
          floatingFilter: true,
        },
        {
          headerName: '쓰기',
          field: 'regRoleYn',
          flex: 1,
          minWidth: 90,
          cellRenderer: 'agCheckboxCellRenderer', // 체크박스 렌더링을 위한 기본 렌더러
          cellEditor: 'agCheckboxCellEditor', // 체크박스 편집을 위한 기본 에디터
          editable: true,
          filter: true,
          floatingFilter: true,
        },
        {
          headerName: '수정',
          field: 'modRoleYn',
          flex: 1,
          minWidth: 90,
          cellRenderer: 'agCheckboxCellRenderer', // 체크박스 렌더링을 위한 기본 렌더러
          cellEditor: 'agCheckboxCellEditor', // 체크박스 편집을 위한 기본 에디터
          editable: true,
          filter: true,
          floatingFilter: true,
        },
        {
          headerName: '삭제',
          field: 'delRoleYn',
          flex: 1,
          minWidth: 90,
          cellRenderer: 'agCheckboxCellRenderer', // 체크박스 렌더링을 위한 기본 렌더러
          cellEditor: 'agCheckboxCellEditor', // 체크박스 편집을 위한 기본 에디터
          editable: true,
          filter: true,
          floatingFilter: true,
        },
      ],
    },
  ]);

  useEffect(() => {}, [pageAuthListData]);

  return (
    <div className="page_auth_contents">
      <Grid
        rowData={pageAuthListData}
        columns={GRID_COLUMNS}
        autoSizeStrategy={'onGridSizeChanged'}
        resizable={true}
        gridOptions={{
          onCellValueChanged: handleCellValueChanged,
        }}
      />
    </div>
  );
};

export default PageAuthList;
