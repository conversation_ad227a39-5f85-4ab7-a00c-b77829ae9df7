import api from '@api/api';
import { DetailFormValues } from '@page/Admin/Account/type';
import { defaultHandleError } from '@utils/apiErrorHandler';

// 권한 목록 조회
interface GetRoleListParams {
  isGetErrorMsg?: boolean;
}

const getRoleList = async ({ isGetErrorMsg = false }: GetRoleListParams) => {
  try {
    const response = await api.get('/api-admin/admin/roles');

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 목록 조회');
  }
};

// 계정 상세 조회
interface GetAccountDetailParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const getAccountDetail = async ({ id, isGetErrorMsg = false }: GetAccountDetailParams) => {
  try {
    const response = await api.get(`/api-admin/admins/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 상세 조회');
  }
};

// 계정 중복 확인
interface GetCheckDuplicateIdParams {
  adminId: string;
  isGetErrorMsg?: boolean;
}

const getCheckDuplicateId = async ({ adminId, isGetErrorMsg = false }: GetCheckDuplicateIdParams) => {
  try {
    const response = await api.post(`/api-admin/admins/dup-check`, {
      adminId,
    });

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 중복 확인');
  }
};

// 계정 등록
interface AddAccountParams {
  data: DetailFormValues;
  isGetErrorMsg?: boolean;
}

const addAccount = async ({ data, isGetErrorMsg = false }: AddAccountParams) => {
  try {
    const response = await api.post(`${'/api-admin/admins'}`, {
      adminId: data.adminId,
      name: data.name,
      password: data.password,
      activeYn: data.activeYn,
      roleIds: data.roleIds,
    });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 등록');
  }
};

// 계정 수정
interface EditAccountParams {
  id: string | number;
  data: DetailFormValues;
  isGetErrorMsg?: boolean;
}

const editAccount = async ({ id, data, isGetErrorMsg = false }: EditAccountParams) => {
  try {
    const response = await api.post(`/api-admin/admins/${id}`, {
      name: data.name,
      activeYn: data.activeYn,
      roleIds: data.roleIds,
    });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 수정');
  }
};

// 계정 삭제
interface DeleteAccountParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const deleteAccount = async ({ id, isGetErrorMsg = false }: DeleteAccountParams) => {
  try {
    const response = await api.post(`/api-admin/admins/${id}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 삭제');
  }
};

// 계정 완전 삭제
interface DeleteAccountHardParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const deleteAccountHard = async ({ id, isGetErrorMsg = false }: DeleteAccountHardParams) => {
  try {
    const response = await api.post(`/api-admin/admins/complete/${id}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 완전 삭제');
  }
};

// 계정 복구
interface RestoreAccountParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const restoreAccount = async ({ id, isGetErrorMsg = false }: RestoreAccountParams) => {
  try {
    const response = await api.post(`/api-admin/admins/restore/${id}`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 복구');
  }
};

interface PostChangePasswordProps {
  id: string | number;
  data: DetailFormValues;
  isGetErrorMsg: boolean;
}

const postChangePassword = async ({ id, data, isGetErrorMsg = false }: PostChangePasswordProps) => {
  try {
    const response = await api.post(`/api-admin/admins/change-password/${id}`, {
      password: data.password,
      passwordConfirm: data.passwordConfirm,
    });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '패스워드 변경');
  }
};

// 계정 잠금 해제
interface PostUnlockAccountParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const postUnlockAccount = async ({ id, isGetErrorMsg = false }: PostUnlockAccountParams) => {
  try {
    const response = await api.post(`/api-admin/admins/unlock/${id}`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 잠금 해제');
  }
};

export {
  getRoleList,
  getAccountDetail,
  getCheckDuplicateId,
  addAccount,
  editAccount,
  deleteAccount,
  deleteAccountHard,
  postChangePassword,
  postUnlockAccount,
  restoreAccount,
};
