import { create } from 'zustand';
import { AuthData, MenuData, PageAuthData, ParsedPageAuthData, ParsedServiceData } from '@page/Admin/Role/type';

interface AuthStore {
  // 권한 목록
  authListData: AuthData[];
  setAuthListData: (authListData: AuthData[]) => void;

  // 원본 권한 목록
  originalAuthListData: AuthData[];
  setOriginalAuthListData: (originalAuthListData: AuthData[]) => void;

  // 상세 권한 데이터
  authData: AuthData;
  setAuthData: (authData: AuthData) => void;

  // 상세 권한 상태
  detailState: 'add' | 'edit' | null;
  setDetailState: (detailState: 'add' | 'edit' | null) => void;

  // 전체 메뉴 목록
  menuListData: MenuData[];
  setMenuListData: (menuListData: MenuData[]) => void;

  // 전체 서비스 목록
  serviceListData: ParsedServiceData[];
  setServiceListData: (serviceListData: ParsedServiceData[]) => void;

  // 전체 페이지 권한 목록
  pageAuthListData: ParsedPageAuthData[];
  setPageAuthListData: (pageAuthListData: ParsedPageAuthData[]) => void;
}

export const useAuthStore = create<AuthStore>((set) => ({
  authListData: [],
  setAuthListData: (authListData: AuthData[]) => set({ authListData }),

  originalAuthListData: [],
  setOriginalAuthListData: (originalAuthListData: AuthData[]) => set({ originalAuthListData }),

  authData: null,
  setAuthData: (authData: AuthData) => set({ authData }),

  detailState: null,
  setDetailState: (detailState: 'add' | 'edit' | null) => set({ detailState }),

  menuListData: [],
  setMenuListData: (menuListData: MenuData[]) => set({ menuListData }),

  serviceListData: [],
  setServiceListData: (serviceListData: ParsedServiceData[]) => set({ serviceListData }),

  pageAuthListData: [],
  setPageAuthListData: (pageAuthListData: ParsedPageAuthData[]) => set({ pageAuthListData }),
}));
