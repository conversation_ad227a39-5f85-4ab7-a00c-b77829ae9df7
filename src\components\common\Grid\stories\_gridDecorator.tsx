import type { Decorator } from '@storybook/react';

/**
 * 그리드 컴포넌트를 화면 가운데 정렬해주는 데코레이터
 */
export const CenteredDecorator: Decorator = (Story) => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'flex-start',
      minHeight: '100vh',
      width: '100vw',
      padding: '2rem',
      boxSizing: 'border-box',
      overflow: 'auto',
    }}
  >
    <div
      style={{
        width: '100%',
        maxWidth: '75rem',
        flexShrink: 0,
      }}
    >
      <Story />
    </div>
  </div>
);
