@use '@styles/utils/mixin' as m;
@use '@styles/utils/variables' as v;

[theme='light'] {
  button {
    @mixin light_icon_styles($icon_name) {
      &.i_#{$icon_name} {
        // 기본형
        &.filled {
          @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
        }

        // 투명형
        &.unfilled {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');

            &.active {
              @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');

              &:hover:not(:disabled) {
                @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');
              }
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_black.svg');
          }
        }

        // border만
        &.outlined {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');

            &.active,
            &:hover:not(:disabled) {
              @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_black.svg');
          }
        }
      }
    }

    @mixin light_editor_icon_styles($editor_icon_name) {
      &.i_#{$editor_icon_name} {
        // 기본형
        &.filled {
          @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
        }

        // 투명형
        &.unfilled {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');

            &.active {
              @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');

              &:hover:not(:disabled) {
                @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');
              }
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_black.svg');
          }
        }

        // border만
        &.outlined {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');

            &.active,
            &:hover:not(:disabled) {
              @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_black.svg');
          }
        }
      }
    }

    // 각 아이콘에 스타일 적용
    @each $icon_name in v.$icons {
      @include light_icon_styles($icon_name);
    }

    @each $icon_name in v.$editor_icons {
      &.i_#{$icon_name} {
        @include light_editor_icon_styles($icon_name);
      }
    }
  }
}

[theme='dark'] {
  button {
    @mixin dark_icon_styles($icon_name) {
      &.i_#{$icon_name} {
        // 기본형
        &.filled {
          @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_black.svg');
          }
        }

        // 투명형
        &.unfilled {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');

            &.active {
              @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');

              &:hover:not(:disabled) {
                @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');
              }
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
          }
        }

        // border만
        &.outlined {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_primary.svg');

            &.active,
            &:hover:not(:disabled) {
              @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_#{$icon_name}_white.svg');
          }
        }
      }
    }

    @mixin dark_editor_icon_styles($editor_icon_name) {
      &.i_#{$editor_icon_name} {
        // 기본형
        &.filled {
          @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_black.svg');
          }
        }

        // 투명형
        &.unfilled {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');

            &.active {
              @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');

              &:hover:not(:disabled) {
                @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');
              }
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
          }
        }

        // border만
        &.outlined {
          &.primary {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_primary.svg');

            &.active,
            &:hover:not(:disabled) {
              @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
            }
          }
          &.grayscale {
            @include m.before_url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg');
          }
        }
      }
    }

    // 각 아이콘에 스타일 적용
    @each $icon in v.$icons {
      @include dark_icon_styles($icon);
    }

    @each $icon in v.$editor_icons {
      &.i_#{$icon} {
        @include dark_editor_icon_styles($icon);
      }
    }

    &.filled {
      &.grayscale {
        color: var(--g_01);
      }
    }
  }
}

button {
  padding: 0.375rem 0.5rem;
  border-radius: 0.25rem;
  transition: all 0.3s;

  &:disabled {
    filter: opacity(0.45);
  }

  .c_button_text {
    font-size: 1rem;
    line-height: 1.5rem;
    font-weight: 600;
    @include m.ellipsis();
  }

  &.c_button_icon,
  &.c_button_icon_text {
    @include m.flex(center);
    &::before {
      @include m.content_without_url(1.5rem, 1.5rem);
    }
  }

  // 아이콘의 방향
  &.c_button_icon_text {
    gap: 0.625rem;

    &.left {
      flex-direction: row;
    }
    &.top {
      flex-direction: column;
    }
    &.right {
      flex-direction: row-reverse;
    }
    &.bottom {
      flex-direction: column-reverse;
    }
  }

  // 디자인
  &.capsule {
    border-radius: 50rem;
  }

  &.rect {
    border-radius: 0;
  }

  &.circle {
    &.icon_only {
      padding: 0;
      align-items: center;
      justify-content: center;
    }
    &.largest {
      border-radius: 21px;
    }

    &.large {
      border-radius: 20px;
    }

    &.medium {
      border-radius: 18px;
    }

    &.small {
      border-radius: 16px;
    }

    &.smallest {
      border-radius: 14px;
    }
  }

  &.switch {
    border-radius: 1rem;
    width: 2.5rem;
    height: 1.5rem;
    padding: 0.125rem 0.25rem;
    background-color: var(--g_03) !important;
    border: 1px solid var(--g_04);
    &::before {
      @include m.content_without_url(1rem, 1rem);
      background-color: #fff;
      border-radius: 50%;
      transition: all 0.3s;
    }
    &.active {
      background-color: var(--p_04) !important;
      &::before {
        margin-left: 0.875rem;
        background-color: #fff;
      }
    }
  }

  &.icon_only {
    padding: 0;

    &.largest {
      width: 44px;
      height: 44px;
      &::before {
        width: 100%;
        height: 100%;
      }
    }

    &.large {
      width: 40px;
      height: 40px;
      &::before {
        width: 100%;
        height: 100%;
      }
    }

    &.medium {
      width: 36px;
      height: 36px;
      &::before {
        width: 100%;
        height: 100%;
      }
    }

    &.small {
      width: 32px;
      height: 32px;
      &::before {
        width: 100%;
        height: 100%;
      }
    }

    &.smallest {
      width: 28px;
      height: 28px;
      &::before {
        width: 100%;
        height: 100%;
      }
    }
  }

  &.handle {
    // 패널, 모달 만들면서 만들기
  }

  // 색상
  // 기본형
  &.filled {
    color: var(--font_white);
    &.primary {
      background-color: var(--p_05);
      &.active {
        background-color: var(--p_04);
      }
      &:hover:not(:disabled) {
        background-color: var(--p_06);
      }
    }

    &.grayscale {
      background-color: var(--g_09);
      &.active {
        background-color: var(--g_05);
      }
      &:hover:not(:disabled) {
        background-color: var(--g_05);
      }
    }

    &.red {
      background-color: var(--red);
    }

    &:disabled {
      &.grayscale {
        background-color: var(--g_05);
        color: var(--g_02);
      }
    }
  }

  // 투명형
  &.unfilled {
    &.primary {
      color: var(--p_05);
      &.active {
        background-color: var(--p_05);
        color: var(--font_white);
      }
      &:hover:not(:disabled) {
        background-color: var(--p_08);
        color: var(--p_05);
      }
      &:disabled {
        color: var(--p_05);
      }
    }
    &.grayscale {
      color: var(--font_default);
      &.active {
        background-color: var(--g_02);
      }
      &:hover:not(:disabled) {
        background-color: var(--g_05);
      }
    }
  }

  // border
  &.outlined {
    &.primary {
      border: 1px solid var(--p_05);
      color: var(--p_05);

      &.active {
        border: 1px solid var(--p_04);
        background-color: var(--p_04);
        color: var(--font_white);
      }

      &:hover:not(:disabled) {
        border: 1px solid var(--p_03);
        background-color: var(--p_03);
        color: var(--font_white);
      }
    }
    &.grayscale {
      border: 1px solid var(--g_10);
      &.active {
        border: 1px solid var(--g_07);
        background-color: var(--g_04);
      }
      &:hover:not(:disabled) {
        border: 1px solid var(--g_07);
        background-color: var(--g_05);
      }
    }

    &:disabled {
      background: transparent;
    }
  }

  @include m.bp_large() {
    padding: 0.25rem 0.5rem;

    .c_button_text {
      font-size: 0.875rem;
    }

    &.c_button_icon,
    &.c_button_icon_text {
      @include m.flex(center);
      &::before {
        @include m.content_without_url(1.25rem, 1.25rem);
      }
    }

    &.c_button_icon_text {
      gap: 0.5rem;
    }
  }

  @include m.bp_medium() {
    padding: 0.125rem 0.5rem;

    .c_button_text {
      font-size: 0.75rem;
    }

    &.c_button_icon,
    &.c_button_icon_text {
      @include m.flex(center);
      &::before {
        @include m.content_without_url(1rem, 1rem);
      }
    }

    &.c_button_icon_text {
      gap: 0.375rem;
    }
  }
}
