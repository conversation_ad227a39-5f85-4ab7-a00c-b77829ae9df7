import Checkbox from '@components/common/Input/Checkbox';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import { Fragment, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { MenuData } from '@page/User/Role/type';

interface MenuAuthListProps {
  menuListData: MenuData[];
}

const MenuAuthList = ({ menuListData }: MenuAuthListProps) => {
  const { getValues, setValue, watch } = useFormContext();
  const menuIds = watch('menuIds');

  // 메뉴 체크박스 변경 처리 함수
  const handleMenuCheckboxChange = (parentMenuId: number, menu: MenuData) => {
    const allMenuIds = getAllMenuIds([menu]);
    const menuCheckedList = getValues('menuIds');
    if (menuCheckedList) {
      const updatedMenuCheckedList = allMenuIds.every((id) => menuCheckedList.includes(id))
        ? menuCheckedList.filter((id) => !allMenuIds.includes(id))
        : [...menuCheckedList, ...allMenuIds];
      setValue('menuIds', updatedMenuCheckedList, { shouldDirty: true });
    }
    updateParentMenusCheckboxStatus(parentMenuId);
  };

  // 모든 메뉴 ID를 재귀적으로 수집하는 함수
  const getAllMenuIds = (menus: MenuData[]): number[] => {
    return menus.reduce((acc: number[], menu) => {
      const ids = [menu.id];
      if (menu.childs && menu.childs.length > 0) {
        ids.push(...getAllMenuIds(menu.childs));
      }
      return [...acc, ...ids];
    }, []);
  };

  const getMenu = (menuId: number): MenuData | null => {
    const findMenuRecursive = (menus: MenuData[]): MenuData | null => {
      for (const menu of menus) {
        if (menu.id === menuId) {
          return menu;
        }
        if (menu.childs && menu.childs.length > 0) {
          const found = findMenuRecursive(menu.childs);
          if (found) {
            return found;
          }
        }
      }
      return null;
    };
    return findMenuRecursive(menuListData);
  }

  const updateParentMenusCheckboxStatus = (parentMenuId: number) => {
    // 상위 메뉴들의 체크박스 상태 업데이트
    if (parentMenuId) {
      let currentParentId = parentMenuId;
      while (currentParentId) {
        const parentMenu = getMenu(currentParentId);
        if (parentMenu) {
          const parentSubMenuIds = getAllMenuIds([parentMenu]).filter(id => id !== parentMenu.id);
          const updatedMenuCheckedList = getValues('menuIds');

          // 하위 메뉴가 모두 체크되어 있으면 상위 메뉴도 체크
          if (parentSubMenuIds.every(id => updatedMenuCheckedList.includes(id))) {
            setValue('menuIds', [...updatedMenuCheckedList, parentMenu.id], { shouldDirty: true });
          } else {
            // 하위 메뉴가 모두 체크되어 있지 않으면 상위 메뉴도 체크 해제
            setValue('menuIds', updatedMenuCheckedList.filter(id => id !== parentMenu.id), { shouldDirty: true });
          }
          // 다음 상위 메뉴로 이동
          const nextParent = menuListData.find(m => m.childs?.some(child => child.id === currentParentId));
          currentParentId = nextParent?.id;
        } else {
          break;
        }
      }
    }
  }

  // 전체 메뉴 체크박스 변경 처리 함수
  const handleAllMenuCheckboxChange = () => {
    const allMenuIds = getAllMenuIds(menuListData);
    const currentCheckedList = getValues('menuIds');

    // 현재 체크된 항목이 전체 메뉴와 동일하면 전체 해제, 아니면 전체 선택
    if (currentCheckedList?.length === allMenuIds.length) {
      setValue('menuIds', [], { shouldDirty: true });
    } else {
      setValue('menuIds', allMenuIds, { shouldDirty: true });
    }
  };

  // 메뉴 목록 태그 재귀적으로 렌더링
  const renderMenuList = (menuId: number, menuList: MenuData[], depth: number = 1) => {
    return menuList.map((menu: MenuData, idx) => (
      <Fragment key={menu.id}>
        <tr>
          {menu.depth === 1 && (
            <th>
              <Checkbox
                value={menu.id}
                checked={menuIds?.includes(menu.id) ?? false}
                onChange={() => handleMenuCheckboxChange(menuId, menu)}
                label={`${menu.name} 체크박스`}
              />
            </th>
          )}
          {menu.depth !== 1 && (
            <td>
              <Checkbox
                value={menu.id}
                checked={menuIds?.includes(menu.id) ?? false}
                onChange={() => handleMenuCheckboxChange(menuId, menu)}
                label={`${menu.name} 체크박스`}
              />
            </td>
          )}
          {menu.depth !== 1 ? (
            <td style={{ paddingLeft: `${depth * 5}px` }}>
              {menu.depth !== 2 && menuList.length - idx !== 1 && '├─ '}
              {menu.depth !== 2 && menuList.length - idx === 1 && '└─ '}
              {menu.name}
            </td>
          ) : (
            <th colSpan={2}>{menu.name}</th>
          )}
        </tr>
        {menu.childs && menu.childs.length > 0 && renderMenuList(menu.id, menu.childs, depth + 1)}
      </Fragment>
    ));
  };

  useEffect(() => { }, [menuIds]);

  return (
    <TableContainer>
      <colgroup>
        <col style={{ width: '5%' }} />
        <col style={{ width: '95%' }} />
      </colgroup>
      <TableHeader sticky>
        <tr>
          <th>
            <Checkbox
              value="전체 선택"
              checked={menuIds?.length === getAllMenuIds(menuListData).length}
              onChange={handleAllMenuCheckboxChange}
              hasLabel={false}
              label="전체 선택"
            />
          </th>
          <th>
            메뉴 리스트
          </th>
        </tr>
      </TableHeader>
      <TableBody>
        {menuListData ? (
          renderMenuList(0, menuListData)
        ) : (
          <tr className="no_result">
            <td colSpan={2}>조회된 데이터가 없습니다.</td>
          </tr>
        )}
      </TableBody>
    </TableContainer>
  );
};

export default MenuAuthList;
