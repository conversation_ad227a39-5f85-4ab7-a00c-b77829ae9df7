import React, { useState } from 'react';
import TestTable from '../TestTable';
import Input from '@components/common/Input/Input';

const TestTextField = () => {
  const [inputVal, setInputVal] = useState('');

  const handleInputVal = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputVal(e.target.value);
  };
  
  return (
    <TestTable
      compName="input_text"
      headChild={
        <>
          <tr>
            <th colSpan={3}>Text Type</th>
          </tr>
          <tr>
            <th>Status</th>
            <th>Text Align - Left (Default)</th>
            <th>Text Align - Right</th>
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr className="p_default">
            <th>Default</th>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} />
            </td>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} align="right" />
            </td>
          </tr>
          <tr className="p_disabled">
            <th>Disabled</th>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} disabled />
            </td>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} align="right" disabled />
            </td>
          </tr>
          <tr className="p_readonly">
            <th>Readonly</th>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} readOnly />
            </td>
            <td>
              <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} align="right" readOnly />
            </td>
          </tr>
          <tr className="p_error">
            <th>Error</th>
            <td>
              <div className="c_input_wrapper error">
                <Input placeholder="Placeholder" value={inputVal} onChange={handleInputVal} design="error" />
              </div>
            </td>
            <td>
              <div className="c_input_wrapper error">
                <Input
                  placeholder="Placeholder"
                  align="right"
                  design="error"
                  value={inputVal}
                  onChange={handleInputVal}
                />
              </div>
            </td>
          </tr>
          <tr className="p_warning">
            <th>Warning</th>
            <td>
              <div className="c_input_wrapper warning">
                <Input placeholder="Placeholder" design="warning" value={inputVal} onChange={handleInputVal} />
              </div>
            </td>
            <td>
              <div className="c_input_wrapper warning">
                <Input
                  placeholder="Placeholder"
                  align="right"
                  design="warning"
                  value={inputVal}
                  onChange={handleInputVal}
                />
              </div>
            </td>
          </tr>
          <tr className="p_confirm">
            <th>confirm</th>
            <td>
              <div className="c_input_wrapper confirm">
                <Input placeholder="Placeholder" design="confirm" value={inputVal} onChange={handleInputVal} />
              </div>
            </td>
            <td>
              <div className="c_input_wrapper confirm">
                <Input
                  placeholder="Placeholder"
                  align="right"
                  design="confirm"
                  value={inputVal}
                  onChange={handleInputVal}
                />
              </div>
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestTextField;
