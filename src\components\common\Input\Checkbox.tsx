import ErrorMsg from '@components/common/Error/ErrorMsg';
import joinClassName from '@utils/joinClassName';
import { forwardRef, InputHTMLAttributes } from 'react';

interface CheckboxProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  value: string | number;
  hasLabel?: boolean;
  className?: string;
  error?: string;
}
// label option 추가
const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      label,
      value,
      hasLabel = false,
      className, // 추가 클래스 요소
      error,
      ...attributes
    },
    ref
  ) => {
    const checkboxStyleClass = joinClassName('c_checkbox', className);

    return (
      <>
        <label className={checkboxStyleClass}>
          <input type="checkbox" value={value} {...attributes} />
          <span className={hasLabel ? '' : 'sr-only'}>{label}</span>
        </label>
        <ErrorMsg text={error} />
      </>
    );
  }
);

export default Checkbox;
