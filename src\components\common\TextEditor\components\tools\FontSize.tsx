import SelectBox, { OptionType } from '@components/common/SelectBox/SelectBox';
import { EditorConstant } from '@components/common/TextEditor/utils/constant';
import { useCurrentEditor } from '@tiptap/react';

const FontSize = ({ defaultFontSize }: { defaultFontSize: number }) => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const { makeFontSizeOptions } = EditorConstant;
  const FONT_SIZES = makeFontSizeOptions(defaultFontSize, 30);

  const getCurrentFontSize = () => {
    const attrs = editor.getAttributes('textStyle');
    return FONT_SIZES.find((size) => size.value === attrs.fontSize) || { label: 'Default', value: 'default' }; // 기본값 16px
  };

  const handleFontSizeChange = (selectedOption: OptionType) => {
    if (editor.isActive('heading')) return;
    if (selectedOption.value === 'default') {
      editor.chain().focus().unsetFontSize().run();
    } else {
      editor
        .chain()
        .focus()
        .setFontSize(selectedOption.value as string)
        .run();
    }
  };

  return (
    <div className="item_box fontsize">
      <label>크기: &nbsp;</label>
      <SelectBox
        options={FONT_SIZES}
        optionListClassName="fontsize_select_list"
        defaultValue={getCurrentFontSize()}
        selectedValue={getCurrentFontSize()}
        setSelectedValue={handleFontSizeChange}
      />
    </div>
  );
};

export default FontSize;
