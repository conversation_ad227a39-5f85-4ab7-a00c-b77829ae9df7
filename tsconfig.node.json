/**
 tsconfig.json 파일을 확장하거나 재정의하여 Node.js 환경에 맞게 컴파일 옵션을 설정하는 파일
  tsconfig.json 에서 정의된 항목을 재정의하는경우 값이 일치해야한다.
 */

{
  "compilerOptions": {
    "composite": true, //TypeScript가 종속성을 지능적으로 관리하고 증분 빌드를 효과적으로 수행할 수 있도록 하여 빌드 시간을 최적화하는 데 도움
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "strict": true
  },
  "include": ["vite.config.ts"]
}
