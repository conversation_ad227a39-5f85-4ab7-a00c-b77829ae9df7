import Button from '@components/common/Button/Button';
import { useNavigate } from 'react-router-dom';

const Error = ({ authError = false }: { authError?: boolean }) => {
  const navigate = useNavigate();

  return (
    <div className="c_error_page">
      <div className="error_content">
        <figure className="error_image" />
        <h1>{authError ? '403 Forbidden' : '404 Not Found'}</h1>
        <p>{authError ? '권한이 없는 페이지입니다.' : '존재하지 않는 페이지입니다.'}</p>
        {!authError && <p>주소를 확인해주세요.</p>}
        <p>문제가 지속되면 관리자에게 문의해주세요.</p>
        <div className="button_wrapper">
          <Button text="이전페이지로" onClick={() => navigate(-1)} />
          <Button text="로그인페이지로" onClick={() => navigate('/login')} />
        </div>
      </div>
    </div>
  );
};

export default Error;
