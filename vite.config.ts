import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import envCompatiblePlugin from 'vite-plugin-env-compatible';
import tsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  return {
    plugins: [react(), envCompatiblePlugin(), tsconfigPaths()],

    //define : 전역상수를 정의하는 속성
    define: {
      _: 'window._',
    },
    esbuild: {
      drop: ['console'], // 빌드시 condole.log 삭제
    },
    resolve: {
      alias: {
        '@api': path.resolve(__dirname, './src/api'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@components': path.resolve(__dirname, './src/components'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@page': path.resolve(__dirname, './src/page'),
        '@styles': path.resolve(__dirname, './src/assets/styles'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@types': path.resolve(__dirname, './src/types'),
        '@constants': path.resolve(__dirname, './src/constants'),
        '@store': path.resolve(__dirname, './src/store'),
        '@route': path.resolve(__dirname, './src/route'),
      },
    },
    server: {
      host: true,
      allowedHosts: true,
      proxy: {
        '/api': {
          target: env.VITE_API_ROOT, // 사용할 요청 도메인을 설정
          changeOrigin: true,
        },
        '/api-admin': {
          target: env.VITE_API_ROOT, // 사용할 요청 도메인을 설정
          changeOrigin: true,
        },
        '/auth': {
          target: env.VITE_AUTH_ROOT, // 사용할 요청 도메인을 설정
          changeOrigin: true,
        },
      },
    },
  };
});
