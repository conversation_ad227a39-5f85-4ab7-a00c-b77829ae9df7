import joinClassName from '@utils/joinClassName';
import React, { TableHTMLAttributes } from 'react';

interface TableSectionProps extends TableHTMLAttributes<HTMLTableSectionElement> {
  sticky?: boolean;
}

const TableHeader = ({ children, sticky, ...attributes }: TableSectionProps) => {
  const stickyClass = sticky ? 'sticky' : '';
  return (
    <thead className={joinClassName('c_table_header', stickyClass)} {...attributes}>
      {children}
    </thead>
  );
};

export default TableHeader;
