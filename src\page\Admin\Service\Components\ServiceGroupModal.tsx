import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormTextarea from '@components/common/Form/FormTextarea';
import DefaultModal from '@components/common/Modal/DefaultModal';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { BodyRowProps } from '@page/Admin/Account/type';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAdminServiceGroupAPI } from '@api/admin/serviceGroupAPI';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';
import { ServiceGroupData } from '@page/Admin/Service/type';
import FormCheckbox from '@components/common/Form/FormCheckbox.tsx';
import { useAlertStore } from '@store/useAlertStore';
import useService from '@page/Admin/Service/hooks/useService';
import Tooltip from '@components/common/Tooltip/Tooltip';
import TestImg from '@assets/images/android-chrome-192x192.png';
import TableBodyRow from '@components/common/Table/TableBodyRow';

const BodyRow = ({ title = '', required, children }: BodyRowProps) => {
  return (
    <tr>
      <th>
        <span className={required ? 'required' : ''}>{title}</span>
      </th>
      <td colSpan={3}>{children}</td>
    </tr>
  );
};

const ServiceGroupModal = () => {
  // hook
  const methods = useForm({
    defaultValues: {
      name: '',
      groupDesc: '',
      defaultYn: '',
    },
  });
  const { reset } = methods;
  const { serviceGroupDetail, modalType, isOpenServiceGroupModal, setIsOpenServiceGroupModal, setServiceGroupDetail } =
    useServiceStore();
  const { addServiceGroup, updateServiceGroup } = useAdminServiceGroupAPI();
  const { activeAlert } = useAlertStore();
  const { handleGetServiceGroupList } = useService();

  const handleOnSubmit = (data: ServiceGroupData) => {
    handleServiceGroup(data);
  };

  // API functions
  const handleServiceGroup = async (serviceGrpData: ServiceGroupData) => {
    if (modalType === 'add') {
      const response = await addServiceGroup({ data: serviceGrpData });
      if (response) {
        setIsOpenServiceGroupModal(false); // 모달 닫기

        // 서비스 그룹 목록 재조회
        handleGetServiceGroupList();

        // 서비스 그룹 상세 정보 설정
        if ('data' in response) {
          setServiceGroupDetail(response.data);
        }

        // 알림 표시
        activeAlert(response.message);
      }
    } else {
      if (serviceGroupDetail) {
        const responseMsg = await updateServiceGroup({ id: serviceGroupDetail.id, data: serviceGrpData });
        if (responseMsg) {
          setIsOpenServiceGroupModal(false);
          handleGetServiceGroupList();
          activeAlert(responseMsg);
        }
      }
    }
  };

  useEffect(() => {
    if (modalType === 'edit') {
      serviceGroupDetail &&
        reset({
          name: serviceGroupDetail.name,
          groupDesc: serviceGroupDetail.groupDesc,
          defaultYn: serviceGroupDetail.defaultYn,
        });
    } else {
      reset({
        name: '',
        groupDesc: '',
        defaultYn: 'N',
      });
    }
  }, [modalType, serviceGroupDetail]);

  return (
    <DefaultModal
      className="service_group_confirm_modal"
      title={`서비스 그룹 ${modalType === 'add' ? '등록' : '수정'}`}
      isOpenModal={isOpenServiceGroupModal}
      setIsOpenModal={setIsOpenServiceGroupModal}
      footer={false}
    >
      <Form methods={methods} onSubmit={handleOnSubmit}>
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '서비스 그룹명',
                required: true,
                contents: (
                  <FormInput
                    wrapperClassName="full"
                    name="name"
                    placeholder="서비스 그룹명 입력"
                    rules={{
                      required: '필수값입니다.',
                    }}
                  />
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '서비스 그룹 설명',
                contents: <FormTextarea name="groupDesc" placeholder="서비스 그룹 설명 입력" className="group_desc" />,
              }}
            />
            <TableBodyRow
              rowData={{
                title: (
                  <div className="th_service_group_manage">
                    시스템 기본 서비스 관리 그룹
                    <span style={{ marginLeft: 5 }}></span>
                    <Tooltip
                      id="tooltip-1"
                      render={() => (
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            textAlign: 'center',
                          }}
                        >
                          <img
                            src={TestImg}
                            alt="test-img"
                            style={{ width: '100px', height: 'auto', marginBottom: '10px' }}
                          />
                          <p>체크박스를 클릭해주세요.</p>
                        </div>
                      )}
                      place="top-start"
                    />
                    {/* <button style={{ padding: '10px 20px', fontSize: '16px' }}>마우스를 올려보세요</button>
              </Tooltip> */}
                  </div>
                ),
                contents: (
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <FormCheckbox name="defaultYn" label="&nbsp;" />
                    <Tooltip id="tooltip-2" content="jsx에도 툴팁을 적용 할 수 있습니다.">
                      <span style={{ padding: '10px 20px', fontSize: '16px' }}>
                        마우스를 올려보세요( tooltip-test )
                      </span>
                    </Tooltip>
                  </div>
                ),
              }}
            />
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button
            text="취소"
            color="grayscale"
            onClick={() => {
              setIsOpenServiceGroupModal(false);
            }}
          />
          <Button type="submit" text="확인" onClick={() => {}} />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default ServiceGroupModal;
