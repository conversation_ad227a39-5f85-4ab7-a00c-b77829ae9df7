@use '@styles/utils/mixin' as m;

.manage_log {
  .manage_log_control_box {
    @include m.flex(center, space-between, row);
    width: 100%;

    .manage_log_search {
      @include m.flex(center, center);
      gap: 1rem;
    }
  }

  .manage_log_content {
    .manage_log_content_control {
      @include m.flex(center, space-between, row);
      margin-bottom: 1rem;

      .manage_log_content_control_data_size {
        @include m.flex(center, center);
        gap: 1rem;

        @include m.bp_large() {
          font-size: 0.875rem;
          gap: 0.875rem;
        }

        @include m.bp_medium() {
          font-size: 0.75rem;
          gap: 0.75rem;
        }
      }
    }

    .manage_log_content_item {
      position: relative;
      height: calc(100vh - 360px);
      margin-bottom: 1rem;

      @include m.bp_large() {
        height: calc(100vh - 330px);
      }

      @include m.bp_medium() {
        height: calc(100vh - 300px);
      }
    }
  }

  // grid 헤더
  .ag-header-cell-label {
    justify-content: center;
  }
}

.manage_log_excel {
  .c_modal_body {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;

    ul {
      margin-left: 1rem;
      text-align: left;
      list-style-type: disc;
    }
  }
}
