import IconButton from '@components/common/Button/IconButton';
import { AdminPostDetailType } from '@page/Admin/Post/type';
import { downloadFile } from '@utils/fileDownload';

interface Props {
  data: AdminPostDetailType['attchFiles'];
}

const imgAttachment = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg', 'tif', 'tiff'];

const Attachment = ({ data }: Props) => {
  return (
    <div className="admin_post_contents_attachment">
      {data &&
        data.map((attachment, idx) => (
          <IconButton
            key={`attachment-${idx}`}
            icon={imgAttachment.includes(attachment.extension) ? 'image' : 'link'}
            design="capsule"
            text={attachment.originName}
            onClick={() => {
              downloadFile({
                fileId: attachment.id,
                imgDownload: true,
                downloadName: attachment.originName,
              });
            }}
          />
        ))}
    </div>
  );
};

export default Attachment;
