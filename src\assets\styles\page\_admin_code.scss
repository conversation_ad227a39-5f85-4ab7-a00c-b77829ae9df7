@use '@styles/utils/mixin' as m;

.manage_admin_code {
  .c_table_container_header {
    margin-bottom: 0;
  }

  .c_control_box,
  .c_form {
    width: 100%;
  }

  .c_table_wrapper {
    overflow-x: auto;
    td {
      .c_select,
      .c_input {
        width: 200px;
      }
      > span {
        max-width: 160px;
      }
    }
  }

  .group_code_detail_btn_wrapper,
  .code_list_btn_wrapper {
    @include m.flex(center, right);
    gap: 0.25rem;
  }

  .c_list_table_contents {
    > div {
      @include m.flex(center, space-between);
    }
  }

  .admin_code_detail_info_wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 0.625rem;

    .no_result {
      @include m.flex(center, center);
      width: 100%;
      height: calc(100vh - 320px);
      border-radius: 0.5rem;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--g_06);

      @include m.bp_large() {
        height: calc(100vh - 290px);
      }

      @include m.bp_medium() {
        height: calc(100vh - 260px);
      }
    }
  }

  .code_list {
    width: 100%;
    height: calc(100vh - 470px);
    margin-bottom: 0.625rem;
    .ag-theme-quartz {
      .ag-header-cell-label {
        justify-content: center;
      }
    }

    @include m.bp_large() {
      height: calc(100vh - 440px);
    }

    @include m.bp_medium() {
      height: calc(100vh - 413px);
    }
  }
}

.code_modal {
  width: 400px;

  .c_modal_body {
    .c_table_wrapper {
      .c_table {
        .c_table_body {
          tr {
            td {
              text-align: left;

              .c_select,
              .c_input_wrapper {
                width: 100%;
                .c_input {
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }

  .button_wrapper {
    @include m.flex(center, right);
    gap: 0.25rem;
    margin-top: 1.25rem;
  }
}
