export interface DataData {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

export interface AuthData {
  id: number;
  name: string;
  dateInfo: DataData;
}

export interface MenuData {
  id: number;
  name: string;
  desc: string;
  url: string;
  depth: number;
  sortNo: number;
  useYn: string;
  childs: Array<MenuData | null>;
  dateInfo: DataData;
}

export interface ParsedServiceData {
  id: number;
  name: string;
  depth: number;
  childs?: ParsedServiceData[];
  serviceGroupId: number;
  serviceGroupName: string;
  serviceDesc: string;
}

export interface SearchFormValues {
  name: string | null;
}

export interface DetailFormValues {
  name: string;
  menuIds: number[];
  serviceIds: number[];
}
