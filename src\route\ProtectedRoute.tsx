import { useEffect, useState } from 'react';
import { Outlet, useLocation, useMatches, useNavigate } from 'react-router-dom';
import { useLayoutStore } from '@store/useLayoutStore';
import { useMenuStore } from '@store/useMenuStore';
import Header from '@components/layout/Header';
import Main from '@components/layout/Main';
import Footer from '@components/layout/Footer';
import Loading from '@components/common/Loading';
import { getMyMenus } from '@api/admin/menuManageApi';
import Error from '@page/Error';
import { Menu } from 'types/adminMenuType';
import { useLogger } from '@components/common/Log/hooks/useLogger';
import { useLogStore } from '@store/useLogStore';
import { createTraceId } from '@components/common/Log/utils/createTraceId';
import { getFrontInfo } from '@components/common/Log/utils/getFrontInfo';
import { useLoadingStore } from '@store/useLoadingStore';
import { match } from 'path-to-regexp';

const ProtectedRoute = () => {
  // hooks
  const location = useLocation();
  const navigate = useNavigate();
  const { dynamicMenuList } = useMenuStore();
  const { isLoading } = useLoadingStore();

  // state
  const { lnbState } = useLayoutStore();

  // 스크린 로그 관련
  const { screenLog } = useLogger();
  const { setLogParams } = useLogStore();
  const matches = useMatches();
  const lastMatch = matches[matches.length - 1];
  const path = lastMatch?.pathname;

  /**
   * 스크린 로그
   */

  const findMenuByUrl = (menuList: any[], currentUrl: string) => {
    for (const menu of menuList) {
      const matcher = match(menu.path, { decode: decodeURIComponent, end: true });
      const matched = matcher(currentUrl);
      if (matched) {
        return menu.element.props;
      }
    }
    return null;
  };

  const handlePageLogging = (usingMenuList: Menu[]) => {
    // menulist가 비어있지 않을 때에만
    if (usingMenuList.length > 0) {
      const currentMenu = findMenuByUrl(usingMenuList, path);
      // console.log("currentPageName", currentMenu?.pageName);
      // 페이지 접근 로그 api 호출
      if (currentMenu) {
        setLogParams((prev) => ({
          ...prev,
          menuName: currentMenu.pageName,
          buttonSection: '',
          button: '',
          logType: 'screen',
          globalTraceId: createTraceId(),
          ...getFrontInfo(),
        }));
        screenLog();
      }
    }
  };

  // 페이지 이동 시 스크린 로그 호출
  useEffect(() => {
    if (!path.endsWith('/')) {
      handlePageLogging(dynamicMenuList);
    }
  }, [path, dynamicMenuList]);

  // 정상 라우팅 처리
  return (
    <div data-is-open-lnb={lnbState}>
      <Header />
      <Main>
        <Outlet />
      </Main>
      <Footer />
    </div>
  );
};

export default ProtectedRoute;
