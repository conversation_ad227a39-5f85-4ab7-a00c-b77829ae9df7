import IconButton from '@components/common/Button/IconButton';
import { useCurrentEditor } from '@tiptap/react';

const EditorAlign = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const alignInit = () => {
    editor.chain().focus().unsetTextAlign().run();
  };

  const alignLeft = () => {
    editor.chain().focus().setTextAlign('left').run();
  };

  const alignCenter = () => {
    editor.chain().focus().setTextAlign('center').run();
  };

  const alignRight = () => {
    editor.chain().focus().setTextAlign('right').run();
  };

  return (
    <div className="item_box align">
      <IconButton
        text="정렬 초기화"
        icon="align_init"
        iconOnly
        fill={'unfilled'}
        color={'grayscale'}
        size='smallest'
        onClick={alignInit}
        disabled={editor.isActive('codeBlock')}
      />
      <IconButton
        text="왼쪽 정렬"
        icon="align_left"
        iconOnly
        fill={editor.isActive({ textAlign: 'left' }) ? 'filled' : 'unfilled'}
        color={editor.isActive({ textAlign: 'left' }) ? 'primary' : 'grayscale'}
        size='smallest'
        onClick={alignLeft}
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('heading') ||
          editor.isActive('listItem') ||
          editor.isActive('taskItem')
          }
      />
      <IconButton
        text="가운데 정렬"
        icon="align_center"
        iconOnly
        fill={editor.isActive({ textAlign: 'center' }) ? 'filled' : 'unfilled'}
        color={editor.isActive({ textAlign: 'center' }) ? 'primary' : 'grayscale'}
        size='smallest'
        onClick={alignCenter}
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('heading') ||
          editor.isActive('listItem') ||
          editor.isActive('taskItem')
        }
      />
      <IconButton
        text="오른쪽 정렬"
        icon="align_right"
        iconOnly
        fill={editor.isActive({ textAlign: 'right' }) ? 'filled' : 'unfilled'}
        color={editor.isActive({ textAlign: 'right' }) ? 'primary' : 'grayscale'}
        size='smallest'
        onClick={alignRight}
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('heading') ||
          editor.isActive('listItem') ||
          editor.isActive('taskItem')
        }
      />
    </div>
  );
};

export default EditorAlign;
