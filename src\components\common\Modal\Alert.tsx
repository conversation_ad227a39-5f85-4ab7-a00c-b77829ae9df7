import { useState, useRef, Fragment, useEffect } from 'react';
import joinClassName from '@utils/joinClassName';
import { useAlertStore } from '@store/useAlertStore';
import ModalPortal from '@components/common/Modal/ModalPortal';
import IconButton from '@components/common/Button/IconButton';
import Button from '@components/common/Button/Button';
import { useLoadingStore } from '@store/useLoadingStore';
interface AlertProps {
  isOpenAlert: boolean;
  title?: string;
  children: string | React.ReactNode;
  confirmText?: string;
  onConfirm: (props?: any) => void;
  onClickedDim?: (props?: any) => void; // dim 클릭했을 때 실행 함수
  transparentDim?: boolean; // dim이 투명한 경우
  className?: string;
}

const Alert = ({
  isOpenAlert, // alert의 open/close 여부를 관리하는 상태값(*)
  title = '알림', // 제목
  children, // 내부 text(*)
  confirmText = '확인', // button text
  onConfirm, // 버튼 클릭 시 실행 함수 (*)
  onClickedDim, // dim 클릭 시 실행 함수 (기본동작: 이벤트 없음)
  transparentDim, // dim 투명 설정(true: 투명 dim / false(기본): 불투명 black dim)
  className, // 추가 클래스 요소
}: AlertProps) => {
  const alertClass = joinClassName('c_modal', 'alert', className);
  const { initAlertState } = useAlertStore();
  const { isLoading } = useLoadingStore();


  const alertRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({
    top: null,
    left: null,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (alertRef.current) {
      if (!position.top || !position.left || isOpenAlert) {
        const modalRef = alertRef.current;
        const { innerWidth, innerHeight } = window;
        const center = {
          x: innerWidth * 0.5 - modalRef.offsetWidth * 0.5,
          y: innerHeight * 0.5 - modalRef.offsetHeight * 0.5,
        };
        setPosition({ top: `${center.y}px`, left: `${center.x}px` });
      }
    }
  }, [isOpenAlert]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target instanceof Element && e.target.closest('.c_modal_header')) {
      setIsDragging(true);

      const rect = alertRef.current.getBoundingClientRect();
      const offsetX = e.clientX - rect.left;
      const offsetY = e.clientY - rect.top;

      setDragOffset({ x: offsetX, y: offsetY });

      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      const left = e.clientX - dragOffset.x;
      const top = e.clientY - dragOffset.y;

      setPosition({
        top: `${top}px`,
        left: `${left}px`,
      });

      e.preventDefault();
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      setIsDragging(false);
      e.preventDefault();
    }
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging]);

  const parseChildren = () => {
    if (typeof children === 'string') {
      return children.split(/\n|<br\s*\/?>|<br>/).map((line, idx) => (
        <Fragment key={idx}>
          {line}
          {idx < children.split(/\n|<br\s*\/?>|<br>/).length - 1 && <br />}
        </Fragment>
      ));
    }
    return children;
  };

  return (
    <>
      {isOpenAlert && (
        <ModalPortal onClickedDim={onClickedDim} transparentDim={transparentDim}>
          <div
            className={alertClass}
            style={position}
            ref={alertRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <div className="c_modal_header">
              <h2 className="c_modal_title">{title}</h2>
              <IconButton
                iconOnly
                text="닫기"
                icon="close"
                color="grayscale"
                fill="unfilled"
                size="smallest"
                design="circle"
                onClick={initAlertState}
              />
            </div>
            <div className="c_modal_body">{parseChildren()}</div>
            <div className="c_modal_footer">
              <Button
                text={confirmText}
                onClick={() => {
                  onConfirm();
                }}
                disabled={isLoading}
              />
            </div>
          </div>
        </ModalPortal>
      )}
    </>
  );
};

export default Alert;
