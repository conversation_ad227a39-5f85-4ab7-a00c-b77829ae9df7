@use '@styles/utils/mixin' as m;

.admin_post {
  &_contents {
    &_control {
      @include m.flex(center, space-between, row);
      margin-bottom: 0.5rem;

      &_cnt {
        @include m.flex(center, center, row);
        gap: 1rem;
      }

      &_btn {
        @include m.flex(center, center, row);
        gap: 0.25rem;
      }
    }
  }

  &_detail {
    &_wrapper {
      @include m.flex(center, center, column);
      gap: 1rem;

      .row_content {
        @include m.ellipsis;

        .admin_post_contents_attachment {
          @include m.flex(start, start, column);

          gap: 0.25rem;

          button {
            span {
              font-weight: normal;
            }
          }
        }
      }

      & .admin_post_filebox {
        table {
          thead {
            tr {
              th {
                span {
                  font-size: inherit;
                }
              }
            }
          }

          tbody {
            tr {
              td {
                span {
                  font-size: inherit;
                }
              }
            }
          }
        }
      }
    }

    &_contents {
      background-color: var(--modal_bg);
      height: calc(100vh - 375px);

      @include m.bp_medium() {
        font-size: 0.75rem;
        line-height: 0.875rem;
        height: calc(100vh - 350px);
      }

      .c_editor_toolbar {
        .item_box {
          button {
            width: 1.5rem;
            height: 1.5rem;
          }
        }
      }
    }

    &_btns {
      @include m.flex(center, space-between, row);

      width: 100%;

      &_right {
        @include m.flex(center, center, row);
        gap: 0.25rem;
      }
    }
  }
}
