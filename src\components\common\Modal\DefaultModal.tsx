import { ReactNode, useEffect, useRef, useState } from 'react';
import IconButton from '@components/common/Button/IconButton';
import ModalPortal from './ModalPortal';

interface DefaultModalProps {
  className: string;
  isOpenModal: boolean;
  setIsOpenModal: (isOpenModal: boolean) => void;

  title?: string;
  children?: string | ReactNode;
  footer?: string | ReactNode;

  onClickedDim?: (props?: any) => void;
  transparentDim?: boolean;
}

const DefaultModal = ({
  className, // 모달 디자인 클래스 (*)
  isOpenModal, // 모달의 open/close 여부를 관리하는 상태값 (*)
  setIsOpenModal, // 모달의 open/close 여부를 관리하는 상태를 조절하는 함수 (*)

  title, // 타이틀 text
  children = <></>, // body 영역에 들어갈 text 혹은 html
  footer = <></>, // footer 영역에 들어갈 text 혹은 html

  onClickedDim, // dim 클릭 시 실행 함수 (기본동작: 이벤트 없음)
  transparentDim, // dim 투명 설정(true: 투명 dim / false(기본): 불투명 black dim)
}: DefaultModalProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({
    top: null,
    left: null,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (ref.current) {
      if (!position.top || !position.left || isOpenModal) {
        setModalPositionCenter(ref.current);
      }
    }
  }, [isOpenModal]);

  const setModalPositionCenter = (modalRef: HTMLDivElement) => {
    const { innerWidth, innerHeight } = window;
    const center = {
      x: innerWidth * 0.5 - modalRef.offsetWidth * 0.5,
      y: innerHeight * 0.5 - modalRef.offsetHeight * 0.5,
    };
    setPosition({ top: `${center.y}px`, left: `${center.x}px` });
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target instanceof Element && e.target.closest('.c_modal_header')) {
      setIsDragging(true);

      const rect = ref.current.getBoundingClientRect();
      const offsetX = e.clientX - rect.left;
      const offsetY = e.clientY - rect.top;

      setDragOffset({ x: offsetX, y: offsetY });

      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      const left = e.clientX - dragOffset.x;
      const top = e.clientY - dragOffset.y;

      setPosition({
        top: `${top}px`,
        left: `${left}px`,
      });

      e.preventDefault();
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      setIsDragging(false);
      e.preventDefault();
    }
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging]);

  return (
    <>
      {isOpenModal && (
        <ModalPortal onClickedDim={onClickedDim} transparentDim={transparentDim}>
          <div
            className={`c_modal ${className}`}
            ref={ref}
            style={position}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <div className="c_modal_header">
              {title && <h2 className="c_modal_title">{title}</h2>}
              <IconButton
                iconOnly
                text="닫기"
                icon="close"
                color="grayscale"
                fill="unfilled"
                size="smallest"
                design="circle"
                onClick={() => setIsOpenModal(false)}
              />
            </div>
            <div className="c_modal_body">{children}</div>
            {footer && <div className="c_modal_footer">{footer}</div>}
          </div>
        </ModalPortal>
      )}
    </>
  );
};

export default DefaultModal;
