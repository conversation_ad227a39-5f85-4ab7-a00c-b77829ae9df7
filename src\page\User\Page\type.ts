/**
 * 그룹 코드 데이터를 위한 인터페이스
 */
export interface GroupCodeDataProps {
  code: string; // 그룹 코드
  name: string; // 그룹 코드명
  isNew?: boolean; // 신규 여부
}

/**
 * 그룹 코드 상태를 위한 타입
 * null: 초기 상태
 * add: 추가 상태
 * edit: 수정 상태
 * delete: 삭제 상태
 */
export type Mode = undefined | 'create' | 'update';

/**
 * 그룹 코드 상태를 위한 타입
 * null: 초기 상태
 * add: 추가 상태
 * edit: 수정 상태
 * delete: 삭제 상태
 */
interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

export interface Page {
  id: string;
  pageName: string;
  pageTypeCode: string;
  pageFileName: string;
  pageDesc: string;
  bbsUseYn: string;
  roleUseYn: string;
  dateInfo: DateInfo;
  boardTempletId: number;
}

export interface SubPage {
  id: number;
  parentId: number;
  boardTempletId: number;
  pageName: string;
  pageFileName: string;
  pageTypeCode: string; // 예: 'P'
  pageDesc: string;
  roleUseYn: 'Y' | 'N';
  dateInfo: DateInfo;
}

/**
 * 그룹 코드 정보를 위한 타입
 */
export type GroupCodeType = {
  code: string; // 그룹 코드
  name: string; // 그룹 코드명
  // state: GroupCodeStateType; // 상태
};

/**
 * 그룹 코드 목록 컴포넌트 Props를 위한 인터페이스
 */
export interface GroupCodeListProps {
  groupCode: GroupCodeType; // 현재 선택된 그룹 코드
  groupCodeListData: GroupCodeDataProps[]; // 그룹 코드 목록 데이터
  setGroupCode: (groupCode: GroupCodeType) => void; // 그룹 코드 설정 함수
  setAlertContents: (alertContents: any) => void; // 알림 내용 설정 함수
  getGroupCodeList: () => void; // 그룹 코드 목록 조회 함수
  setGroupCodeDetailData: (groupCodeDetailData: any) => void; // 그룹 코드 상세 데이터 설정 함수
}
