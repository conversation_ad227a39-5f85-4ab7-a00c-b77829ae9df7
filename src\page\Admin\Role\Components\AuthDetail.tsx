import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import { useAuthStore } from '@page/Admin/Role/store/useAuthStore';

const AuthDetail = () => {
  const { authData, detailState } = useAuthStore();
  return (
    <div className="admin_authority_detail_info_wrapper">
      {!authData && detailState !== 'add' ? (
        <div className="no_result">
          권한이 선택되지 않았습니다. <br />
          조회할 권한을 선택하거나 추가해주세요.
        </div>
      ) : (
        <TableContainer className="admin_authority_detail_info">
          <TableBody>
            <TableBodyRow
              rowData={[
                {
                  title: '권한명',
                  required: true,
                  isFullWidth: true,
                  contents: (
                    <>
                      {detailState && (
                        <FormInput
                          wrapperClassName="full"
                          name="name"
                          placeholder="권한명을 입력하세요"
                          rules={{ required: '권한명은 필수값입니다.' }}
                        />
                      )}
                    </>
                  ),
                },
              ]}
            />
            {detailState !== 'add' && (
              <>
                <TableBodyRow
                  rowData={[
                    {
                      title: '등록자',
                      contents: authData?.dateInfo?.createUser,
                    },
                    {
                      title: '등록 일시',
                      contents: authData?.dateInfo?.createDate,
                    },
                  ]}
                />
                <TableBodyRow
                  rowData={[
                    {
                      title: '최종 수정자',
                      contents: authData?.dateInfo?.updateUser,
                    },
                    {
                      title: '최종 수정 일시',
                      contents: authData?.dateInfo?.updateDate,
                    },
                  ]}
                />
              </>
            )}
          </TableBody>
        </TableContainer>
      )}
    </div>
  );
};

export default AuthDetail;
