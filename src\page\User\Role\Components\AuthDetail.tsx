import FormInput from '@components/common/Form/FormInput';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { useAuthStore } from '@page/User/Role/store/useAuthStore';

const AuthDetail = () => {
  const { authData, detailState } = useAuthStore();
  return (
    <div className="admin_authority_detail_info_wrapper">
      <TableContainer className="admin_authority_detail_info">
        <colgroup>
          <col style={{ width: '15%' }} />
          <col style={{ width: '35%' }} />
          <col style={{ width: '15%' }} />
          <col style={{ width: '35%' }} />
        </colgroup>
        <TableBody>
          <tr>
            <th>
              <span className="required">권한명</span>
            </th>
            <td colSpan={3}>
              {detailState && (
                <FormInput
                  name="name"
                  placeholder="권한명을 입력하세요"
                  rules={{ required: '권한명은 필수값입니다.' }}
                />
              )}
            </td>
          </tr>
          {detailState !== 'add' && (
            <>
              <tr>
                <th>권한 ID</th>
                <td colSpan={3}>{authData?.id}</td>
              </tr>
              <tr>
                <th>등록자</th>
                <td>{authData?.dateInfo?.createUser}</td>
                <th>등록 일시</th>
                <td>{authData?.dateInfo?.createDate}</td>
              </tr>
              <tr>
                <th>최종 수정자</th>
                <td>{authData?.dateInfo?.updateUser}</td>
                <th>최종 수정 일시</th>
                <td>{authData?.dateInfo?.updateDate}</td>
              </tr>
            </>
          )}
        </TableBody>
      </TableContainer>
      {!authData && detailState !== 'add' && (
        <div className="no_result">
          권한이 선택되지 않았습니다. <br />
          조회할 권한을 선택하거나 추가해주세요.
        </div>
      )}
    </div>
  );
};

export default AuthDetail;
