import React, { useState } from 'react';
import { useCurrentEditor } from '@tiptap/react';
import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import DefaultModal from '@components/common/Modal/DefaultModal';
import { useForm } from 'react-hook-form';
import { useAlertStore } from '@store/useAlertStore';
import Alert from '@components/common/Modal/Alert';
import { useEditorAPI } from '@components/common/TextEditor/hooks/useEditorAPI';

interface ModalState {
  isOpen: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const EditorVideo = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return;

  const { uploadFile } = useEditorAPI();

  const methods = useForm({
    defaultValues: {
      url: '',
    },
  });

  const [tab, setTab] = useState<'file' | 'url'>('url');
  const { alertState, setAlertState, initAlertState } = useAlertStore();
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    onConfirm: () => {},
    onCancel: () => {
      setModalState((prev) => ({ ...prev, isOpen: false }));
    },
  });

  const openModal = () => {
    setModalState((prev) => ({ ...prev, isOpen: true }));
  };

  const closeModal = () => {
    setModalState((prev) => ({ ...prev, isOpen: false }));
  };

  const handleInsertVideo = (data: { url: string }) => {
    closeModal();
    if (tab === 'file') {
      const fileInput = document.createElement('input');
      fileInput.setAttribute('type', 'file');
      fileInput.setAttribute('accept', 'video/*');
      fileInput.setAttribute('max-size', '10485760'); // 10MB 제한
      fileInput.click();

      fileInput.onchange = async (event: Event) => {
        const files = fileInput.files;
        if (!files) return;

        try {
          const response = await uploadFile(files);
          const id = response.id;

          editor.commands.insertContent({
            type: 'video',
            attrs: {
              src: '/api/files/' + id,
              alt: files[0].name,
              title: files[0].name,
            },
          });
        } catch (error) {
          setAlertState({
            isOpen: true,
            content: '동영상 업로드에 실패했습니다.',
            onConfirm: initAlertState,
          });
        }
      };
    } else {
      if (data.url.includes('youtube.com') || data.url.includes('youtu.be')) {
        editor.commands.setYoutubeVideo({
          src: data.url,
        });
      } else {
        editor.commands.insertContent({
          type: 'video',
          attrs: {
            src: data.url,
            width: 480,
            height: 320,
            muted: true,
            playsinline: true,
            autoplay: true,
          },
        });
      }
    }
  };

  return (
    <>
      <IconButton
        text="video"
        icon="video"
        iconOnly
        fill="unfilled"
        size='smallest'
        color="grayscale"
        onClick={openModal}
        disabled={editor.isActive('codeBlock')}
      />
      <DefaultModal
        title="동영상 업로드"
        className="video_modal"
        isOpenModal={modalState.isOpen}
        setIsOpenModal={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
        onClickedDim={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
      >
        <div className="tab_list">
          <Button
            text="링크 임베드"
            onClick={() => setTab('url')}
            fill="unfilled"
            color={tab === 'url' ? 'primary' : 'grayscale'}
          />
          <Button
            text="파일 업로드"
            onClick={() => setTab('file')}
            fill="unfilled"
            color={tab === 'file' ? 'primary' : 'grayscale'}
          />
        </div>
        <div className="tab_panel">
          {tab === 'file' && (
            <>
              <p className="file_upload_text">
                동영상 파일을 업로드해주세요. <br />
                최대 10MB까지 업로드 가능합니다.
              </p>
              <div className="button_wrapper">
                <Button text="취소" color="grayscale" onClick={closeModal} />
                <Button text="파일 선택" onClick={() => handleInsertVideo({ url: '' })} />
              </div>
            </>
          )}
          {tab === 'url' && (
            <Form methods={methods} onSubmit={handleInsertVideo}>
              <FormInput
                type="url"
                name="url"
                placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ 형식으로 입력해주세요"
                rules={{ required: 'URL을 입력해주세요.' }}
              />
              <div className="button_wrapper">
                <Button text="취소" color="grayscale" onClick={closeModal} />
                <Button text="URL 업로드" type="submit" />
              </div>
            </Form>
          )}
        </div>
      </DefaultModal>
      <Alert isOpenAlert={alertState.isOpen} children={alertState.content} onConfirm={alertState.onConfirm} />
    </>
  );
};

export default EditorVideo;
