import { useEffect, useState } from 'react';
import { useAlertStore } from '@store/useAlertStore';
import { useThemeStore } from '@store/useThemeStore';
import dayjs from 'dayjs';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import { useConfirmStore } from '@store/useConfirmStore';
import { Outlet } from 'react-router-dom';

function App() {
  dayjs.locale('ko'); // dayjs 세팅
  const { themeState } = useThemeStore();
  const { alertState } = useAlertStore();
  const { confirmState } = useConfirmStore();

  useEffect(() => {
    const html = document.documentElement;
    html.setAttribute('theme', themeState);
  }, [themeState]);

  return (
    <>
      <Outlet />
      <Alert
        className={alertState.className}
        title={alertState.title}
        isOpenAlert={alertState.isOpen}
        children={alertState.content}
        onConfirm={alertState.onConfirm}
      />
      <Confirm
        className={confirmState.className}
        title={confirmState.title}
        isOpenConfirm={confirmState.isOpen}
        children={confirmState.content}
        onLeftButton={confirmState.onCancel}
        onRightButton={confirmState.onConfirm}
      />
    </>
  );
}

export default App;
