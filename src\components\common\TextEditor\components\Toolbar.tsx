import { useRef } from 'react';
import joinClassName from '@utils/joinClassName';
import { useCurrentEditor } from '@tiptap/react';
import FontStyle from '@components/common/TextEditor/components/tools/FontStyle';
import FontColor from '@components/common/TextEditor/components/tools/FontColor';
import EditorIndent from '@components/common/TextEditor/components/tools/EditorIndent';
import EditorBlock from '@components/common/TextEditor/components/tools/EditorBlock';
import Embeds from '@components/common/TextEditor/components/tools/Embeds';
import Heading from '@components/common/TextEditor/components/tools/Heading';
import EditorAlign from '@components/common/TextEditor/components/tools/EditorAlign';
import FontSize from '@components/common/TextEditor/components/tools/FontSize';
import FontFamily from '@components/common/TextEditor/components/tools/FontFamily';
import EditorList from '@components/common/TextEditor/components/tools/EditorList';

type ToolbarProps = {
  className?: string;
  defaultFontSize?: number;
};

const Toolbar = ({ className, defaultFontSize = 16 }: ToolbarProps) => {
  const { editor } = useCurrentEditor();
  if (!editor) return;

  const toolbarRef = useRef<HTMLDivElement>(null);

  return (
    <div className={joinClassName('c_editor_toolbar', className)} ref={toolbarRef}>
      <FontStyle />
      <FontColor toolbarRef={toolbarRef} />
      <EditorIndent />
      <EditorBlock />
      <Embeds />
      <FontSize defaultFontSize={defaultFontSize} />
      <FontFamily />
      <Heading />
      <EditorList />
      <EditorAlign />
    </div>
  );
};

export default Toolbar;
