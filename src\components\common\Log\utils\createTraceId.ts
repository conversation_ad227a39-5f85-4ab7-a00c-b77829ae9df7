import { dateToString } from "@utils/date";
import dayjs from "dayjs";

export const createTraceId = () => {
    const length = 32;
    const day = dateToString(dayjs(), "YYYYMMDD")

    let randomString = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    for ( let i = 0; i < length; i++ ) {
        randomString += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    
    const result = `g${day}${randomString}`;
    return result;
};