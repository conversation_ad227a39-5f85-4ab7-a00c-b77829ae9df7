:root {
  --font_default: #000;
  --font_white: #fff;
  --font_black: #000;
  --font_disabled: #5d5d61;

  --p_00: #eff5ff;
  --p_01: #d3e1fb;
  --p_02: #a7c4f7;
  --p_03: #7ca6f3;
  --p_04: #5089ef;
  --p_05: #246beb;
  --p_06: #1d56bc;
  --p_07: #16408d;
  --p_08: #0e2b5e;
  --p_09: #07152f;

  --s_00: #edf1f5;
  --s_01: #cdd7e4;
  --s_02: #b4c4d6;
  --s_03: #99b0cb;
  --s_04: #2a5c96;
  --s_05: #003675;
  --s_06: #002b5e;
  --s_07: #002046;
  --s_08: #00162f;
  --s_09: #000b17;

  --g_01: #f8f8f8;
  --g_02: #f0f0f0;
  --g_03: #e4e4e4;
  --g_04: #d8d8d8;
  --g_05: #c6c6c6;
  --g_06: #8e8e8e;
  --g_07: #717171;
  --g_08: #555555;
  --g_09: #2d2d2d;
  --g_10: #1d1d1d;

  --sub: #8b8b8e;
  --disabled: #5d5d61;

  --control_box: #fff;

  --table_header_bg: #fff;
  --table_cell_bg: #f8f8f8;

  --modal_bg: #fff;
  --bg: #efefef;
  --gnb_bg: #0e2b5e;
  --lnb_bg: #fefefe;
  --footer_bg: #d8d8d8;
  --scroll_bg: #6a6a6a;
  --scroll_hover_bg: #808080;
  --voc_card_bg: #fff;

  --red: #f32b0c;
  --error: #fda193;
  --warning: #ffcb7a;
  --confirm: #56d8aa;

  --shadow_l: 0 0 10px 4px rgba(#b9bcbb, 0.17);
  --shadow_s: 0 2px 8px 0 rgba(#000, 0.25);
}

[theme='dark'] {
  --font_default: #fff;

  --g_01: #1a1a1a;
  --g_02: #333333;
  --g_03: #4d4d4d;
  --g_04: #6a6a6a;
  --g_05: #808080;
  --g_06: #9f9f9f;
  --g_07: #b3b3b3;
  --g_08: #cdcecf;
  --g_09: #e6e6e6;
  --g_10: #f2f2f2;

  --control_box: #333333;

  --table_header_bg: #000;
  --table_cell_bg: #333333;

  --modal_bg: #212121;
  --sub: #8b8b8e;
  --disabled: #4f4d4a;
  --footer_bg: #333333;
  --voc_card_bg: #333333;

  --bg: #212121;
  --gnb_bg: #171717;
  --lnb_bg: #171717;
}

$p_00: #eff5ff;
$p_01: #d3e1fb;
$p_02: #a7c4f7;
$p_03: #7ca6f3;
$p_04: #5089ef;
$p_05: #246beb;
$p_06: #1d56bc;
$p_07: #16408d;
$p_08: #0e2b5e;
$p_09: #07152f;

$s_00: #edf1f5;
$s_01: #cdd7e4;
$s_02: #b4c4d6;
$s_03: #99b0cb;
$s_04: #2a5c96;
$s_05: #003675;
$s_06: #002b5e;
$s_07: #002046;
$s_08: #00162f;
$s_09: #000b17;

$red: #f32b0c;
$error: #fda193;
$warning: #ffcb7a;
$confirm: #56d8aa;

$shadow_l: 0 0 10px 4px rgba(#b9bcbb, 0.17);
$shadow_s: 0 2px 8px 0 rgba(#000, 0.25);
