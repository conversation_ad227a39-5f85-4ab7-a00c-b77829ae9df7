import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';

const ServiceGroupDetail = () => {
  const { serviceGroupDetail } = useServiceStore();

  return (
    <div className="service_group_detail_wrapper">
      <TableContainer className="service_group_detail">
        <TableBody>
          <TableBodyRow
            rowData={[
              {
                title: '서비스 그룹명',
                contents: serviceGroupDetail?.name,
              },
              {
                title: '시스템 기본 서비스 관리 그룹',
                contents: serviceGroupDetail?.defaultYn,
              },
            ]}
          />
          <TableBodyRow
            rowData={{
              title: '서비스 그룹 설명',
              isFullWidth: true,
              contents: serviceGroupDetail?.groupDesc,
            }}
          />
          <TableBodyRow
            rowData={[
              {
                title: '등록자',
                contents: serviceGroupDetail?.dateInfo?.createUser,
              },
              {
                title: '등록일자',
                contents: serviceGroupDetail?.dateInfo?.createDate,
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '수정자',
                contents: serviceGroupDetail?.dateInfo?.updateUser,
              },
              {
                title: '수정일자',
                contents: serviceGroupDetail?.dateInfo?.updateDate,
              },
            ]}
          />
        </TableBody>
      </TableContainer>
      {!serviceGroupDetail && (
        <div className="no_result">
          서비스 그룹이 선택되지 않았습니다. <br />
          조회할 서비스 그룹을 선택해주세요.
        </div>
      )}
    </div>
  );
};

export default ServiceGroupDetail;
