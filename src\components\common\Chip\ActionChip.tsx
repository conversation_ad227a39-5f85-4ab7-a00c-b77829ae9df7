import joinClassName from '@utils/joinClassName';
import { ActionChipProps } from './types';

const ActionChip = ({ label, size = 'medium', imgSrc, active = false, className, onClick }: ActionChipProps) => {
  const activeClass = active ? 'active' : '';
  const withImgClass = imgSrc ? 'with_img' : '';
  const chipClass = joinClassName('c_chip', 'c_chip_action', size, withImgClass, activeClass, className);

  return (
    <div className={chipClass} onClick={onClick}>
      {imgSrc && <img src={imgSrc} alt="profile" className="chip_img" />}
      <span>{label}</span>
    </div>
  );
};

export default ActionChip;
