import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import Pagination from '@components/common/Pagination';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import TableHeader from '@components/common/Table/TableHeader';
import TableRow from '@components/common/Table/TableRow';
import Title from '@components/common/Title/Title';
import { useConfirm } from '@hooks/useConfirm';
import { pagePerCountNo } from '@utils/pagePerCountNo';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useUserAccountListAPI } from './hooks/useUserAccountListAPI';
import { useUserAccountListStore } from './store/useUserAccountListStore';
import { ListFormValues, TActive } from './type';

// static
const activeType = [
  { label: '계정 활성 여부', value: '' },
  { label: '활성', value: 'Y' },
  { label: '비활성', value: 'N' },
];
const tableHeaderData = [
  'NO',
  '사용자ID',
  '사용자 이름',
  '계정 활성화',
  '등록자',
  '등록시간',
  '수정자',
  '수정시간',
  '수정',
  '삭제',
];

const UserAccountListPage = () => {
  const { currentPage, setCurrentPage, pageSize, userListData, paginationData } = useUserAccountListStore();
  const { alertState } = useAlertStore();
  const { confirmState } = useConfirmStore();
  const { openConfirm } = useConfirm();
  // 검색 폼 관리
  const searchMethods = useForm<ListFormValues>({
    defaultValues: {
      userId: '', // 사용자 아이디 검색어 초기값
      name: '', // 사용자 이름 검색어 초기값
      activeYn: null, // 활성 상태 코드 초기값
    },
  });
  const { getAccountList, deleteAccount, goAddAccount, goEditAccount } = useUserAccountListAPI({ searchMethods });

  useEffect(() => {
    getAccountList();
  }, [currentPage, pageSize]);

  // Function
  const parseActive = (code: TActive) => {
    return code === 'Y' ? '활성' : code === 'N' ? '비활성' : '상태없음';
  };

  const handleClickEdit = (itemId: number | string) => {
    openConfirm('수정하시겠습니까?', () => {
      goEditAccount(itemId);
    });
  };

  const handleClickDelete = (itemId: number | string) => {
    openConfirm(
      <>
        삭제한 계정은 복구할 수 없습니다. <br />
        정말 삭제 하시겠습니까?
      </>,
      () => {
        deleteAccount(itemId);
      }
    );
  };

  return (
    <>
      <Title />
      {/* 검색 폼 */}
      <Form onSubmit={getAccountList} methods={searchMethods}>
        <ControlBox>
          <FormSelectBox name="activeYn" options={activeType} placeholder="계정 활성 여부" />
          <FormInput name="userId" placeholder="사용자 아이디" />
          <FormInput name="name" placeholder="사용자 이름" />
          <Button type="submit" text="조회" />
        </ControlBox>
      </Form>
      <TableContainerHeader
        leftChildren={<p>총 {paginationData?.totalCount}건</p>}
        rightChildren={<Button text="추가" onClick={goAddAccount} />}
      />
      <TableContainer className="admin_account_list">
        {/* 
          admin_account_colgroup id값을 이용해 반응형 사이즈 업데이트
          추후 width값을 삭제후 scss로 관리
        */}
        <colgroup id="admin_account_list_col">
          <col id="admin_account_list_col_no" />
          <col id="admin_account_list_col_id" />
          <col id="admin_account_list_col_name" />
          <col id="admin_account_list_col_useYn" />
          <col id="admin_account_list_col_creator" />
          <col id="admin_account_list_col_createDate" />
          <col id="admin_account_list_col_editor" />
          <col id="admin_account_list_col_editDate" />
          <col id="admin_account_list_col_edit" />
          <col id="admin_account_list_col_delete" />
        </colgroup>
        <TableHeader sticky>
          <TableRow cellData={tableHeaderData} tag="th" />
        </TableHeader>
        <TableBody>
          {userListData &&
            userListData.map((item, idx) => (
              <tr key={idx}>
                <td>
                  {pagePerCountNo(
                    paginationData?.totalCount,
                    paginationData?.pageSize,
                    paginationData?.currentPage,
                    idx
                  )}
                </td>
                <td>{item.userId}</td>
                <td>{item.name}</td>
                <td>{parseActive(item.activeYn)}</td>
                <td>{item.dateInfo.createUser}</td>
                <td>{item.dateInfo.createDate}</td>
                <td>{item.dateInfo.updateUser}</td>
                <td>{item.dateInfo.updateDate}</td>
                <td>
                  <Button text="수정" onClick={() => handleClickEdit(item.id)} />
                </td>
                <td>
                  <Button text="삭제" color="red" onClick={() => handleClickDelete(item.id)} />
                </td>
              </tr>
            ))}
        </TableBody>
      </TableContainer>
      {paginationData && (
        <Pagination
          currentPage={currentPage}
          itemCountPerPage={pageSize}
          onPageChange={setCurrentPage}
          totalCount={paginationData.totalCount}
        />
      )}
      <Alert isOpenAlert={alertState.isOpen} children={alertState.content} onConfirm={alertState.onConfirm} />
      <Confirm
        isOpenConfirm={confirmState.isOpen}
        title="알림"
        children={confirmState.content}
        onLeftButton={confirmState.onCancel}
        onRightButton={confirmState.onConfirm}
        rightButtonText={confirmState.confirmType === 'delete' ? '삭제' : '확인'}
        isDelete={confirmState.confirmType === 'delete'}
      />
    </>
  );
};

export default UserAccountListPage;
