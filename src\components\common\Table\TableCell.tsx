import joinClassName from '@utils/joinClassName';
import React, { TableHTMLAttributes } from 'react';

interface TableCellProps extends TableHTMLAttributes<HTMLTableCellElement> {
  children: React.ReactNode | string | number;
  tag?: 'td' | 'th';
  colSpan?: number;
  width?: string;
  className?: string;
  innerClassName?: string; // span 태그에 적용되는 클래스
}

const TableCell = ({
  tag = 'td',
  width,
  children,
  className,
  innerClassName,
  colSpan,
  ...attributes
}: TableCellProps) => {
  const Tag = tag;

  return (
    <Tag
      className={joinClassName('c_table_cell', className)}
      style={width ? { width } : undefined}
      {...attributes}
      colSpan={colSpan}
    >
      <span className={joinClassName('c_table_cell_text', innerClassName)}>{children}</span>
    </Tag>
  );
};

export default TableCell;
