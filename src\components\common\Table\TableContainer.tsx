import React, { TableHTMLAttributes } from 'react';
import joinClassName from '@utils/joinClassName';

interface TableProps extends TableHTMLAttributes<HTMLTableElement> {}

const TableContainer = ({ children, className, ...attributes }: TableProps) => {
  return (
    <div className={joinClassName('c_table_wrapper', className)}>
      <table className="c_table" {...attributes}>
        {children}
      </table>
    </div>
  );
};

export default TableContainer;
