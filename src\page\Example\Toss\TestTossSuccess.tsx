import api from '@api/api.ts';
import Button from '@components/common/Button/Button.tsx';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

const TestTossSuccess = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isConfirmed, setIsConfirmed] = useState(false);
  const paymentKey = searchParams.get('paymentKey');
  const orderId = searchParams.get('orderId');
  const amount = searchParams.get('amount');

  useEffect(() => {
    // 5. 사용자 결제 완료 후, 서버에서 확인 절차를 거침
    async function confirm() {
      const requestData = {
        pgType: 'TOSS',
        data: {
          paymentKey: paymentKey,
          orderId: orderId,
          amount: amount,
        },
      };

      try {
        const apiUrl = `/api/orders/${orderId}/confirm`;
        const response = await api.post(apiUrl, requestData);
        removeQueryString();

        if (response.status == 200) {
          if (response.data.code == 'SUCCESS') {
            setIsConfirmed(true);
          } else {
            navigate(`/example/toss/failure?message=${response.data.message}&code=${response.data.code}`);
          }
        }
      } catch (error) {
        navigate(`/example/toss/failure?message=${error?.response.data.message}`);
      }
    }

    confirm();
  }, [searchParams]);

  // 현재 URL에서 쿼리스트링 제거
  const removeQueryString = () => {
    window.history.replaceState({}, '', window.location.pathname);
  };

  return (
    <div>
      <div>
        <Button text="주문하러 가기" onClick={() => navigate('/example/toss/checkout')} />
        <br />
        <br />
      </div>
      {isConfirmed && (
        <div>
          <img src="https://static.toss.im/illusts/check-blue-spot-ending-frame.png" width="120" height="120" />
          <h1>결제를 완료했어요</h1>
          <div>
            <div>
              <span style={{ marginRight: '50px' }}>결제 금액</span>
              <span>{amount}</span>
            </div>
            <div>
              <span style={{ marginRight: '50px' }}>주문번호</span>
              <span>{orderId}</span>
            </div>
            <div>
              <span style={{ marginRight: '50px' }}>paymentKey</span>
              <span>{paymentKey}</span>
            </div>
          </div>
        </div>
      )}
      {!isConfirmed && (
        <div>
          <h1>서버에서 결제 완료를 진행중입니다...</h1>
        </div>
      )}
    </div>
  );
};

export default TestTossSuccess;
