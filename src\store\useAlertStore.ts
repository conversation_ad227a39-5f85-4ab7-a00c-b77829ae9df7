import { create } from 'zustand';
import React from 'react';

interface AlertStore {
  alertState: {
    className?: string;
    title?: string;
    isOpen: boolean;
    content: string | React.ReactNode;
    onConfirm: () => void;
  };
  setAlertState: (state: any) => void;
  initAlertState: () => void;
  activeAlert: (content: string | React.ReactNode, onConfirm?: () => void | Promise<void>) => void;
}

export const useAlertStore = create<AlertStore>((set) => {
  const initAlertState = () =>
    set({ alertState: { className: '', isOpen: false, title: '알림', content: '', onConfirm: () => {} } });

  return {
    alertState: {
      className: '',
      title: '알림',
      isOpen: false,
      content: '',
      onConfirm: () => {},
    },
    setAlertState: (state) =>
      set((prev) => ({ alertState: typeof state === 'function' ? state(prev.alertState) : state })),
    initAlertState,
    activeAlert: (contentValue, onConfirm) =>
      set((prev) => ({
        alertState: {
          ...prev.alertState,
          isOpen: true,
          content: contentValue,
          onConfirm: () => {
            onConfirm && onConfirm();
            initAlertState();
          },
        },
      })),
  };
});
