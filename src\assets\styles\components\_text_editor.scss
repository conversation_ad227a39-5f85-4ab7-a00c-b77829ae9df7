@use '@styles/utils/mixin' as m;

.c_editor {
  width: 100%;
  height: auto;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid var(--g_05);

  &.readonly {
    #tiptap {
      .ProseMirror {
        min-height: 0;
        padding-bottom: 0;
      }
    }
  }
}

.c_editor_toolbar {
  @include m.flex();
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.25rem;
  background-color: var(--modal_bg);
  border-bottom: 1px solid var(--g_05);

  .item_box {
    @include m.flex(center);
    gap: 0.125rem;
    padding: 0.25rem;
    border-radius: 0.25rem;
    border: 1px solid var(--g_04);

    .c_button_icon {
      padding: 0.125rem;
    }
    .c_select {
      .c_selected_label {
        min-height: 1.75rem;
        font-size: 0.875rem;
      }
    }
  }
}

.fontsize_select_list,
.fontfamily_select_list {
  .c_select_lists_inner {
    .c_select_option_item {
      min-height: 1.5rem;
      font-size: 0.875rem;
    }
  }
}

.c_editor_color_pallette {
  position: fixed;
  max-width: 190px;
  z-index: 1000;
  padding: 0.5rem 0 0.75rem 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--modal_bg);
  border: 1px solid var(--g_05);

  .color-list {
    @include m.flex();
    gap: 0.25rem;
    flex-wrap: wrap;
    width: 100%;

    .color-item {
      button {
        width: 1.25rem;
        height: 1.25rem;
        border-radius: 0.25rem;
        cursor: pointer;
        border: 1px solid var(--g_05);

        &:hover {
          transform: scale(1.1);
        }
      }

      &.inherit {
        button {
          position: relative;
          overflow: hidden;
          &::after {
            content: '';
            position: absolute;
            top: 25%;
            left: 25%;
            width: 150%;
            height: 150%;
            transform: rotate(-45deg);
            border: 2px solid red;
          }
        }
      }
    }
  }
}

.tippy-box {
  max-width: none !important;
}

hr {
  border-color: var(--g_05);
}

.c_table_bubble {
  @include m.flex(center, center);
  border-radius: 0.25rem;
  width: auto;
  padding: 0.25rem;
  background-color: var(--g_03);

  .add_btn_group,
  .delete_btn_group,
  .header_toggle_btn_group {
    @include m.flex();
    gap: 0.25rem;
  }

  .delete_btn_group,
  .header_toggle_btn_group,
  .i_table_merge,
  .i_delete {
    position: relative;
    &::after {
      content: '';
      width: 2px;
      height: 25px;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      background-color: var(--g_07);
    }
  }
}

.codeblock_modal {
  width: 300px;
  .c_modal_body {
    padding: 0;
  }
  .c_modal_footer {
    button {
      width: 100%;
    }
  }
}

.table_modal {
  .c_modal_body {
    label {
      @include m.flex(center, center);
      gap: 0.25rem;
      margin-bottom: 0.625rem;

      span {
        width: 40px;
      }

      .c_input_wrapper {
        margin-bottom: 0;
      }
    }

    .button_wrapper {
      @include m.flex(center, flex-end);
      gap: 0.25rem;

      button {
        width: auto;
      }
    }
  }
}

.image_modal,
.link_modal,
.video_modal {
  width: 450px;

  .c_modal_body {
    .tab_list {
      @include m.flex();
      gap: 0.25rem;
      margin-bottom: 0.625rem;
      border-bottom: 1px solid var(--g_07);
    }
    .tab_panel {
      padding: 0.5rem 0;
    }

    .c_input_wrapper {
      width: 100%;
      margin-bottom: 0.625rem;
      .c_input {
        width: 100%;
      }
    }

    .file_upload_text {
      font-size: 0.875rem;
      margin-bottom: 1.25rem;
      color: var(--g_06);
    }

    .button_wrapper {
      @include m.flex(start, end);
      gap: 0.25rem;
    }
    // .c_button {
    //   width: 100%;
    // }
  }
}

#tiptap {
  padding: 10px !important;

  /* 여백 초기화 */
  div,
  ul,
  li,
  dl,
  dd,
  dt,
  ol,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  input,
  fieldset,
  legend,
  p,
  select,
  table,
  th,
  td,
  tr,
  textarea,
  button,
  form,
  figure,
  figcaption {
    margin: 0;
    padding: 0;
    text-align: initial;
  }

  /* a 링크 초기화 */
  a {
    text-decoration: none;
    color: inherit;
    cursor: pointer;
    transition: all 0.3s ease-in-out;
  }
  a:hover {
    text-decoration: none;
    filter: brightness(1.5);
  }

  /* 폰트 스타일 초기화 */
  address {
    font-style: normal;
  }

  /* 블릿기호 초기화 */
  ul,
  li,
  ol {
    list-style: none;
  }

  /* 테이블 초기화 */
  table {
    border-collapse: collapse;
    border-spacing: 0;

    th,
    td {
      font-weight: inherit;
    }
  }

  /* 버튼초기화 */
  button {
    border: 0;
  }

  /* Tiptap Style  */
  .ProseMirror-focused {
    border: none !important;
    outline: none !important;
  }

  .ProseMirror {
    min-height: 300px;
    padding-bottom: 1.25rem;
  }

  .tiptap .is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    height: 0;
    pointer-events: none;
    font-size: 0.875rem;
    color: var(--g_06);
  }

  .tiptap {
    p {
      padding: 0.375rem 0;
    }

    @for $i from 1 through 6 {
      h#{$i} {
        font-weight: 600;
        font-size: #{2 - ($i - 1) * 0.25}rem;
      }
    }

    pre {
      border-radius: 0.5rem;
      font-family: 'JetBrainsMono', monospace;
      margin: 1.5rem 0;
      padding: 0.75rem 1rem;
      background: var(--font_black);
      color: var(--g_01);

      code {
        font-size: 0.8rem;
        padding: 0;
        background: none;
        color: inherit;

        > p {
          padding-top: 1rem;
        }
      }

      .hljs-emphasis {
        font-style: italic;
      }

      .hljs-strong {
        font-weight: 700;
      }

      /* Code styling */
      .hljs-comment,
      .hljs-quote {
        color: var(--g_06);
      }

      .hljs-variable,
      .hljs-template-variable,
      .hljs-attribute,
      .hljs-tag,
      .hljs-regexp,
      .hljs-link,
      .hljs-name,
      .hljs-selector-id,
      .hljs-selector-class {
        color: var(--error);
      }

      .hljs-number,
      .hljs-meta,
      .hljs-built_in,
      .hljs-builtin-name,
      .hljs-literal,
      .hljs-type,
      .hljs-params {
        color: var(--warning);
      }

      .hljs-string,
      .hljs-symbol,
      .hljs-bullet {
        color: var(--confirm);
      }

      .hljs-title,
      .hljs-section {
        color: #faf594;
      }

      .hljs-keyword,
      .hljs-selector-tag {
        color: #70cff8;
      }
    }

    /* List styles */
    ul,
    ol {
      padding: 0 1rem;
    }

    ul > li {
      list-style: disc;

      li {
        list-style: circle;
      }
    }

    ol > li {
      list-style: decimal;
    }

    /* Task list specific styles */
    ul[data-type='taskList'] {
      list-style: none;
      margin-left: 0;
      padding: 0;

      li {
        align-items: flex-start;
        display: flex;

        > label {
          flex: 0 0 auto;
          margin-right: 0.5rem;
          user-select: none;
        }

        > div {
          flex: 1 1 auto;
        }
      }

      input[type='checkbox'] {
        cursor: pointer;
      }

      ul[data-type='taskList'] {
        margin: 0;
      }
    }

    blockquote {
      margin: 1.5rem 0;
      padding-left: 1rem;
      border-left: 5px solid var(--g_05);
    }

    a {
      color: var(--p_07);
      text-decoration: underline;
    }

    iframe {
      padding: 0.625rem;
    }

    .tableWrapper {
      margin: 1.25rem 0;
    }

    table {
      overflow: hidden;
      border-collapse: collapse;
      table-layout: fixed;
      width: 100%;
      user-select: contain;

      tr {
        th,
        td {
          padding: 0.9375rem 0.375rem;
          border: 1px solid var(--g_05) !important;
        }

        .selectedCell {
          position: relative;
          &::after {
            content: '';
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            pointer-events: none;
            position: absolute;
            z-index: 2;
          }
        }
      }

      tbody {
        tr {
          &:nth-child(2n) {
            background-color: var(--g_02);
          }

          &:nth-child(2n + 1) {
            background-color: var(--g_03);
          }

          &:hover {
            > th {
              background-color: none;
            }
            > td {
              background-color: none;
            }
          }

          .selectedCell {
            &::after {
              background: rgba(var(--p_07), 0.2);
            }
          }
        }
      }
    }
  }

  .resize-cursor {
    td:has(.column-resize-handle) {
      cursor: col-resize;
      border-right: 2px solid var(--p_07) !important;
    }
  }
}
