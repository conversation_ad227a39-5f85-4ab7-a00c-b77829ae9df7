interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

export interface PageDTO {
  boardTempletId: number;
  pageName: string;
  pageFileName: string;
  filePath: string;
  pageTypeCode: 'L' | 'R' | 'M' | 'P' | 'C' | 'N' | 'B';
  pageDesc: string;
  pageContent: string;
  pageLink: string;
  pageUrl: string;
  roleUseYn: 'Y' | 'N';
}
export interface Page extends PageDTO {
  id: string;
  dateInfo: DateInfo;
}

export interface SubPageDTO extends PageDTO {
  parentId: number;
}

export interface DeletePages {
  parentId: number | string;
  ids: number[];
}
