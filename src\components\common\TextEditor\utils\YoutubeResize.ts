// X-Frame-Options: SAMEORIGIN 에러는 유튜브가 보안상의 이유로 자사 도메인(youtube.com)에서만
// iframe 임베딩을 허용하기 때문에 발생합니다.
// youtube-nocookie.com 도메인을 사용하면 이 제한을 우회할 수 있습니다.

import Youtube from '@tiptap/extension-youtube';
import captionBlack from '@assets/images/icon/icon_editor_add_alt_black.svg';
import alignLeftBlack from '@assets/images/icon/icon_editor_align_left_black.svg';
import alignCenterBlack from '@assets/images/icon/icon_editor_align_center_black.svg';
import alignRightBlack from '@assets/images/icon/icon_editor_align_right_black.svg';
import captionPrimary from '@assets/images/icon/icon_editor_add_alt_primary.svg';
import alignLeftPrimary from '@assets/images/icon/icon_editor_align_left_primary.svg';
import alignCenterPrimary from '@assets/images/icon/icon_editor_align_center_primary.svg';
import alignRightPrimary from '@assets/images/icon/icon_editor_align_right_primary.svg';

export const YoutubeResize = Youtube.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      width: {
        default: 640,
        parseHTML: (element) => {
          return element.getAttribute('width') || 640;
        },
      },
      height: {
        default: 360,
        parseHTML: (element) => {
          return element.getAttribute('height') || 360;
        },
      },
      style: {
        default: 'max-width: 80vw;',
        parseHTML: (element) => {
          return element.style.cssText;
        },
      },
      caption: {
        default: '',
        parseHTML: (element) => {
          console.log('element :: ', element.getAttribute('caption'));
          const figcaption = element.getAttribute('caption');
          return figcaption || '';
        },
      },
      src: {
        default: null,
        parseHTML: (element) => {
          // youtube.com URL을 youtube-nocookie.com으로 변환
          const src = element.getAttribute('src');
          if (src) {
            if (src.includes('youtube-nocookie.com')) {
              return src;
            } else if (src.includes('youtu.be')) {
              // youtu.be/VIDEO_ID 형식을 www.youtube-nocookie.com/embed/VIDEO_ID 형식으로 변환
              const videoId = src.split('/').pop();
              return `https://www.youtube-nocookie.com/embed/${videoId}`;
            }
            const videoIds = src.split('?v=').pop();
            return `https://www.youtube-nocookie.com/embed/${videoIds}`;
          }
          return null;
        },
      },
    };
  },

  addNodeView() {
    return ({ node, editor, getPos }) => {
      const {
        view,
        options: { editable },
      } = editor;
      const { style, caption, src, width, height } = node.attrs;
      const $wrapper = document.createElement('p');
      const $caption = document.createElement('figcaption');
      const $container = document.createElement('figure');
      const $iframe = document.createElement('iframe');
      const iconStyle = 'width: 24px; height: 24px; cursor: pointer;';

      // iframe src 속성을 youtube-nocookie.com으로 설정
      if (src) {
        if (src.includes('youtube-nocookie.com')) {
          $iframe.setAttribute('src', src);
        } else if (src.includes('youtu.be')) {
          const videoId = src.split('/').pop();
          $iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${videoId}`);
        } else {
          const videoId = src.split('?v=').pop();
          $iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${videoId}`);
        }
      }

      const dispatchNodeView = () => {
        if (typeof getPos === 'function') {
          const containerStyle = $container.getAttribute('style');
          
          const newAttrs = {
            ...node.attrs,
            width: $iframe.getAttribute('width'),
            height: $iframe.getAttribute('height'),
            style: `${containerStyle?.replace(/outline:.*?;/, '')} width: ${$iframe.getAttribute('width')}px; height: ${$iframe.getAttribute('height')}px;`,
            caption: $caption.textContent,
          };
          console.log('$caption.textContent :: ', $caption);
          view.dispatch(view.state.tr.setNodeMarkup(getPos(), null, newAttrs));
        }
      };

      const paintPositionController = () => {
        const $positionController = document.createElement('div');

        const $captionController = document.createElement('img');
        const $leftController = document.createElement('img');
        const $centerController = document.createElement('img');
        const $rightController = document.createElement('img');

        $positionController.setAttribute(
          'style',
          'position: absolute; top: 0%; left: 50%; min-width: 150px; padding: 10px; z-index: 98; background-color: #fff; border-radius: 4px; outline: 2px solid #6C6C6C; cursor: pointer; transform: translate(-50%, -50%); display: flex; justify-content: space-between; align-items: center;'
        );

        $captionController.setAttribute('src', captionBlack);
        $captionController.setAttribute('alt', 'youtube caption');
        $captionController.setAttribute('style', iconStyle);
        $captionController.addEventListener('mouseover', () => $captionController.setAttribute('src', captionPrimary));
        $captionController.addEventListener('mouseout', () => $captionController.setAttribute('src', captionBlack));

        $leftController.setAttribute('src', alignLeftBlack);
        $leftController.setAttribute('style', iconStyle);
        $leftController.setAttribute('alt', 'align left');
        $leftController.addEventListener('mouseover', () => $leftController.setAttribute('src', alignLeftPrimary));
        $leftController.addEventListener('mouseout', () => $leftController.setAttribute('src', alignLeftBlack));

        $centerController.setAttribute('src', alignCenterBlack);
        $centerController.setAttribute('alt', 'align center');
        $centerController.setAttribute('style', iconStyle);
        $centerController.addEventListener('mouseover', () =>
          $centerController.setAttribute('src', alignCenterPrimary)
        );
        $centerController.addEventListener('mouseout', () => $centerController.setAttribute('src', alignCenterBlack));

        $rightController.setAttribute('src', alignRightBlack);
        $rightController.setAttribute('alt', 'align right');
        $rightController.setAttribute('style', iconStyle);
        $rightController.addEventListener('mouseover', () => $rightController.setAttribute('src', alignRightPrimary));
        $rightController.addEventListener('mouseout', () => $rightController.setAttribute('src', alignRightBlack));

        $captionController.addEventListener('click', () => {
          const newCaption = window.prompt('Enter a caption for your video', $caption.textContent || '');
          if (newCaption === null) return;

          $caption.textContent = newCaption;
          dispatchNodeView();
        });

        $leftController.addEventListener('click', () => {
          $container.setAttribute('style', `${style} margin: 0 auto 0 0;`);
          dispatchNodeView();
        });
        $centerController.addEventListener('click', () => {
          $container.setAttribute('style', `${style} margin: 0 auto;`);
          dispatchNodeView(); 
        });
        $rightController.addEventListener('click', () => {
          $container.setAttribute('style', `${style} margin: 0 0 0 auto;`);
          dispatchNodeView();
        });

        $positionController.appendChild($captionController);
        $positionController.appendChild($leftController);
        $positionController.appendChild($centerController);
        $positionController.appendChild($rightController);

        $container.appendChild($positionController);
      };

      $wrapper.setAttribute('style', `display: flex; max-width: 80vw;`);
      $wrapper.appendChild($container);

      $container.setAttribute('style', `${style}`);
      $container.appendChild($iframe);

      $caption.setAttribute('style', 'color: #9c9c9c; font-size: 12px; padding: 0.25rem 0.625rem');
      $caption.textContent = caption || '';
      $container.appendChild($caption);

      Object.entries(node.attrs).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        if (key === 'src') {
          // src 속성을 youtube-nocookie.com으로 설정
          if (src.includes('youtube-nocookie.com')) {
            $iframe.setAttribute('src', src);
          } else if (src.includes('youtu.be')) {
            const videoId = src.split('/').pop();
            $iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${videoId}`);
          } else {
            const videoId = src.split('?v=').pop();
            $iframe.setAttribute('src', `https://www.youtube-nocookie.com/embed/${videoId}`);
          }
        } else if (key === 'style') {
          // style 속성은 container에만 적용하고 iframe에는 적용하지 않음
          return;
        } else {
          $iframe.setAttribute(key, value);
        }
      });

      if (!editable) return { dom: $container };

      const isMobile = document.documentElement.clientWidth < 768;
      const dotPosition = isMobile ? '-8px' : '-4px';
      const dotsPosition = [
        `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,
        `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,
      ];

      let isResizing = false;
      let startX: number, startWidth: number;

      $container.addEventListener('click', () => {
        const isMobile = document.documentElement.clientWidth < 768;
        isMobile && (document.querySelector('.ProseMirror-focused') as HTMLElement)?.blur();

        if ($container.childElementCount > 3) {
          for (let i = 0; i < 5; i++) {
            $container.removeChild($container.lastChild as Node);
          }
        }

        paintPositionController();

        if (!editable) return;

        console.log('readOnly', editable);

        $container.style.position = 'relative';
        $container.style.outline = '2px dashed #6C6C6C';
        $container.style.cursor = 'pointer';

        Array.from({ length: 4 }, (_, index) => {
          const $dot = document.createElement('div');
          $dot.setAttribute(
            'style',
            `position: absolute; width: ${isMobile ? 16 : 9}px; height: ${isMobile ? 16 : 9}px; outline: 1.5px solid #6C6C6C; border-radius: 50%; ${dotsPosition[index]}`
          );

          $dot.addEventListener('mousedown', (e) => {
            e.preventDefault();
            isResizing = true;
            startX = e.clientX;
            startWidth = parseInt($iframe.getAttribute('width') || '640');

            const onMouseMove = (e: MouseEvent) => {
              if (!isResizing) return;
              const deltaX = index % 2 === 0 ? -(e.clientX - startX) : e.clientX - startX;
              const newWidth = startWidth + deltaX;
              const newHeight = Math.round((newWidth * 9) / 16);

              $iframe.setAttribute('width', String(newWidth));
              $iframe.setAttribute('height', String(newHeight));
            };

            const onMouseUp = () => {
              if (isResizing) {
                isResizing = false;
              }
              dispatchNodeView();

              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          });

          $dot.addEventListener(
            'touchstart',
            (e) => {
              e.cancelable && e.preventDefault();
              isResizing = true;
              startX = e.touches[0].clientX;
              startWidth = parseInt($iframe.getAttribute('width') || '640');

              const onTouchMove = (e: TouchEvent) => {
                if (!isResizing) return;
                const deltaX = index % 2 === 0 ? -(e.touches[0].clientX - startX) : e.touches[0].clientX - startX;
                const newWidth = startWidth + deltaX;
                const newHeight = Math.round((newWidth * 9) / 16);

                $iframe.setAttribute('width', String(newWidth));
                $iframe.setAttribute('height', String(newHeight));
              };

              const onTouchEnd = () => {
                if (isResizing) {
                  isResizing = false;
                }
                dispatchNodeView();

                document.removeEventListener('touchmove', onTouchMove);
                document.removeEventListener('touchend', onTouchEnd);
              };

              document.addEventListener('touchmove', onTouchMove);
              document.addEventListener('touchend', onTouchEnd);
            },
            { passive: false }
          );
          $container.appendChild($dot);
        });
      });

      document.addEventListener('click', (e: MouseEvent) => {
        const $target = e.target as HTMLElement;
        const isClickInside = $container.contains($target) || $target.style.cssText === iconStyle;

        if (!isClickInside) {
          const containerStyle = $container.getAttribute('style');
          const newStyle = containerStyle?.replace(/outline:.*?;/, '');
          $container.setAttribute('style', newStyle as string);

          if ($container.childElementCount > 3) {
            for (let i = 0; i < 5; i++) {
              $container.removeChild($container.lastChild as Node);
            }
          }
        }
      });

      return {
        dom: $wrapper,
      };
    };
  },
});
