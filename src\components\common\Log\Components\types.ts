export interface LogParams {
  // 글루벌 추적 ID
  globalTraceId?: string | null;
  // 메뉴명
  menuName?: string;
  // 유저 행동 (ex. 클릭인지, 화면 조회인지 등)
  logType?: 'click' | 'screen' | 'submit' | null;
  // 버튼 영역 (다른 위치의 같은 버튼명을 가진 경우 구분을 위해서)
  buttonSection?: string | null;
  // 버튼명
  button?: string | null;
  // FULL URL
  fullUrl? : string | null;
  // url
  url? : string | null;
  // 이전 페이지
  referrer?: string | null;
  // 추가적인 기타 파라미터
  data?: any | null;
}
