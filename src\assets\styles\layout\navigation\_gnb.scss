@use '@styles/utils/mixin' as m;

.c_gnb {
  @include m.flex(center, space-between);
  width: 100%;
  height: 60px;
  padding: 0 1.5rem;
  transition: all 0.3s;
  background-color: var(--gnb_bg);
  box-shadow: 0 0 5px 2px rgba(black, 0.17);

  .left {
    .mbtn {
      position: relative;
      width: 32px;
      height: 32px;
      cursor: pointer;

      i {
        position: absolute;
        right: 0;
        height: 3px;
        transition: all 0.3s;
        background-color: white;

        &.bar_1 {
          top: 10%;
          width: 40%;
        }
        &.bar_2 {
          top: 40%;
          width: 60%;
        }
        &.bar_3 {
          top: 70%;
          width: 80%;
        }
      }

      &:hover {
        i {
          right: 50%;
          transform: translateX(50%);

          &.bar_1,
          &.bar_2,
          &.bar_3 {
            width: 80%;
          }
        }
      }
      &.active {
        i {
          width: 80%;
          right: 50%;
          transform: translateX(50%);
        }
      }
    }
  }
  .right {
    @include m.flex(center, end);
    width: calc(100% - 200px);
    gap: 1rem;
    height: 100%;

    .timeout {
      @include m.flex(center, space-between);
      gap: 1rem;
      padding-right: 1rem;
      color: white;
      border-right: 1px solid rgba(white, 0.3);
    }

    .my_info_name {
      color: white;
    }

    .c_searchbar_wrapper {
      width: 225px;
      input {
        height: 35px;
      }
    }

    .button_wrapper {
      @include m.flex(center, space-between);
      gap: 10px;

      button {
        border-radius: 50rem;
        background-color: var(--g_01);
        &.icon {
          width: 32px;
          height: 32px;
        }
      }
    }
  }

  @include m.bp_large() {
    font-size: 0.875rem;
    padding: 0 1.25rem;

    .left {
      .mbtn {
        width: 28px;
        height: 28px;
      }
    }

    .right {
      width: calc(100% - 150px);
      gap: 0.75rem;
      .timeout {
        padding-right: 0.5rem;
      }
    }
  }

  @include m.bp_medium() {
    height: 55px;
    font-size: 0.75rem;
    padding: 0 1.125rem;

    .left {
      .mbtn {
        width: 24px;
        height: 24px;
      }
    }

    .right {
      width: calc(100% - 125px);
      gap: 0.5rem;
    }
  }
}

.c_profile_modal {
  position: fixed;
  width: 200px;
  background-color: #fff;
  border: 1px solid var(--g_05);
  border-radius: 0.25rem;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2);
  z-index: 1000;

  .c_profile_modal_title {
    padding: 0.5rem 0.625rem;
    font-size: 1rem;
    font-weight: 600;
    background-color: var(--p_05);
    color: #fff;
    border-radius: 0.25rem 0.25rem 0 0;
  }

  .c_profile_modal_content {
    font-size: 0.875rem;
    font-weight: 400;

    &_item {
      padding: 0.5rem 0.625rem;

      button {
        width: 100%;
      }
    }
  }
}
