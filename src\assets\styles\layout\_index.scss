@use '@styles/utils/mixin' as m;
@use '@styles/utils/colors_dark' as dc;
@use '@styles/utils/colors_light' as lc;
@use '@styles/utils/colors_common' as cc;

// 레이아웃
@use '@styles/layout/header';
@use '@styles/layout/footer';
@use '@styles/layout/main';
@use '@styles/layout/scroll';
@use '@styles/layout/content_layout';

// navigation
@use '@styles/layout/navigation/gnb';
@use '@styles/layout/navigation/lnb';
@use '@styles/layout/navigation/rnb';

@font-face {
  font-family: 'govermentTTF';
  src: url('@assets/fonts/governmentFont.ttf');
}

@font-face {
  font-family: 'pretendard';
  src: url('@assets/fonts/pretendard.ttf');
}

body {
  font-family: 'pretendard';
}

[theme='dark'] {
  body {
    color: white;
    background-color: dc.$bg;
  }
}

[theme='light'] {
  body {
    color: black;
    background-color: lc.$bg;
  }
}
