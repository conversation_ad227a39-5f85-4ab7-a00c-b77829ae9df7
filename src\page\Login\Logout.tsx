import api from '@api/api';
import { useLoginStore } from '@store/useLoginStore';
import { useEffect } from 'react';

const Logout = () => {

    const { setIsLogin } = useLoginStore();

    useEffect(() => {
        const handleLogout = async () => {
            setIsLogin(false);
            window.location.href = '/login';
            localStorage.removeItem('accessToken');

            await api.post('/auth/logout');
        };

        handleLogout();
    }, [])

    return (
        <div>
        </div>
    );
};

export default Logout;