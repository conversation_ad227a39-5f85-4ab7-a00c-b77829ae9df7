import dayjs from 'dayjs';

/**
 * Date 객체를 문자열 패턴의 날짜로 변경합니다.
 */
export function dateToString(date, format = 'YYYY.MM.DD') {
    return dayjs(date).format(format);
}

/**
 * 문자열 패턴의 날짜를 Date 객체로 반환합니다.
 */
export function stringToDate(value, format = 'YYYY.MM.DD') {
    return dayjs(value, format);
}

/**
 * 현재 시간을 반환합니다.
 * 
 * @returns 
 */
export const getNowDate = () => {
    return dayjs().format("YYYY-MM-DDTHH:mm:ss.SSS");
};