import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { deleteMenu, editMenuDetails, getMenuDetail, getPageOptions, saveChildMenu } from '@api/admin/menuManageApi';
import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import FormTextarea from '@components/common/Form/FormTextarea';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import SubTitle from '@components/common/Title/SubTitle';
import Grid from '@components/common/Grid/Grid';

import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useSaveMenuStore } from '@store/useSaveMenuStore';
import { useAlert } from '@hooks/useAlert';
import { getExtractedStyleSheetIconOptions } from '@utils/extractedIconOption';

import {
  CellValueChangedEvent,
  ColDef,
  GridSizeChangedEvent,
  RowDragEndEvent,
  RowSelectedEvent,
} from '@ag-grid-community/core';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import { pageTypeOptions } from '@constants/options';

/**
 * @TODO
 *  1. 권한, 서비스, 코드, 그룹 검색 레이아웃 변경
 *  2. Title에  BreadCrumb 추가 및 계산 로직 필요
 */

interface MenuDetailProps {
  pageType?: 'detail' | 'edit';
  categoryId: number;
  setCategoryId: (id: number | null) => void;
}

const MenuDetail = ({ pageType = 'detail', categoryId, setCategoryId }: MenuDetailProps) => {
  const [type, setType] = useState(pageType);
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { openAlert } = useAlert();
  const [menuDetail, setMenuDetail] = useState(null);
  const [childMenuList, setChildMenuList] = useState([]);
  const [iconOptions, setIconOptions] = useState(null);
  const gridRef = useRef(null);
  const { isSaved, setIsSaved } = useSaveMenuStore(); // 메뉴 변경사항이 저장되었는가
  const [pageOptions, setPageOptions] = useState([]);

  // @메뉴정보 상세 조회, 수정 --------------------------------------
  const methods = useForm({
    defaultValues: {
      name: '',
      url: '',
      desc: '',
      pageId: {
        label: '선택이 필요합니다.',
        value: '',
      },
      icon: { label: 'arrow_right', value: 'arrow_right' },
      useYn: { label: '사용', value: 'Y' },
      delAbleYn: { label: '가능', value: 'Y' },
    },
  });

  const handleGetMenuDetail = async () => {
    const [menuDetailResponse, pageOtionsResponse] = await Promise.all([
      getMenuDetail({ categoryId }),
      getPageOptions(),
    ]);

    const options = pageOtionsResponse.map((item) => ({
      value: item.pageId,
      label: item.pageNm,
    }));
    setPageOptions([
      {
        label: '선택이 필요합니다.',
        value: '',
      },
      ...options,
    ]);

    // 하위 메뉴 sortNo 기준으로 정렬
    // const childMenuList = menuDetailResponse.childs.sort((a, b) => a.sortNo - b.sortNo);
    // 하위 메뉴 -> 하위 페이지로 변경
    const childPageList = menuDetailResponse.pages;
    setMenuDetail(menuDetailResponse);
    setChildMenuList(childPageList);
    setOriginalRowData(childPageList);
  };

  const handleSubmit = async (data) => {
    if (type === 'edit') {
      const { name, url, desc, icon, useYn, delAbleYn, pageId } = data;

      const paredFormData = {
        name,
        url,
        desc,
        icon: icon.value,
        useYn: useYn.value,
        pageId: pageId.value,
        delAbleYn: delAbleYn.value,
      };

      const editResult = await editMenuDetails({
        menuId: menuDetail.id,
        detailData: paredFormData,
      });

      setAlertState({
        isOpen: true,
        title: '알람',
        content: editResult.message,
        type: editResult.status === 'success' ? 'success' : 'error',
        onConfirm: () => {
          initAlertState();
          handleGetMenuDetail();
          setIsSaved(true);
          setType('detail');
        },
      });
    }
  };

  /**관리자 메뉴 삭제 */
  const handleDeleteMenu = () => {
    setConfirmState({
      title: '알람',
      isOpen: true,
      content: '선택된 메뉴를 삭제하시겠습니까?',
      onConfirm: () => {
        initConfirmState();
        deleteCategoryMenu();
      },
      onCancel: () => {
        initConfirmState();
      },
    });
  }; // 삭제 confirm

  const deleteCategoryMenu = async () => {
    // 메뉴 삭제 API 호출
    const response = await deleteMenu({ ids: [categoryId] });
    openAlert(response?.message, () => {
      initAlertState();
      setCategoryId(null);
      setIsSaved(true);
    });
  };

  useEffect(() => {
    // 메뉴상세조회 콜
    if (categoryId) {
      handleGetMenuDetail();
      setType('detail');
    }
  }, [categoryId]);

  useEffect(() => {
    if (menuDetail) {
      const icon = menuDetail.icon || { label: 'arrow_right', value: 'arrow_right' };
      const useYn = menuDetail.useYn || { label: '사용', value: 'Y' };
      const delAbleYn = menuDetail.delAbleYn || { label: '가능', value: 'Y' };
      const desc = menuDetail.desc || '';
      const name = menuDetail.name || '';
      const url = menuDetail.url || '';
      const pageId = {
        label: menuDetail?.pageId ? utils.getLabel(menuDetail?.pageId, pageOptions) : '선택이 필요합니다.',
        value: menuDetail?.pageId ? menuDetail?.pageId : '',
      };

      methods.reset({
        name,
        url,
        desc,
        icon,
        useYn,
        pageId,
        delAbleYn,
      });
    }
  }, []);

  useEffect(() => {
    if (type === 'edit') {
      methods.reset({
        name: menuDetail.name,
        url: menuDetail.url,
        desc: menuDetail.desc,
        pageId: {
          label: menuDetail?.pageId ? utils.getLabel(menuDetail?.pageId, pageOptions) : '선택이 필요합니다.',
          value: menuDetail?.pageId ? menuDetail?.pageId : '',
        },
        icon: { label: menuDetail.icon, value: menuDetail.icon },
        useYn: { label: menuDetail.useYn === 'Y' ? '사용' : '미사용', value: menuDetail.useYn },
        delAbleYn: { label: menuDetail.delAbleYn === 'Y' ? '가능' : '불가능', value: menuDetail.delAbleYn },
      });
    }
  }, [type]);

  // 아이콘 옵션 조회
  useEffect(() => {
    if (!iconOptions) {
      setIconOptions(getExtractedStyleSheetIconOptions());
    }
  }, []);

  // @ 메뉴 하위 페이지 조회 관련 ------------------------------------
  const [rowData, setRowData] = useState([]);
  const [originalRowData, setOriginalRowData] = useState([]);
  const [isSameData, setIsSameData] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    // { headerName: 'No.', field: 'sortNo', width: 110, sortable: true },
    { headerName: '유형', field: 'pageTypeCode', width: 100, valueFormatter: ({ value }) => { return utils.getLabel(value, pageTypeOptions); } },
    { headerName: '페이지명', field: 'pageName', flex: 1 },
    { headerName: '파일경로', field: 'filePath', width: 400 },
    { headerName: '페이지URL', field: 'pageUrl', width: 200 },
    { headerName: '생성자', field: 'dateInfo.createUser', width: 100 },
    { headerName: '생성일', field: 'dateInfo.createDate', flex: 1 },
    { headerName: '수정자', field: 'dateInfo.updateUser', width: 100 },
    { headerName: '수정일', field: 'dateInfo.updateDate', flex: 1 },
  ]);

  const parsedSortNumber = (data) => {
    return data.map((item, index) => ({
      ...item,
      sortNo: index + 1,
    }));
  };

  const onRowDragEnd = useCallback((event: RowDragEndEvent) => {
    // 드래그가 끝난 후 모든 행 데이터를 가져옴
    const updatedData = [];

    // 드래그된 행의 인덱스 추적
    const draggedRowIndex = event.node.rowIndex;

    event.api.forEachNode((node) => {
      if (node.data) {
        updatedData.push(node.data);
      }
    });

    // 드래그 후 스크롤 위치 유지
    const viewPort = document.querySelector('.ag-body-viewport');
    const viewPortScrollTop = viewPort?.scrollTop;

    // sort 번호 재정렬
    const reorderedData = parsedSortNumber(updatedData);

    // 업데이트된 데이터 설정
    setRowData(reorderedData);

    // 데이터 업데이트 후 드래그된 행에 포커스 유지
    setTimeout(() => {
      viewPort?.scrollTo({
        top: viewPortScrollTop,
      });

      // 드래그된 행에 포커스 유지
      const rowNode = event.api.getDisplayedRowAtIndex(draggedRowIndex);
      rowNode?.setSelected(true);
    }, 100);
  }, []);

  const onRowSelected = useCallback((event: RowSelectedEvent) => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data) || [];
    setSelectedRows(selectedData);
  }, []);

  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent) => {
      // 셀 값이 변경되었을 때 호출되는 함수
      const {
        data,
        colDef: { field },
        oldValue,
      } = event;
      const { id } = data;

      // 원본 데이터에서 동일한 코드를 가진 항목 찾기
      const originalItem = originalRowData.find((item) => item.id === id);

      // 원본 데이터가 존재하고, 현재 데이터와 비교하여 변경 여부 확인
      if (originalItem) {
        const isSameData = originalItem[field] === oldValue;
        setIsSameData(isSameData);
      }
    },
    [originalRowData]
  );

  // 코드 추가를 위한 행 추가 로직
  const handleAddRow = () => {
    setRowData((prev) => {
      const prevData = prev;
      const newData = {
        id: null,
        name: '',
        url: '',
        sortNo: prevData[prevData.length - 1]?.sortNo + 1 || 1,
        dateInfo: { createUser: '', createDate: '', updateUser: '', updateDate: '' },
        checked: false,
      };

      setTimeout(() => {
        // 현재 No. 컬럼의 정렬 상태 확인
        const columnState = gridRef.current?.api.getColumnState();
        const sortColumn = columnState?.find((col) => col.colId === 'sortNo');
        const isSortColumnSorting = sortColumn?.sort;

        let rowIndex = newData?.sortNo - 1; // 기본은 마지막 인덱스
        if (isSortColumnSorting === 'desc') rowIndex = 0; // 내림차순정렬일 경우만 첫번째 인덱스로 변경

        gridRef.current!.api.ensureIndexVisible(rowIndex);
        gridRef.current!.api.setFocusedCell(rowIndex, 'name');
        setTimeout(() => {
          gridRef.current!.api.startEditingCell({
            rowIndex: rowIndex,
            colKey: 'name',
          });
        }, 100);
      }, 100);

      return [...prevData, newData];
    });
  };

  const handleDeletePop = () => {
    // const hasNewData = selectedRows.some((row) => row.id === null);
    setConfirmState({
      isOpen: true,
      content: (
        <>
          <hr className="my-2" />
          <p className="font-bold text-red-500">{selectedRows.map((row) => row.name).join(', ')}</p>
          <hr className="my-2" />
          <p>하위 메뉴를 삭제하시겠습니까?</p>
          <p>삭제된 메뉴는 복구할 수 없습니다.</p>
        </>
      ),
      onConfirm: () => {
        handleDeleteRow();
        initConfirmState();
      },
      onCancel: initConfirmState,
    });
  };

  // 선택한 Row 삭제 버튼 클릭 이벤트 핸들러 >> 신규데이터가 있을때, 복합적인 로직 필요
  const handleDeleteRow = async () => {
    const hasNewData = selectedRows.find((row) => row.id === null);

    if (hasNewData) {
      const filteredSelectedRows = selectedRows.filter((row) => row.id === null);
      setRowData((prev) => parsedSortNumber(prev.filter((row) => !filteredSelectedRows.includes(row))));
    }

    const prevSavedRowDatas = selectedRows.filter((row) => row.id);
    const deleteIds = prevSavedRowDatas.map((row) => row.id);

    if (deleteIds.length === 0) return;

    const response = await deleteMenu({ ids: deleteIds });

    setAlertState({
      isOpen: true,
      title: '알람',
      content: response?.message,
      type: response?.status === 'success' ? 'success' : 'error',
      onConfirm: () => {
        initAlertState();
        handleGetMenuDetail();
      },
    });
  };

  const handleSaveChildMenu = async () => {
    const parsedSaveData = rowData.map((row) => {
      return {
        id: row.id,
        name: row.name,
        url: row.url,
      };
    });

    if (parsedSaveData.length === 0) return;
    if (parsedSaveData.some((row) => !row.name || !row.url)) {
      setAlertState({
        isOpen: true,
        title: '알람',
        content: (
          <p>
            메뉴 이름과 페이지 URL을 입력해주세요. <br />
            행이 포커스 되어있다면 포커스를 해제하고 다시 시도하세요.
          </p>
        ),
        type: 'error',
        onConfirm: () => {
          initAlertState();
        },
      });
      return;
    }

    const response = await saveChildMenu({
      categoryId,
      saveData: parsedSaveData,
    });

    setAlertState({
      isOpen: true,
      title: '알람',
      content: response?.message,
      type: response?.status === 'success' ? 'success' : 'error',
      onConfirm: () => {
        initAlertState();
        handleGetMenuDetail();
      },
    });
  };

  const parsedRowData = useMemo(() => {
    return childMenuList.map((item, index) => {
      return {
        id: item.id,
        pageName: item.pageName,
        pageFileName: item.pageFileName,
        filePath: item.filePath,
        pageTypeCode: item.pageTypeCode,
        pageUrl: item.pageUrl,
        dateInfo: {
          createUser: item.dateInfo.createUser,
          createDate: item.dateInfo.createDate,
          updateUser: item.dateInfo.updateUser,
          updateDate: item.dateInfo.updateDate,
        },
        checked: false,
      };
    });
  }, [childMenuList]);

  useEffect(() => {
    setIsSameData(
      rowData.length === originalRowData.length &&
        rowData.every((row, index) => {
          const original = originalRowData[index];
          // 텍스트 변화도 감지하기 위해 직접 비교
          return row.name === original.name && row.url === original.url && row.sortNo === original.sortNo;
        })
    );
  }, [rowData, originalRowData]);

  useEffect(() => {
    if (childMenuList) {
      setRowData(parsedRowData);
    }
  }, [childMenuList]);

  // 카테고리 아이디가 변경될 때마다 선택된 행 초기화
  useEffect(() => {
    setSelectedRows([]);
  }, [categoryId]);

  return categoryId ? (
    <>
      <div className="admin_menu_info_detail">
        <Form methods={methods} onSubmit={handleSubmit}>
          <TableContainerHeader
            leftChildren={<SubTitle>메뉴 정보</SubTitle>}
            rightChildren={
              <>
                <Button
                  text="메뉴 삭제"
                  color="red"
                  onClick={handleDeleteMenu}
                  clickLog={{ buttonSection: '메뉴 정보' }}
                />
                {type === 'detail' && (
                  <Button
                    text="수정"
                    disabled={isSaved}
                    onClick={() => {
                      setType('edit');
                    }}
                    clickLog={{ buttonSection: '메뉴 정보' }}
                  />
                )}
                {type === 'edit' && (
                  <>
                    <Button
                      text="취소"
                      color="grayscale"
                      onClick={() => {
                        setType('detail');
                      }}
                    />
                    <Button type="submit" text="저장" clickLog={{ buttonSection: '메뉴 정보' }} />
                  </>
                )}
              </>
            }
          />
          <TableContainer id="admin_menu_info_table">
            <TableBody>
              <TableBodyRow
                rowData={[
                  {
                    title: '메뉴 이름',
                    required: true,
                    contents:
                      type === 'detail' ? (
                        menuDetail?.name
                      ) : (
                        <FormInput name="name" placeholder="메뉴 이름을 입력해 주세요" />
                      ),
                  },
                  {
                    title: '페이지 URL',
                    required: true,
                    contents:
                      type === 'detail' ? (
                        menuDetail?.url
                      ) : (
                        <FormInput wrapperClassName="url_input" name="url" placeholder="페이지 URL을 입력해 주세요" />
                      ),
                  },
                ]}
              />
              <TableBodyRow
                rowData={[
                  {
                    title: '페이지 선택',
                    contents:
                      type === 'detail' ? (
                        utils.getLabel(menuDetail?.pageId, pageOptions)
                      ) : (
                        <FormSelectBox name="pageId" options={pageOptions} />
                      ),
                  },
                  {
                    title: '아이콘',
                    contents:
                      type === 'detail' ? (
                        <div className="menu_icon_select">
                          <div className="c_selected_label" data-selected-value={menuDetail?.icon}>
                            <span className="menu_icon_select_label_text">{menuDetail?.icon}</span>
                          </div>
                        </div>
                      ) : (
                        <FormSelectBox
                          name="icon"
                          className="menu_icon_select"
                          optionListClassName="menu_icon_select_option_list"
                          options={iconOptions}
                        />
                      ),
                  },
                ]}
              />
              <TableBodyRow
                rowData={[
                  {
                    title: '사용 여부',
                    contents:
                      type === 'detail' ? (
                        menuDetail?.useYn === 'Y' ? (
                          '사용'
                        ) : (
                          '미사용'
                        )
                      ) : (
                        <FormSelectBox
                          name="useYn"
                          options={[
                            { label: '사용', value: 'Y' },
                            { label: '미사용', value: 'N' },
                          ]}
                        />
                      ),
                  },
                  {
                    title: '삭제 가능 여부',
                    required: true,
                    contents:
                      type === 'detail' ? (
                        menuDetail?.delAbleYn === 'Y' ? (
                          '가능'
                        ) : (
                          '불가능'
                        )
                      ) : (
                        <FormSelectBox
                          name="delAbleYn"
                          options={[
                            { label: '가능', value: 'Y' },
                            { label: '불가능', value: 'N' },
                          ]}
                        />
                      ),
                  },
                  // {
                  //   title: '페이지 권한',
                  //   // required: true,
                  //   contents:
                  //     type === 'detail' ? (
                  //       menuDetail?.delAbleYn === 'Y' ? (
                  //         '가능'
                  //       ) : (
                  //         '불가능'
                  //       )
                  //     ) : (
                  //       <FormSelectBox
                  //         name="delAbleYn"
                  //         options={[
                  //           { label: '가능', value: 'Y' },
                  //           { label: '불가능', value: 'N' },
                  //         ]}
                  //       />
                  //     ),
                  // },
                ]}
              />

              <TableBodyRow
                rowData={{
                  title: '메뉴 설명',
                  isFullWidth: true,
                  contents:
                    type === 'detail' ? (
                      menuDetail?.desc || ''
                    ) : (
                      <FormTextarea name="desc" placeholder="메뉴 설명을 입력해 주세요" />
                    ),
                }}
              />
              {type === 'detail' && (
                <>
                  <TableBodyRow
                    rowData={[
                      {
                        title: '최초 등록자',
                        contents: menuDetail?.dateInfo.createUser,
                      },
                      {
                        title: '생성 일시',
                        contents: menuDetail?.dateInfo.createDate,
                      },
                    ]}
                  />
                  <TableBodyRow
                    rowData={[
                      {
                        title: '최종 수정자',
                        contents: menuDetail?.dateInfo.updateUser,
                      },
                      {
                        title: '수정 일시',
                        contents: menuDetail?.dateInfo.updateDate,
                      },
                    ]}
                  />
                </>
              )}
            </TableBody>
          </TableContainer>
        </Form>
      </div>
      {type === 'detail' && (
        <div className="admin_menu_info_child_menu">
          <TableContainerHeader
            leftChildren={<SubTitle>하위 페이지 목록</SubTitle>}
            // rightChildren={
            //   <>
            //     <Button
            //       text="행 삭제"
            //       color="red"
            //       disabled={selectedRows.length === 0}
            //       onClick={handleDeletePop}
            //       clickLog={{ buttonSection: '하위 메뉴 목록' }}
            //     />
            //     <Button text="행 추가" onClick={handleAddRow} clickLog={{ buttonSection: '하위 메뉴 목록' }} />
            //     <Button
            //       text="하위 메뉴 저장"
            //       onClick={handleSaveChildMenu}
            //       disabled={isSameData}
            //       clickLog={{ buttonSection: '하위 메뉴 목록' }}
            //     />
            //   </>
            // }
          />
          <Grid
            sizeReset
            autoSizeStrategy={'onGridSizeChanged'}
            placeholder="데이터가 없습니다. 하위 페이지를 추가해주세요."
            ref={gridRef}
            columns={columnDefs}
            rowData={rowData}
            defaultColDef={{
              width: 170,
              resizable: true,
            }}
            gridOptions={
              {
                // rowDragManaged: true,
                // onRowDragEnd: onRowDragEnd,
                // onCellValueChanged: onCellValueChanged,
                // onRowSelected: onRowSelected,
              }
            }
          />
        </div>
      )}
    </>
  ) : (
    <>
      <SubTitle>메뉴 정보</SubTitle>
      <div className="no_result">
        메뉴가 선택되지 않았습니다. <br />
        메뉴를 선택해주세요.
      </div>
    </>
  );
};

export default MenuDetail;
