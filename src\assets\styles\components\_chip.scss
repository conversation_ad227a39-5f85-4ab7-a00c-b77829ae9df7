@use '@styles/utils/mixin' as m;

.c_chip {
  @include m.inline_flex(center, space-between);
  gap: 0.5rem;
  padding: 0 1rem;
  max-width: 10rem;
  height: 36px;
  border-radius: 50rem;
  cursor: pointer;
  transition: all 0.3s;

  span {
    @include m.ellipsis;
  }

  .chip_img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .c_button_icon.i_close {
    margin: 0 !important;
    padding: 0 !important;
    width: 20px !important;
    height: 20px !important;
    &.filled {
      background-color: var(--g_05);
    }
  }

  &_input,
  &_action {
    background-color: var(--g_03);
    &:hover {
      background-color: var(--g_04);
    }
  }

  &_input {
    padding: 0 0.5rem 0 1rem;
    &.with_img {
      padding: 0 0.5rem;
    }
  }

  &_action {
    &.with_img {
      padding: 0 0.5rem;
    }
    &.active {
      color: var(--font_white);
      background-color: var(--p_05);
      &:hover {
        background-color: var(--p_04);
      }
    }
  }

  &_filter,
  &_choice {
    border: 1.5px solid var(--g_06);
    color: var(--g_06);
    background-color: transparent;

    &:hover {
      filter: brightness(0.5);
    }

    &.active {
      border: 2px solid var(--p_05);
      color: var(--p_05);
      &:hover {
        filter: brightness(1.3);
      }
    }
  }

  &_filter {
    max-width: none;
    &.active {
      span {
        width: calc(100% - 1.75rem);
      }
      &::after {
        @include m.content('@assets/images/icon/icon_checked_primary.svg', 20px, 20px);
      }
    }
  }

  &_choice {
    padding: 0 0.5rem;
    &::before {
      @include m.content('@assets/images/icon/icon_checked_black.svg', 20px, 20px);
    }
    span {
      width: calc(100% - 1.75rem);
    }

    &.active::before {
      @include m.content('@assets/images/icon/icon_checked_primary.svg', 20px, 20px);
    }
  }
}
