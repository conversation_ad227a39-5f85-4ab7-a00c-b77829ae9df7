@use '@styles/utils/mixin' as m;

.my_info {
  .my_info_contents {
    .c_table_wrapper {
      .c_table {
        tbody {
          tr {
            .my_name_cell {
              .c_table_cell_text {
                display: flex;
                flex-direction: row;
                gap: 1rem;
                align-items: flex-start;
              }
            }

            .password_change_cell {
              display: flex;
              flex-direction: column;
              gap: 1rem;

              .c_table_cell_text {
                .password_input,
                .password_confirm {
                  @include m.flex(center);
                  gap: 0.25rem;
                }
              }
            }
          }
        }
      }
    }

    .my_info_save_btn {
      margin-top: 1rem;
      float: right;
    }
  }
}

.my_info_update {
  .c_modal_body {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;

    div {
      p {
        text-align: left;
      }
      ul {
        margin-left: 1rem;
        text-align: left;
        list-style-type: disc;
      }
    }
  }
}
