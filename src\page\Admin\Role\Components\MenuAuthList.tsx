import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { MenuData } from '@page/Admin/Role/type';
import CheckboxTree from 'react-checkbox-tree';
import 'react-checkbox-tree/src/scss/react-checkbox-tree.scss';

interface MenuAuthListProps {
  menuListData: MenuData[];
  checkedList: number[];
  setCheckedList: Dispatch<SetStateAction<number[]>>;
}

const MenuAuthList = ({ menuListData, checkedList, setCheckedList }: MenuAuthListProps) => {
  const [menuData, setMenuData] = useState([]);
  const [pMenuId, setPMenuId] = useState([]);

  const getParentNodes = (nodes) => {
    const map = {};
    const dfs = (node) => {
      if (node.children) {
        node.children.forEach((child) => {
          map[child.value] = node.value;
          dfs(child);
        });
      }
    };
    nodes.forEach(dfs);
    return map;
  };

  const handleCheck = (newChecked) => {
    const allChecked = new Set(newChecked.map(String));
    const parentMap = getParentNodes(menuData);
    // 부모 추가: 자식이 하나라도 체크되어 있으면 부모도 포함
    Object.entries(parentMap).forEach(([child, parent]) => {
      if (allChecked.has(child)) {
        allChecked.add(parent);
      }
    });
    setCheckedList(Array.from(allChecked).map(Number));
  };

  // 1depth 메뉴 리스트
  useEffect(() => {
    if (menuListData) {
      const pMenuIds: string[] = [];

      const menuList = menuListData.map((menu) => {
        pMenuIds.push(String(menu.id));

        return {
          className: 'depth1_menu',
          label: menu.name,
          value: menu.id,
          children: menu.childs.map((child) => {
            pMenuIds.push(String(child.id));
            return {
              className: 'depth2_menu',
              label: child.name,
              value: child.id,
              children: child.childs.map((bChild) => {
                return { className: 'depth3_menu', label: bChild.name, value: bChild.id };
              }),
            };
          }),
        };
      });

      setMenuData(menuList);
      setPMenuId(pMenuIds);
    }
  }, [menuListData]);

  return (
    <div className="menu_auth_contents">
      <CheckboxTree
        nodes={menuData}
        checked={checkedList.map(String)}
        // onCheck={(checked) => setCheckedList(checked.map(Number))}
        onCheck={handleCheck}
        expanded={pMenuId}
        onExpand={(expanded) => setPMenuId(expanded)}
        checkModel="all"
      />
    </div>
  );
};

export default MenuAuthList;
