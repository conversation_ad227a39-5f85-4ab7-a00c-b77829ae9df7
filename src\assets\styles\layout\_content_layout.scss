@use '@styles/utils/mixin' as m;

.content {
  .left_content {
    width: 20%;
    padding: 0 1rem 1rem 0;

    .i_close {
      @include m.bp_large() {
        width: 28px;
        height: 28px;
      }
      
      @include m.bp_medium() {
        width: 24px;
        height: 24px;
      }
    }
  }
  .right_content {
    width: 80%;
    padding: 0 0 0 1rem;
    border-left: 1px solid var(--g_06);
  }

  &.reverse {
    .left_content {
      width: 80%;
    }
    .right_content {
      width: 20%;
    }
  } 

  .left_content,
  .right_content {
    .c_table_container_header {
      width: 100%;
      min-height: 33px;
    }
    .c_table_wrapper {
      width: 100%;
      .c_table {
        width: 100%;
      }
    }
  }
}

.vertical {
  @include m.flex(start, start, column);
  gap: 0.625rem;
}

.horizontal {
  @include m.flex();
}
