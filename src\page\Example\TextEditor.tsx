import Button from '@components/common/Button/Button';
import TextEditor from '@components/common/TextEditor/TextEditor';
import Title from '@components/common/Title/Title';
import { useCurrentEditor } from '@tiptap/react';
import React, { useEffect, useState } from 'react';

const TextEditorExample = () => {
  const [content, setContent] =
    useState(`<div contenteditable="false" role="textbox" translate="no" class="tiptap ProseMirror"><h1 style="">Heading1</h1><h2 style="">Heading2</h2><h3 style="">Heading3</h3><h4 style="">Heading4</h4><h5 style="">Heading5</h5><h6 style="">Heading6</h6><p style=""><br class="ProseMirror-trailingBreak"></p><p style=""><strong>글씨</strong>에 <em>효과</em>를 <s>주고</s> <u>싶어요</u>.</p><p style=""><span style="color: rgb(255, 255, 255); font-size: 30px"><mark data-color="#8c1aff" style="background-color: #8c1aff; color: inherit">색</mark></span><span style="color: rgb(179, 255, 179); font-size: 30px"><mark data-color="#ffb3b3" style="background-color: #ffb3b3; color: inherit">칠</mark></span><span style="font-size: 30px">도 하고 <mark data-color="#3399ff" style="background-color: #3399ff; color: inherit">싶어요.</mark></span> <span style="font-size: 22px">( </span><span style="font-size: 42px"><strong>폰트</strong></span><span style="font-size: 22px"><strong> </strong></span><span style="font-size: 30px"><u>사이즈</u></span><span style="font-size: 22px">도 </span><span style="font-size: 16px">변경</span><span style="font-size: 30px"><em>할</em></span><span style="font-size: 40px"><em> 수</em></span><span style="font-size: 22px"> 있어요. )</span></p><blockquote><p style="">인용구도 가능하구요</p></blockquote><pre><code class="language-java"><span class="hljs-comment">// Java </span>

<span class="hljs-keyword">public</span> java 

<span class="hljs-literal">null</span> ....</code></pre><pre><code class="language-javascript"><span class="hljs-comment">// Javascript</span>

<span class="hljs-keyword">const</span> js = <span class="hljs-string">'javascript'</span>;
<span class="hljs-keyword">let</span> result = js + <span class="hljs-string">'editor'</span>;

<span class="hljs-variable language_">console</span>.<span class="hljs-title function_">log</span>(result);</code></pre><figure style="width: 112px; height: auto; max-width: 80vw; margin: 0px auto;" class="" id=""><figure style="width: 112px; height: auto; max-width: 80vw; margin: 0px auto;" contenteditable="false" draggable="true"><img src="https://cameleon1.synology.me/blue_bottle/img/main_section/main_right.jpg" alt="이미지" title="이미지" style="width: 112px; height: auto; max-width: 80vw; margin: 0px auto;" caption=""><figcaption style="color: #9c9c9c; font-size: 12px; margin-top: 4px;"></figcaption></figure><figcaption style="color: #9c9c9c; font-size: 12px; margin-top: 4px;" class="image-caption" id="" textcontent="주석을 달아줘요">주석을 달아줘요</figcaption></figure><p style=""><br class="ProseMirror-trailingBreak"></p><p style=""><figure style="max-width: 80vw; position: relative; cursor: pointer; margin: 0px 0px 0px auto; width: 401px; height: 226px;" contenteditable="false" draggable="true"><iframe src="https://www.youtube-nocookie.com/embed/FRxcsTxSI18?si=YqNyEYp_uDYO3DQW" start="0" width="401" height="226" caption="주석을달아요~"></iframe><figcaption style="color: #9c9c9c; font-size: 12px; padding: 0.25rem 0.625rem">주석을달아요~</figcaption></figure><img class="ProseMirror-separator" alt=""><br class="ProseMirror-trailingBreak"></p><p style=""><br class="ProseMirror-trailingBreak"></p><p style=""><br class="ProseMirror-trailingBreak"></p><figure style="width: 359px; height: auto;" contenteditable="false" draggable="true"><video src="https://cameleon1.synology.me/blue_bottle/video/bluebottle.mp4" width="359" height="320" muted="true" playsinline="true" autoplay="true" controls="true" style="width: 359px; height: auto;" caption="주석을 달아주세요"></video><figcaption style="color: #9c9c9c; font-size: 12px; margin-top: 4px;">주석을 달아주세요</figcaption></figure><p style=""><br class="ProseMirror-trailingBreak"></p><table style="min-width: 581px"><colgroup><col style="min-width: 25px"><col style="width: 531px"><col style="min-width: 25px"></colgroup><tbody><tr><th colspan="1" rowspan="1"><p style=""><br class="ProseMirror-trailingBreak"></p></th><th colspan="1" rowspan="1" colwidth="531"><p style="">Col 헤더</p></th><th colspan="1" rowspan="1"><p style="">Col 헤더</p></th></tr><tr><th colspan="1" rowspan="1"><p style="">Row 헤더</p></th><td colspan="1" rowspan="1" colwidth="531"><table style="min-width: 222px"><colgroup><col style="width: 172px"><col style="min-width: 25px"><col style="min-width: 25px"></colgroup><tbody><tr><th colspan="1" rowspan="1" colwidth="172"><p style="">겹 테이블</p></th><th colspan="1" rowspan="1"><p style=""><br class="ProseMirror-trailingBreak"></p></th><th colspan="1" rowspan="1"><p style="">ㅇㅇ</p></th></tr><tr><td colspan="1" rowspan="1" colwidth="172"><p style="">ㅇㅇㅇ</p></td><td colspan="1" rowspan="2"><p style="">ㅇㅇ</p><p style="">ㅇ</p></td><td colspan="1" rowspan="1"><p style="">ㅇ</p></td></tr><tr><td colspan="1" rowspan="1" colwidth="172"><p style=""><br class="ProseMirror-trailingBreak"></p></td><td colspan="1" rowspan="1"><p style="">ㅇ</p></td></tr></tbody></table></td><td colspan="1" rowspan="1"><ol><li><p style="">체크리스트</p></li><li><p style="">체크</p></li></ol></td></tr><tr><th colspan="1" rowspan="1"><p style="">Row 헤더</p></th><td colspan="2" rowspan="1" colwidth="531,0"><p style="">병합</p></td></tr></tbody></table><p style=""><br class="ProseMirror-trailingBreak"></p><ol><li><p style="">체크리스트</p><ol><li><p style="">안으로</p></li><li><p style="">들어가서</p></li></ol></li><li><p style="">다시 나오고</p></li></ol><p style=""><br class="ProseMirror-trailingBreak"></p><ul><li><p style="">이것도</p><ul><li><p style="">가능합니다</p></li></ul></li><li><p style="">당연히</p><ul><li><p style="">되는거에요</p><ul><li><p style="">그럼요</p></li></ul></li></ul></li><li><p style="">됩니다.</p></li></ul><p style=""><br class="ProseMirror-trailingBreak"></p><ul data-type="taskList"><li class="c_checkbox" data-checked="false"><label contenteditable="false"><input type="checkbox"><span></span></label><div><p style="">체크리스트도 가능합니다.</p></div></li><li class="c_checkbox" data-checked="true"><label contenteditable="false"><input type="checkbox"><span></span></label><div><p style="">당연히 되는데요</p></div></li></ul><p style=""><br class="ProseMirror-trailingBreak"></p><p style="">링크도 당연히 되는거에요: <a target="_blank" rel="noopener noreferrer nofollow" href="http://google.com">http://google.com</a></p><p style=""><br class="ProseMirror-trailingBreak"></p></div>`);
  const [isSaved, setIsSaved] = useState(false);

  const handleSave = () => {
    setIsSaved(true);
  };

  return (
    <div className="w-full h-full">
      <h1 className="c_title">
        <p className="c_title_text">에디터</p>
      </h1>
      <TextEditor content={content} onChange={setContent} />
      <Button text="저장" onClick={handleSave} className="mt-2" />

      {isSaved && <TextEditor content={content} onChange={setContent} readOnly />}
    </div>
  );
};

export default TextEditorExample;
