@use '@styles/utils/mixin' as m;

.c_error_page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100vw;
  background-color: #f0f0f0;

  .error_content {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .error_image {
      width: 100px;
      height: 100px;
      background-image: url('@assets/images/icon/icon_warning.svg');
      background-size: contain;
      background-repeat: no-repeat;
      margin-bottom: 1rem;
    }

    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #333;
      margin-bottom: 1rem;
    }

    .button_wrapper {
      margin-top: 1rem;
      @include m.flex(center, center);
      gap: 0.25rem;
    }
  }
}
