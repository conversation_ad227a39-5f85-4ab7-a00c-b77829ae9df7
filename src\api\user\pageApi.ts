import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';
import type { Page, PageDTO } from '@page/Admin/Page/type';

const isGetErrorMsg = false;

/**
 * 페이지 목록을 조회하는 API 함수
 */
export const getPageList = async (params = null) => {
  const queryString = utils.buildQueryString(params);

  try {
    const response = await api.get(`/api-admin/admin/group-pages/paging${queryString}`);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 목록 조회');
  }
};

/**
 * 페이지 상세를 조회하는 API 함수
 */
export const getPageDetail = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/admin/group-pages/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 상세 조회');
  }
};

/**
 * 게시판옵션을 조회하는 API 함수
 */
export const getTemplateOptions = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/bbs-posts/templets`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 목록 조회');
  }
};

/**
 * 페이지정보 CRUD
 */

export const createPage = async (data: PageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 생성');
  }
};

export const updatePage = async (pageId, data: PageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages/${pageId}`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 수정');
  }
};

export const deletePage = async (pageId: Page['id']) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages/${pageId}/delete`);
    return response.data.message;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '페이지 삭제');
  }
};

/**
 * 하위페이지 목록을 조회하는 API 함수
 */
export const getSubPagesList = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/admin/pages/group/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 하위 목록 조회');
  }
};

/**
 * 그룹 코드 상세 정보를 조회하는 API 함수 (검색 조건 미사용)
 * @param code 조회할 그룹 코드
 */
interface GetGroupCodeDetailProps {
  code: string;
  isGetErrorMsg?: boolean;
}

/**
 * 그룹 코드 삭제 API
 * @param code 삭제할 그룹 코드
 */

interface DeleteGroupCodeParams {
  code: string;
  isGetErrorMsg?: boolean;
}

const deleteGroupCode = async ({ code, isGetErrorMsg = false }: DeleteGroupCodeParams) => {
  try {
    const response = await api.post(`/api-admin/group-codes/${code}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 삭제');
  }
};

/**
 * 그룹 코드 추가 API
 * @param formData 추가할 그룹 코드 데이터
 */

interface AddGroupCodeProps {
  formData: any;
  isGetErrorMsg?: boolean;
}

const addGroupCode = async ({ formData, isGetErrorMsg = false }: AddGroupCodeProps) => {
  try {
    const response = await api.post('/api-admin/group-codes', formData);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 추가');
  }
};

/**
 * 그룹 코드 수정 API
 * @param code 수정할 그룹 코드
 * @param formData 수정할 데이터
 */

interface EditGroupCodeProps {
  code: string;
  formData: any;
  isGetErrorMsg?: boolean;
}

const editGroupCode = async ({ code, formData, isGetErrorMsg = false }: EditGroupCodeProps) => {
  try {
    const response = await api.post(`/api-admin/group-codes/${code}`, formData);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 수정');
  }
};

/**
 * 코드 추가 API
 * @param data 추가할 코드 데이터
 */

interface PostCodeProps {
  data: any;
  isGetErrorMsg?: boolean;
}

const postCode = async ({ data, isGetErrorMsg = false }: PostCodeProps) => {
  try {
    const response = await api.post('/api-admin/codes', data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '코드 추가 및 수정');
  }
};

/**
 * 코드 삭제 API
 * @param code 삭제할 코드
 */

interface DeleteCodeProps {
  data: {
    groupCode: string;
    codes: Array<string | number>;
  };
  isGetErrorMsg?: boolean;
}

const deleteCode = async ({ data, isGetErrorMsg = false }: DeleteCodeProps) => {
  try {
    const response = await api.post('/api-admin/codes/delete', { ...data });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '코드 삭제');
  }
};
