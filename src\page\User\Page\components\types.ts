/**
 * 코드 상세 정보를 위한 인터페이스
 */
export interface CodeDetailProps {
  code: string; // 코드
  name: string; // 코드명
  sort: number; // 정렬 순서
  useYn: string; // 사용 여부
  groupCode: string; // 그룹 코드
  dateInfo: {
    createUser: string; // 등록자
    createDate: string; // 등록일자
    updateUser: string; // 수정자
    updateDate: string; // 수정일자
  };
}

/**
 * 폼 입력값을 위한 인터페이스
 */
export interface FormValues {
  code: string; // 코드
  groupCode: { label: string; value: string } | null; // 그룹 코드 (라벨과 값)
  name: string; // 코드명
  sort: number | string | null; // 정렬 순서
  useYn: { label: string; value: string } | null; // 사용 여부 (라벨과 값)
}

/**
 * 확인 모달 상태를 위한 인터페이스
 */
export interface ConfirmStateType {
  isOpen: boolean; // 모달 열림 여부
  confirmType: 'add' | 'edit' | 'delete' | null; // 확인 유형 (추가/수정/삭제)
  content: string | React.ReactNode; // 모달 내용
  onCancel: () => void; // 취소 버튼 클릭 시 실행될 함수
  onConfirm: (() => void) | ((data: any) => void); // 확인 버튼 클릭 시 실행될 함수
}

/**
 * 알림 모달 내용을 위한 인터페이스
 */
export interface AlertContentsType {
  isOpen: boolean;
  content: string;
  onConfirm: () => void;
} 