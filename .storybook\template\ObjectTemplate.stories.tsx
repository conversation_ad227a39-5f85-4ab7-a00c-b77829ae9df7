/**
 * Story 객체 방식 (CSF 3, 최신)
 *  정적인 스타일 테스트, <PERSON>lt<PERSON>, Avatar, Badge 같은 단순 렌더링 컴포넌트에 적합
 */

import type { Meta, StoryObj } from '@storybook/react';

// @ts-expect-error: 템플릿용 임시 컴포넌트
import YourComponent from './YourComponent';

// 문서 참고용 타입 임포트
import type { CustomStoryMeta } from './storybook-meta-config.types';

/**
 * Meta 설정입니다. 자세한 옵션 설명은 {@link CustomStoryMeta} 참고
 */
const meta: Meta<typeof YourComponent> = {
  title: 'Components/YourComponent',
  component: YourComponent,
  parameters: {
    layout: 'centered',
  },
  args: {},
};

export default meta;

type Story = StoryObj<typeof YourComponent>;

export const Defalut: Story = {};

export const Options: Story = {
  args: {},
};
