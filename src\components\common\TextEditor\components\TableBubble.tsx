import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';
import { BubbleMenu, useCurrentEditor } from '@tiptap/react';
import React from 'react';

const TableBubble = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const handleAddColBefore = () => {
    editor.chain().focus().addColumnBefore().run();
  };

  const handleAddColAfter = () => {
    editor.chain().focus().addColumnAfter().run();
  };

  const handleAddRowBefore = () => {
    editor.chain().focus().addRowBefore().run();
  };

  const handleAddRowAfter = () => {
    editor.chain().focus().addRowAfter().run();
  };

  const handleMerge = () => {
    editor.commands.mergeOrSplit();
  };

  const handleDeleteTable = () => {
    editor.commands.deleteTable();
  };

  const handleDeleteRow = () => {
    editor.commands.deleteRow();
  };

  const handleDeleteCol = () => {
    editor.commands.deleteColumn();
  };

  const handleToggleRowHeader = () => {
    editor.commands.toggleHeaderRow();
  };

  const handleToggleColHeader = () => {
    editor.commands.toggleHeaderColumn();
  };

  const handleBackground = () => {
    editor.chain().focus().setCellAttribute('background-color', 'red !important').run();
  }

  return (
    <BubbleMenu
      editor={editor}
      className="c_table_bubble"
      shouldShow={({ editor }) => {
        return editor.isActive('table') && editor.isEditable;
      }}
    >
      <div className="add_btn_group">
        {editor.can().addRowBefore() && (
          <IconButton
            icon={'table_add_row_above' as IconType}
            iconOnly
            text="위에 행 추가"
            color="grayscale"
            fill="unfilled"
            onClick={handleAddRowBefore}
          />
        )}
        {editor.can().addRowAfter() && (
          <IconButton
            icon={'table_add_row_below' as IconType}
            iconOnly
            text="아래에 행 추가"
            color="grayscale"
            fill="unfilled"
            onClick={handleAddRowAfter}
          />
        )}
        {editor.can().addColumnBefore() && (
          <IconButton
            icon={'table_add_col_left' as IconType}
            iconOnly
            text="왼쪽에 열 추가"
            color="grayscale"
            fill="unfilled"
            onClick={handleAddColBefore}
          />
        )}
        {editor.can().addColumnAfter() && (
          <IconButton
            icon={'table_add_col_right' as IconType}
            iconOnly
            text="오른쪽에 열 추가"
            color="grayscale"
            fill="unfilled"
            onClick={handleAddColAfter}
          />
        )}
      </div>

      {editor.can().mergeOrSplit() && (
        <IconButton
          icon={'table_merge' as IconType}
          iconOnly
          text="테이블 병합"
          color={
            editor.getAttributes('tableCell').rowspan > 1 || editor.getAttributes('tableCell').colspan > 1
              ? 'primary'
              : 'grayscale'
          }
          fill='unfilled'
          onClick={handleMerge}
        />
      )}

      <div className="delete_btn_group">
        <IconButton
          icon={'table_delete_col' as IconType}
          iconOnly
          text="테이블 삭제"
          color="grayscale"
          fill="unfilled"
          onClick={handleDeleteCol}
        />
        <IconButton
          icon={'table_delete_row' as IconType}
          iconOnly
          text="테이블 삭제"
          color="grayscale"
          fill="unfilled"
          onClick={handleDeleteRow}
        />
      </div>

      <div className="header_toggle_btn_group">
        <IconButton
          icon={'table_row_header' as IconType}
          iconOnly
          text="행 헤더 토글"
          color="grayscale"
          fill="unfilled"
          onClick={handleToggleColHeader}
        />

        <IconButton
          icon={'table_col_header' as IconType}
          iconOnly
          text="열 헤더 토글"
          color="grayscale"
          fill="unfilled"
          onClick={handleToggleRowHeader}
        />
      </div>

      <IconButton
        icon="delete"
        iconOnly
        text="테이블 삭제"
        color="grayscale"
        fill="unfilled"
        onClick={handleDeleteTable}
      />
    </BubbleMenu>
  );
};

export default TableBubble;
