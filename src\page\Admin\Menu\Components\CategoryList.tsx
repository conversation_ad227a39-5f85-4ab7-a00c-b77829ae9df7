import { useEffect, useState } from 'react';
import { addMenu, changeMenuSort, getAdminMenus } from '@api/admin/menuManageApi';

import Button from '@components/common/Button/Button';
import SubTitle from '@components/common/Title/SubTitle';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';

import { useSaveMenuStore } from '@store/useSaveMenuStore';
import { useAlertStore } from '@store/useAlertStore';
import joinClassName from '@utils/joinClassName';

import { DragDropContext, Draggable, Droppable } from '@hello-pangea/dnd';

interface Props {
  clickedMenuId: number;
  handleClickedMenuId: React.Dispatch<React.SetStateAction<number>>;
}

const CategoryList = ({ clickedMenuId, handleClickedMenuId }: Props) => {
  const [menus, setMenus] = useState([]);

  const { setAlertState, initAlertState } = useAlertStore();

  const [hasHandling, setHasHandling] = useState<boolean>(false); // 한번이라도 순서를 바꿨는가
  const { isSaved, setIsSaved } = useSaveMenuStore(); // 메뉴 변경사항이 저장되었는가

  // 메뉴 수정 activeToggle 기능 정의 및 개발 필요
  const [isPossibleChange, setIsPossibleChange] = useState<boolean>(false); // 메뉴 변경 가능한가

  /**관리자 메뉴 호출 */
  const getMenus = async () => {
    const response = await getAdminMenus({});
    setMenus(response);
  };

  useEffect(() => {
    getMenus();
  }, []);

  useEffect(() => {
    if (isSaved) {
      // 메뉴 변경사항 있을 시 menu name 갱신
      getMenus();
      setIsSaved(false);
    }
  }, [isSaved]);

  /**관리자 메뉴 순서 변경 */
  const handleCompleteChange = async () => {
    const response = await changeMenuSort({ menus });

    handleAlertOpen(response.message, () => {
      getMenus();
      setIsPossibleChange(false);
    });
  };

  /**관리자 메뉴 추가 */
  const handleAddCategory = async () => {
    const response = await addMenu({});
    const { data } = response;

    const { id, name } = data;
    setMenus([...menus, { id, name, childs: [] }]);
  };

  const handleAlertOpen = (contents, plusFc?) => {
    setAlertState({
      title: '알람',
      isOpen: true,
      content: contents,
      onConfirm: () => {
        initAlertState();
        plusFc && plusFc(); // 알림 후 추가 함수 실행
      },
    });
  };

  //  --------------------------------------------------------------------------

  // 메뉴 클릭 이벤트(id 전달)
  const handleClickMenu = (menuId) => {
    handleClickedMenuId(menuId);
  };

  // 메뉴의 순서 변경
  const reorderMenus = (menus, sourceIndex, destinationIndex) => {
    const reorderedMenus = Array.from(menus);
    const [removed] = reorderedMenus.splice(sourceIndex, 1);
    reorderedMenus.splice(destinationIndex, 0, removed);

    return reorderedMenus;
  };

  // 서로 다른 부모 사이에 자식 메뉴를 이동
  const moveChildMenu = (menus, sourceIndex, destinationIndex, sourcePMenuId, destinationPMenuId) => {
    let removed;

    const removedMenus = menus.map((menu) => {
      if (menu.id === sourcePMenuId) {
        const copyMenus = [...menu.childs];
        [removed] = copyMenus.splice(sourceIndex, 1);
        return { ...menu, childs: copyMenus };
      }
      return menu;
    });

    const reorderedMenus = removedMenus.map((menu) => {
      if (menu.id === destinationPMenuId && removed) {
        menu.childs.splice(destinationIndex, 0, removed);
        return menu;
      }
      return menu;
    });

    return reorderedMenus;
  };

  // DND 핸들링 함수
  const handleOnDragEnd = (result) => {
    setHasHandling(true);

    const { combine, source, draggableId, destination, type } = result;

    if (!combine && destination) {
      const sourceId = source.droppableId;
      const destinationId = destination.droppableId;

      // CASE 1:: 부모 간의 위치 이동
      if (type === 'top-level' && sourceId === destinationId) {
        return setMenus(reorderMenus(menus, source.index, destination.index));
      }

      // CASE 2:: 부모 내의 자식 간의 위치 이동
      if (type === 'nested' && sourceId === destinationId) {
        const pMenuId = parseInt(sourceId.split('-')[1]);

        return setMenus(
          menus.map((menu) => {
            if (menu.id === pMenuId) {
              const copyMenus = [...menu.childs];
              const [removed] = copyMenus.splice(source.index, 1);
              copyMenus.splice(destination.index, 0, removed);
              return { ...menu, childs: copyMenus };
            }
            return menu;
          })
        );
      }

      // CASE 3:: 자식 메뉴가 다른 부모의 하위 메뉴로 이동
      if (sourceId !== destinationId && type === 'nested') {
        const sourcePMenuId = parseInt(sourceId.split('-')[1]);
        const destinationPMenuId = parseInt(destinationId.split('-')[1]);

        return setMenus(moveChildMenu(menus, source.index, destination.index, sourcePMenuId, destinationPMenuId));
      }
    }

    // CASE 4:: 부모 메뉴가 다른 부모의 하위 메뉴로 이동
    if (combine) {
      const copyMenus = Array.from(menus);
      const [removed] = copyMenus.splice(source.index, 1);
      const pMenuId = parseInt(combine.draggableId.split('-')[1]);

      const reorderedMenus = copyMenus.map((menu) => {
        if (menu.id === pMenuId) {
          menu.childs.push(removed);
          return menu;
        }
        return menu;
      });

      return setMenus(reorderedMenus);
    }

    // CASE 5:: 자식 메뉴가 독립적인 부모 메뉴로 이동
    if (!combine && !destination) {
      const isChildMenu = draggableId.split('-')[0] === 'c';

      if (isChildMenu && type === 'nested') {
        const pMenuId = parseInt(source.droppableId.split('-')[1]);
        const cMenuId = parseInt(draggableId.split('-')[1]);
        let removed;

        const removedMenus = menus.map((menu) => {
          if (menu.id === pMenuId) {
            const copyMenus = [...menu.childs];
            const childIndex = copyMenus.findIndex((child) => child.id === cMenuId);
            [removed] = copyMenus.splice(childIndex, 1);
            return { ...menu, childs: copyMenus };
          }
          return menu;
        });

        removedMenus.push(removed); // 가장 마지막 메뉴로 빠지도록
        return setMenus(removedMenus);
      }
    }
  };

  return (
    <>
      <div className="admin_menu_category_wrap">
        <TableContainerHeader
          leftChildren={<SubTitle>메뉴 목록</SubTitle>}
          rightChildren={
            <div className="admin_menu_button">
              {isPossibleChange ? (
                <>
                  {/* <Button text="삭제" onClick={handleRelatedPageDeletePop} color="red" /> */}
                  <Button text="메뉴 추가" onClick={handleAddCategory} clickLog={{ buttonSection: '메뉴 목록' }} />
                  <Button
                    text="편집 완료"
                    onClick={hasHandling ? handleCompleteChange : () => setIsPossibleChange(false)}
                    clickLog={{ buttonSection: '메뉴 목록' }}
                  />
                </>
              ) : (
                <Button
                  text="메뉴 편집"
                  onClick={() => {
                    handleClickMenu(null);
                    setIsPossibleChange(true);
                  }}
                  clickLog={{ buttonSection: '메뉴 목록' }}
                />
              )}
            </div>
          }
        />
        <div className="admin_menu_category">
          {isPossibleChange ? (
            <DragDropContext onDragEnd={handleOnDragEnd}>
              <Droppable droppableId="top-level" type="top-level" isCombineEnabled>
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef} className="admin_menu_category_top">
                    {menus.map((pMenu, pIndex) => (
                      <Draggable key={pMenu.id} draggableId={`p-${pMenu.id}`} index={pIndex}>
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="admin_menu_category_parent_wrap"
                            style={{
                              ...provided.draggableProps.style,
                            }}
                          >
                            <div
                              {...provided.dragHandleProps}
                              className={joinClassName(
                                'admin_menu_category_parent',
                                pMenu.id === clickedMenuId && 'clicked'
                              )}
                              // onClick={() => handleClickMenu(pMenu.id)}
                            >
                              {pMenu.useYn == 'N' ? pMenu.name + ' (미사용)' : pMenu.name}
                            </div>

                            {/* 자식메뉴 */}
                            <Droppable droppableId={`p-${pMenu.id}`} type="nested">
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.droppableProps}
                                  className="admin_menu_category_child_wrap"
                                >
                                  {pMenu.childs.map((cMenu, cIndex) => (
                                    <Draggable key={cMenu.id} draggableId={`c-${cMenu.id}`} index={cIndex}>
                                      {(provided) => (
                                        <div
                                          ref={provided.innerRef}
                                          {...provided.draggableProps}
                                          {...provided.dragHandleProps}
                                          style={{
                                            ...provided.draggableProps.style,
                                          }}
                                          className={joinClassName(
                                            'admin_menu_category_child',
                                            cMenu.id === clickedMenuId && 'clicked'
                                          )}
                                          // onClick={() => handleClickMenu(cMenu.id)}
                                        >
                                          {cMenu.useYn == 'N' ? cMenu.name + ' (미사용)' : cMenu.name}
                                        </div>
                                      )}
                                    </Draggable>
                                  ))}
                                  {provided.placeholder}
                                </div>
                              )}
                            </Droppable>
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          ) : (
            <div className="admin_menu_category_top no_dnd">
              {menus.map((pMenu) => (
                <div className="admin_menu_category_parent_wrap" key={pMenu.id}>
                  <div
                    className={joinClassName(
                      'admin_menu_category_parent',
                      pMenu.icon ? `i_${pMenu.icon}` : 'i_arrow_right',
                      pMenu.id === clickedMenuId && 'clicked'
                    )}
                    onClick={() => handleClickMenu(pMenu.id)}
                  >
                    {pMenu.name}
                  </div>
                  <div className="admin_menu_category_child_wrap">
                    {pMenu.childs.map((cMenu) => (
                      <div
                        key={cMenu.id}
                        className={joinClassName(
                          'admin_menu_category_child',
                          cMenu.icon ? `i_${cMenu.icon}` : 'i_arrow_right',
                          cMenu.id === clickedMenuId && 'clicked'
                        )}
                        onClick={() => handleClickMenu(cMenu.id)}
                      >
                        {cMenu.name}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default CategoryList;
