import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useLoadingStore } from '@store/useLoadingStore';
import { AxiosError } from 'axios';

const activeErrorAlert = (message: string, onConfirm?: () => void) => {
  useAlertStore.getState().activeAlert(message, onConfirm);
};

// 토큰 만료 시 처리
const handleTokenExpired = () => {
  localStorage.removeItem('accessToken');
  window.location.href = '/login';
};

// error 의 타입이 AxiosError 일 때 처리
const handleAPIError = (
  error: AxiosError<{ code?: string; message?: string }>,
  isGetErrorMsg: boolean,
  apiName: string
) => {
  if (isGetErrorMsg) return error;

  console.error(`${apiName} \n ${error}`);

  if (error.response) {
    // 토큰 만료 시 처리
    if (error.response.status === 401) {
      activeErrorAlert(`${apiName} \n ${error.response?.data.message}`, handleTokenExpired);
      return;
    }
    if (error.response?.data.message) {
      activeErrorAlert(`${apiName} \n ${error.response?.data.message}`);
      return;
    }
  }

  // 알 수 없는 에러 처리
  activeErrorAlert(`${apiName} \n API 조회 중 알 수 없는 오류 발생`);
  return;
};

const defaultHandleError = (error: any, isGetErrorMsg: boolean, apiName: string) => {
  // 로딩 해제
  useLoadingStore.getState().setIsLoading(false);
  // confirm 해제
  useConfirmStore.getState().initConfirmState();

  // axios 에러 처리
  if (error instanceof AxiosError) {
    handleAPIError(error, isGetErrorMsg, apiName);
    return;
  }

  // 일반 에러 처리
  if (error instanceof Error) {
    if (isGetErrorMsg) return error;

    activeErrorAlert(`${apiName} \n ${error.message}`);
    return;
  }

  // 알 수 없는 에러 처리
  activeErrorAlert(`${apiName} \n API 조회 중 알 수 없는 오류 발생`);
  return;
};

export { defaultHandleError };
export default activeErrorAlert;
