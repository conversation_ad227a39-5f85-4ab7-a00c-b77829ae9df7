import api from '@api/api';
import { useRef, useState } from 'react';

/**
 * 대용량 파일 분할 업로드 테스트 컴포넌트
 * 
 * 추후 개선 필요
 * 
 * @returns 
 */
const TestFileChunkUpload = () => {

    const fileInputRef = useRef(null);
    const fileInputRef2 = useRef(null);
    const [progress, setProgress] = useState(0);
    const [progress2, setProgress2] = useState(0);

    const sendFile = () => {
        const file = fileInputRef.current.files[0];
        fileChunkUpload(file, 10, "BBS")
    }


    const fileUpload = async () => {
        const files = fileInputRef2.current.files[0];
        const formData = new FormData();
        formData.append("files", files);
        formData.append("type", "BBS");

        console.time();
        const response = await api.post("/api/files/upload", formData);
        if (response.status === 200) {
            console.timeEnd();
            console.log("## 일반 업로드 성공")
            setProgress2(100);
        }
    }

    const fileChunkUpload = async (file: File, mb: number = 5, type: string) => {
        if (!file) {
            return;
        }
        
        const chunkSize = mb * 1024 * 1024; // MB
        const tempFileName = crypto.randomUUID()
        const totalChunks = Math.ceil(file.size / chunkSize);
    
        const uploadQueue: Promise<void>[] = [];

        console.time();
        for(let currentChunk=0; currentChunk<totalChunks; currentChunk++) {
            const start = currentChunk * chunkSize;
            const end = Math.min(start + chunkSize, file.size);
            const chunk = file.slice(start, end);

            const formData = new FormData();
            formData.append("type", type);
            formData.append("chunkedFile", chunk, file.name);
            formData.append("chunkNumber", currentChunk.toString());
            formData.append("totalChunks", totalChunks.toString());
            formData.append("tempFileName", tempFileName);

            uploadQueue.push(api.post("/api/files/chunk-upload", formData))
        }

        await Promise.all(uploadQueue);
        console.timeEnd();
        console.log("## 분할 업로드 성공")
        setProgress(100)
    }

    return (
        <div>
            <h1>분할 업로드</h1>
            <input type="file" ref={fileInputRef}/>
            <button onClick={() => sendFile()}>파일 업로드</button>
            <p>진행률 : {progress}%</p>

            <br/>
            <br/>
            <br/>

            <h1>일반 업로드</h1>
            <input type="file" ref={fileInputRef2}/>
            <button onClick={() => fileUpload()}>파일 업로드</button>
            <p>진행률 : {progress2}%</p>
        </div>
    );
};

export default TestFileChunkUpload;