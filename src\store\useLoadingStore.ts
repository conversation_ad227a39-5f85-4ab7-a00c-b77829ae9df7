import { create } from 'zustand';

interface LoadingStore {
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
}

interface LoadingGridDataStore {
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
}

interface DownloadingStore {
  isDownloading: boolean;
  setIsDownloading: (val: boolean) => void;
}

export const useLoadingStore = create<LoadingStore>((set) => ({
  isLoading: false,
  setIsLoading: (val) => set(() => ({ isLoading: val })),
}));

export const useLoadingGirdDataStore = create<LoadingGridDataStore>((set) => ({
  isLoading: false,
  setIsLoading: (val) => set(() => ({ isLoading: val })),
}));

export const useDownloadingStore = create<DownloadingStore>((set) => ({
  isDownloading: false,
  setIsDownloading: (val) => set(() => ({ isDownloading: val })),
}));
