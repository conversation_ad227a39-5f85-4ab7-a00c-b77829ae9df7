import { HTMLAttributes, InputHTMLAttributes, TextareaHTMLAttributes } from 'react';

// 기본 input
export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  placeholder?: string;
  design?: 'default' | 'error' | 'warning' | 'confirm';
  align?: 'left' | 'right';
  inputSize?: 'medium' | 'small';
  className?: string;
  divClass?: string;
  error?: string;
  label?: string;
}

// label + input
export interface LabelInputProps extends InputProps {
  label: string;
  relateId: string;
}

// checkbox
export interface CheckboxOptionProps {
  label: string;
  value: string | number;
}

export interface TextAreaProps extends TextareaHTMLAttributes<HTMLTextAreaElement> {
  placeholder?: string;
  className?: string;
  error?: string;
  design?: 'default' | 'error' | 'warning' | 'confirm';
}
