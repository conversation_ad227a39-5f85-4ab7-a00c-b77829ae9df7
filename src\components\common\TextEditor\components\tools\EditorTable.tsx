import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import DefaultModal from '@components/common/Modal/DefaultModal';
import { useCurrentEditor } from '@tiptap/react';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';

interface ModalState {
  isOpen: boolean;
  type: 'image' | 'table';
  onConfirm?: () => void;
  onCancel?: () => void;
}

const EditorTable = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    type: 'image',
    onConfirm: () => {},
    onCancel: () => {},
  });

  const methods = useForm({
    defaultValues: {
      rows: 3,
      cols: 3,
    },
  });

  const handleOpenModal = (type: 'image' | 'table') => {
    setModalState((prev) => ({ ...prev, isOpen: true, type }));
  };

  const handleCloseModal = () => {
    setModalState((prev) => ({
      isOpen: false,
      type: null,
      content: null,
      onConfirm: () => {},
      onCancel: () => {},
    }));
  };

  const handleSubmitTableRowCol = (data: { rows: number; cols: number }) => {
    editor.commands.insertTable({ rows: data.rows, cols: data.cols, withHeaderRow: true });
    handleCloseModal();
  };

  return (
    <>
      <IconButton
        text="table"
        icon="table"
        iconOnly
        fill="unfilled"
        size='smallest'
        color="grayscale"
        onClick={() => handleOpenModal('table')}
        disabled={editor.isActive('codeBlock')}
      />
      <DefaultModal
        className="table_modal"
        isOpenModal={modalState.isOpen}
        setIsOpenModal={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
        title={modalState.type === 'image' ? '이미지 업로드' : '테이블 생성'}
        onClickedDim={handleCloseModal}
        footer={modalState.type === 'table' && <></>}
      >
        {modalState.type === 'table' && (
          <Form methods={methods} onSubmit={handleSubmitTableRowCol}>
            <label htmlFor="rows">
              <span>행 :</span>{' '}
              <FormInput
                type="number"
                id="rows"
                name="rows"
                rules={{
                  required: true,
                  max: { value: 50, message: '50 이하의 숫자를 입력하세요' },
                  min: { value: 0 },
                }}
              />
            </label>
            <label htmlFor="cols">
              <span>열 :</span>{' '}
              <FormInput
                type="number"
                id="cols"
                name="cols"
                rules={{
                  required: true,
                  max: { value: 50, message: '50 이하의 숫자를 입력하세요' },
                  min: { value: 0, message: '0 이상의 숫자를 입력하세요' },
                }}
              />
            </label>
            <div className="button_wrapper">
              <Button text="취소" color="grayscale" onClick={handleCloseModal} />
              <Button text="확인" type="submit" onClick={() => {}} />
            </div>
          </Form>
        )}
      </DefaultModal>
    </>
  );
};

export default EditorTable;
