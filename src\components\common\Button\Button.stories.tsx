import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import { ButtonProps, IconButtonProps, iconTypes } from '@components/common/Button/types';
import type { Meta, StoryObj } from '@storybook/react';

/**
 * default 타입 {@link ButtonProps} 참고
 * iconButton 타입 {@link IconButtonProps} 참고
 */

const meta: Meta<typeof Button> = {
  title: 'Components/Common/Button',
  parameters: {
    layout: 'centered',
  },
};

export default meta;

// 기본 버튼
type DefaultButtonStory = StoryObj<typeof Button>;

export const Button_: DefaultButtonStory = {
  render: (args: ButtonProps) => <Button {...args} />,
  args: {
    type: 'button',
    text: 'button',
    design: 'default',
    fill: 'filled',
    color: 'primary',
    isActive: false,
    disabled: false,
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['button', 'submit', 'reset'],
    },
    design: {
      control: 'select',
      options: ['default', 'capsule', 'rect', 'handle'],
    },
    fill: {
      control: 'select',
      options: ['filled', 'unfilled', 'outlined'],
    },
    color: {
      control: 'select',
      options: ['primary', 'grayscale', 'red'],
    },
  },
};

// 아이콘 버튼
type IconButtonStory = StoryObj<typeof IconButton>;

export const IconButton_: IconButtonStory = {
  render: (args: IconButtonProps) => <IconButton {...args} />,
  args: {
    type: 'button',
    text: 'button',
    design: 'default',
    icon: 'arrow_right',
    iconPosition: 'left',
    iconOnly: false,
    fill: 'filled',
    color: 'primary',
    isActive: false,
    disabled: false,
  },
  argTypes: {
    type: {
      control: 'select',
      options: ['button', 'submit', 'reset'],
    },
    icon: {
      control: 'select',
      options: iconTypes,
    },
    iconPosition: {
      control: 'select',
      options: ['top', 'bottom', 'left', 'right'],
    },
    design: {
      control: 'select',
      options: ['default', 'capsule', 'rect', 'circle', 'handle'],
    },
    fill: {
      control: 'select',
      options: ['filled', 'unfilled', 'outlined'],
    },
    color: {
      control: 'select',
      options: ['primary', 'grayscale', 'red'],
    },
  },
};
