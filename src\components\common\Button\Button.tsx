import joinClassName from '@utils/joinClassName';
import useClickLog from '@hooks/useClickLog';
import { LogParams } from '@components/common/Log/Components/types';
import { ButtonProps } from './types';

/**기본 버튼 */
const Button = ({
  text, // 버튼 text(*)
  onClick, // 버튼 클릭 시 실행 함수(*)
  type = 'button', // 버튼의 type (button/submit/reset)
  clickLog, // 버튼 로그 params

  /**버튼 디자인 요소 */
  design = 'default',
  fill = 'filled',
  color = 'primary',
  size = 'medium',
  className, // 추가 클래스 요소
  isActive = false,
  ...attributes
}: ButtonProps) => {
  const sizeClass = design === 'circle' ? size : ''; // Design 이 Circle일 경우만 사용
  const activeClass = isActive ? 'active' : '';
  const buttonClass = joinClassName('c_button', design, sizeClass, fill, color, className, activeClass);
  const { handleClickLog } = useClickLog();

  return (
    <button
      type={type}
      onClick={(event) => {
        if (typeof onClick === 'function') {
          onClick(event);
        }

        if (clickLog) {
          handleClickLog({
            eventType: (type === 'button' ? 'click' : type) as LogParams['logType'],
            button: text,
            buttonSection: clickLog.buttonSection,
          });
        }
      }}
      className={buttonClass}
      {...attributes}
    >
      <span className={joinClassName('c_button_text', design === 'switch' && 'sr-only')}>{text}</span>
    </button>
  );
};

export default Button;
