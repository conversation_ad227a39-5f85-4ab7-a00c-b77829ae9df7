import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import useUserMenuStore from '../store/useUserMenuStore';

export const useUserMenuAPI = () => {
  const USER_MENU = '/api-admin/user/menus';
  const USER_MENU_CHANGE = '/api-admin/user/menus/change-sort';
  const { setMenus } = useUserMenuStore();
  const { openAlert } = useAlert();

  // 메뉴 목록 조회
  const getMenus = async () => {
    try {
      const response = await api.get(USER_MENU);

      if (response.status === 200) {
        // 호출 성공
        const data = response.data.data;
        setMenus(data);
      } else {
        // 호출 실패
        openAlert('메뉴리스트 호출에 실패했습니다');
      }

      return response;
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 메뉴 생성
  const createMenu = async () => {
    try {
      const response = await api.post(USER_MENU);

      if (response.status === 200) {
        getMenus();
      }
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 사용자 메뉴 순서 변경
  const changeSortMenus = async (menus) => {
    try {
      const response = await api.post(USER_MENU_CHANGE, menus);
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 메뉴 삭제
  const deleteMenu = async (ids) => {
    try {
      const response = await api.post(USER_MENU + '/delete', { ids });

      if (response.status === 200) {
        openAlert('메뉴가 성공적으로 삭제되었습니다.', getMenus);
      }
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  return { getMenus, createMenu, changeSortMenus, deleteMenu };
};
