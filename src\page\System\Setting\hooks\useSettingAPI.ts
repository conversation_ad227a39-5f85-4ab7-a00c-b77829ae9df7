import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

const isGetErrorMsg = false;

export const useSettingAPI = () => {
  const ADMIN_SETTING = '/api-admin/settings';

  const getSettingsTypes = async () => {
    try {
      const response = await api.get(ADMIN_SETTING);
      return response.data.data;
    } catch(error) {
      defaultHandleError(error, isGetErrorMsg, '설정 타입 조회');
      return false
    }
  };

  const getSettings = async (type: string) => {
    try {
      const response = await api.get(`${ADMIN_SETTING}/${type}`);
      return response.data.data;
    } catch(error) {
      defaultHandleError(error, isGetErrorMsg, '설정 목록 조회');
      return false
    }
  };

  const editSetting = async (data: any) => {
    try {
      const response = await api.post(`${ADMIN_SETTING}/update`, data);
      
      return true
    } catch(error) {
      defaultHandleError(error, isGetErrorMsg, '설정 수정');
      return false
    }
  };

  return {
    getSettingsTypes,
    getSettings,
    editSetting,
  };
};
