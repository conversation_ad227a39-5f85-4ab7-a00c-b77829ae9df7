export interface ChildPage {
  id: number | null;
  parentId: number | null;
  boardTempletId: number | null;
  pageName: string;
  pageFileName: string;
  pageContent: string | null;
  pageLink: string | null;
  pageTypeCode: 'L' | 'P' | 'R' | 'C' | 'M' | 'N' | 'B';
  pageDesc: string | null;
  roleUseYn: 'Y' | 'N' | null;
  pageRole: string | null;
  dateInfo: string | null;
  filePath: string;
}

export interface Menu {
  id: number;
  parentId: number | null;
  name: string;
  sortNo: number;
  url: string;
  depth: number;
  icon: string;
  newWindowYn: 'Y' | 'N';
  pageId: number;
  pageParentId: number | null;
  pageName: string;
  pageContent: string | null;
  pageLink: string | null;
  pageTypeCode: 'L' | 'P' | 'R' | 'C' | 'M' | 'N' | 'B';
  pageFileName: string;
  filePath: string;
  roleUseYn: 'Y' | 'N';
  readRoleYn: 'Y' | 'N';
  regRoleYn: 'Y' | 'N';
  modRoleYn: 'Y' | 'N';
  delRoleYn: 'Y' | 'N';
  boardTempletId: number;
  boardTempletType: 'GENERAL' | 'QNA' | 'GALLERY' | null;
  childMenus: Menu[];
  childPages: ChildPage[];
}

export interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

export interface AdminPostSearchOptions {
  pageSize: { label: string; value: number }[];
  boardList: { label: string; value: number }[];
}

export interface AdminPostFormValue {
  board: { label: string; value: number };
  postTitle: string;
}

export interface AdminPostDetailType {
  id: number;
  title: string;
  content: string;
  viewCnt: number;
  delYn: 'Y' | 'N';
  dateInfo: {
    createUser: string;
    createDate: string;
    updateUser: string;
    updateDate: string;
  };
  templetId: number | null;
  notiYn: 'Y' | 'N';
  templetNm: string;
  templetTypeCd: string;
  templetTypeNm: string;
  attchFiles: any[];
}

export interface AdminPostDetailOptions {
  templetList: { label: string; value: string }[];
}
export interface GeneralBoardPostDTO {
  title?: string;
  content: string;
  notiYn: 'Y' | 'N';
  files?: FileList;
}

export interface GeneralBoardPost extends GeneralBoardPostDTO {
  id: string;
  dateInfo: DateInfo;
}
