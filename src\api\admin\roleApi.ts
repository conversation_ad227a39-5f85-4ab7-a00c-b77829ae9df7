import api from '@api/api';
import { DetailFormValues, ParsedPageAuthData } from '@page/Admin/Role/type';
import { defaultHandleError } from '@utils/apiErrorHandler';

// 권한 목록 조회
interface GetAuthListParams {
  isGetErrorMsg?: boolean;
}

const getAuthList = async ({ isGetErrorMsg = false }: GetAuthListParams) => {
  try {
    const response = await api.get(`/api-admin/admin/roles`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 목록 조회');
  }
};

// 권한 상세 조회
interface GetAuthInfoDataParams {
  id: number;
  isGetErrorMsg?: boolean;
}

const getAuthInfoData = async ({ id, isGetErrorMsg = false }: GetAuthInfoDataParams) => {
  try {
    const response = await api.get(`/api-admin/admin/roles/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 상세 조회');
  }
};

interface GetPageAuthListParams {
  isGetErrorMsg?: boolean;
}

const getPageAuthList = async ({ isGetErrorMsg = false }: GetPageAuthListParams) => {
  try {
    const response = await api.get(`/api-admin/admin/page-roles`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 상세 조회');
  }
};
// 권한 추가
interface AddAuthParams {
  name: string;
  menuIds: number[];
  serviceIds: number[];
  serviceGroupIds: number[];
  pageRoles: ParsedPageAuthData[];
  isGetErrorMsg?: boolean;
}

const addAuth = async ({
  name,
  menuIds,
  serviceIds,
  serviceGroupIds,
  pageRoles,
  isGetErrorMsg = false,
}: AddAuthParams) => {
  try {
    const response = await api.post('/api-admin/admin/roles', {
      name,
      menuIds,
      serviceGroupIds,
      serviceIds,
      pageRoles,
    });

    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 추가');
  }
};

// 권한 수정
interface UpdateAuthParams {
  id: number;
  name: string;
  menuIds: number[];
  serviceIds: number[];
  serviceGroupIds: number[];
  pageRoles: ParsedPageAuthData[];
  isGetErrorMsg?: boolean;
}

const updateAuth = async ({
  id,
  name,
  menuIds,
  serviceIds,
  serviceGroupIds,
  pageRoles,
  isGetErrorMsg = false,
}: UpdateAuthParams) => {
  try {
    const response = await api.post(`/api-admin/admin/roles/${id}`, {
      name,
      menuIds,
      serviceGroupIds,
      serviceIds,
      pageRoles,
    });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 수정');
  }
};

// 권한 삭제
interface DeleteAuthParams {
  id: number;
  isGetErrorMsg?: boolean;
}

const deleteAuth = async ({ id, isGetErrorMsg = false }: DeleteAuthParams) => {
  try {
    const response = await api.post(`/api-admin/admin/roles/${id}/delete`);

    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '권한 삭제');
  }
};

export { getAuthList, getAuthInfoData, getPageAuthList, addAuth, updateAuth, deleteAuth };
