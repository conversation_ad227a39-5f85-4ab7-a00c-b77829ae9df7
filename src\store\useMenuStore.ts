import { Menu } from 'types/adminMenuType';
import { create } from 'zustand';

interface MenuStore {
  myMenuList: Menu[];
  setMyMenuList: (menuList: Menu[]) => void;
  dynamicMenuList: any[];
  setDynamicMenuList: (dynamicMenuList: any[]) => void;
}

export const useMenuStore = create<MenuStore>((set) => ({
  // 접근 가능 메뉴 리스트
  myMenuList: [],
  setMyMenuList: (myMenuList) => set({ myMenuList }),

  dynamicMenuList: [],
  setDynamicMenuList: (dynamicMenuList) => set({ dynamicMenuList }),
}));
