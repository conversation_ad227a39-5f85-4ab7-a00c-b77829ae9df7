import IconButton from '@components/common/Button/IconButton';
import { InputChipProps } from './types';
import joinClassName from '@utils/joinClassName';

const InputChip = ({ label, onDelete, imgSrc, size = 'medium', className }: InputChipProps) => {
  const withImgClass = imgSrc ? 'with_img' : '';
  const chipClass = joinClassName('c_chip', 'c_chip_input', size, withImgClass, className);

  return (
    <div className={chipClass}>
      {imgSrc && <img src={imgSrc} alt="profile" className='chip_img'/>}
      <span>{label}</span>
      <IconButton
        text="close"
        icon="close"
        iconOnly
        design="circle"
        color="grayscale"
        onClick={onDelete}
      />
    </div>
  );
};

export default InputChip;
