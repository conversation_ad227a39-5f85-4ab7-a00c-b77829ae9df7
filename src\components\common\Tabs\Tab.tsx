import React from 'react';
import Button from '@components/common/Button/Button';
import { useTabContext } from './TabsContainer';

interface TabProps {
  id: string;
  children: string;
}

export const Tab: React.FC<TabProps> = ({ id, children }) => {
  const useStore = useTabContext();
  const { activeTab, setActiveTab } = useStore();

  const handleClick = () => {
    setActiveTab(id);
  };

  return (
    <Button
      className="c_tab"
      design="tab"
      onClick={handleClick}
      type="button"
      role="tab"
      color="grayscale"
      aria-selected={activeTab === id}
      aria-controls={`${id}-tabpanel`}
      isActive={activeTab === id}
      text={children}
      clickLog={{ buttonSection: '탭 버튼' }}
    />
  );
};
