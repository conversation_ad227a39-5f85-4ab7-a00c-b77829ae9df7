import { useEffect } from 'react';
import { useSettingStore } from '@page/System/Setting/store/useSettingStore';
import { useSettingAPI } from '@page/System/Setting/hooks/useSettingAPI';
import { SettingType } from '@page/System/Setting/types';
import ListTable from '@components/common/Table/ListTable';
import SubTitle from '@components/common/Title/SubTitle';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Button from '@components/common/Button/Button';

const SettingTypes = () => {
  const { settingTypeListData, settingType, setSettingType, setSettingTypesData } = useSettingStore();
  const { getSettingsTypes } = useSettingAPI();

  const handleSettingTypeSelect = (type: SettingType) => {
    setSettingType(type);
  };

  const getTypeList = async () => {
    const data = await getSettingsTypes();
    setSettingTypesData(data);
  };

  useEffect(() => {
    getTypeList();
  }, []);

  return (
    <>
      <TableContainerHeader leftChildren={<SubTitle>설정 유형</SubTitle>} />
      <ListTable
        title="설정"
        list={
          settingTypeListData && settingTypeListData.length > 0
            ? settingTypeListData.map((item) => (
                <div
                  key={item.code}
                  className={item.code === settingType ? 'active' : ''}
                  onClick={() => handleSettingTypeSelect(item.code)}
                >
                  <span>{item.desc}</span>
                </div>
              ))
            : []
        }
      />
    </>
  );
};

export default SettingTypes;
