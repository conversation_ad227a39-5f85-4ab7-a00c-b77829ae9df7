import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { FormValues } from './components/types';
import { useCodeStore } from './store/useCodeStore';
import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Title from '@components/common/Title/Title';
import PageList from './components/PageList';
import PageDetail from './components/PageDetail';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import { getGroupCodeDetail, getGroupCodeList } from '@api/admin/systemCodeAPI';
import useEventBus from '@hooks/useEventBus';
import { Mode, Page } from './type';
import { getPageList } from '@api/user/pageApi';
import DataGrid from '@components/common/Grid/DataGrid';
import { ColDef, ColGroupDef } from '@ag-grid-community/core';
import { useNavigate } from 'react-router-dom';

const UserPageListPage = () => {
  // 로딩 상태 관리

  const {
    groupCode, // 현재 선택된 그룹 코드 정보
    setGroupCodeDetailData, // 그룹 코드 상세 정보를 설정하는 함수
    setGroupCodeListData, // 그룹 코드 목록 데이터를 설정하는 함수
  } = useCodeStore();

  // 검색 폼 관리
  const searchMethods = useForm<FormValues>({
    defaultValues: {
      code: '', // 그룹 코드 검색어 초기값
    },
  });

  const [rowData, setRowData] = useState<Page[]>([]);

  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      const data = await getPageList();
      setRowData(data);
    };

    fetchData();
  }, []);

  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    {
      field: 'id',
      headerName: 'No',
      valueSetter: (params) => {
        params.data.id = params.newValue;
        return true;
      },
      valueParser: (params) => Number(params.newValue),
      cellEditor: 'agTextCellEditor',
      cellEditorParams: { maxLength: 10 },
      cellEditorSelector: () => ({ component: 'agTextCellEditor' }),
      cellEditorPopup: true,
      cellEditorPopupPosition: 'under',
      singleClickEdit: true,
      useValueParserForImport: true,
    },
    {
      field: 'pageName',
      headerName: '페이지명',
      cellClass: 'underline cursor-pointer',
      onCellClicked: (event: any) => {
        const pageId = event.data.id;
        navigate(`/user/page/detail/${pageId}`);
      },
    },
    {
      field: 'pageFileName',
      headerName: '파일명',
    },
    {
      field: 'pageTypeCode',
      headerName: '유형',
      valueFormatter: ({ value }) => {
        const reverseMap = {
          L: '목록',
          R: '등록',
          M: '수정',
          P: '상세',
        };
        return reverseMap[value] ?? value;
      },
    },
    {
      field: 'pageDesc',
      headerName: '설명',
    },

    {
      field: 'bbsUseYn',
      headerName: '게시판 사용 여부',
    },
    {
      field: 'roleUseYn',
      headerName: '권한 사용 여부',
    },
    {
      field: 'createUser',
      headerName: '등록자',
    },
    {
      field: 'createDate',
      headerName: '등록일',
    },
    {
      field: 'updateUser',
      headerName: '변경자',
    },
    {
      field: 'updateDate',
      headerName: '변경일',
    },
  ]);

  return (
    <div className="admin_board">
      <Form onSubmit={() => {}} methods={searchMethods}>
        <ControlBox>
          <FormInput name="code" label="페이지명" placeholder="페이지명을 입력하세요." />
          <Button type="submit" text="조회" onClick={() => {}} clickLog={{ buttonSection: '검색창' }} />
        </ControlBox>
      </Form>
      <DataGrid
        rowEditMode={false}
        serverPagination={true}
        addButtonCallback={() => {}}
        columnDefs={columnDefs}
        // rowData={rowData}
        rowData={[
          {
            id: 1,
            pageName: '회원 목록',
            pageFileName: 'MemberListPage.tsx',
            pageTypeCode: 'L', // valueFormatter에 의해 '목록'으로 보일 것
            pageDesc: '회원 목록 페이지',
            bbsUseYn: 'Y',
            roleUseYn: 'N',
            createUser: 'admin',
            createDate: '2024-04-01',
            updateUser: 'editor',
            updateDate: '2024-04-15',
          },
        ]}
      />
    </div>
  );
};

export default UserPageListPage;
