@use '@styles/utils/mixin' as m;

.c_modal {
  @include m.flex(center, space-between, column);
  min-width: 300px;
  min-height: 150px;
  border-radius: 0.5rem;
  gap: 1.25rem;
  padding: 1rem;
  z-index: 10001;
  position: fixed;
  background-color: var(--modal_bg);
  border: 1px solid var(--g_06);
  box-shadow: var(--shadow_l);

  &.test_modal {
    width: 300px;
    margin: 0 auto;
    margin-bottom: 1rem;
  }
  &.confirm {
    .c_modal_footer {
      @include m.flex(center, flex-end);
    }
  }
  &.alert {
    .c_modal_footer {
      @include m.flex(center, center);

      button {
        width: 100%;
      }
    }
  }

  &.event {
    @include m.position_center();

    .c_modal_body {
      width: 440px;
      height: 550px;
      padding: 0;

      .swiper {
        width: 100%;
        height: 100%;
        .desc {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          z-index: 8;
          background-color: rgba(#fff, 0.8);
          text-align: left;
          padding: 1rem 2rem 2rem 2rem;
          h3 {
            font-size: 1.5rem;
            font-weight: 600;
          }
        }

        .swiper-slide {
          width: 100%;
          height: 100%;

          .slide_img {
            width: 100%;
            height: 100%;
            cursor: pointer;
            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              object-position: center;
            }
          }
        }
      }
    }

    .c_modal_footer {
      @include m.flex(center, space-between);
    }

    @include m.bp_large {
      padding: 0.875rem;
      .c_modal_body {
        width: 400px;
        height: 500px;

        .swiper {
          .desc {
            padding: 0.875rem 1.75rem 1.75rem 1.75rem;

            h3 {
              font-size: 1.25rem;
              font-weight: 600;
            }
            p {
              font-size: 0.875rem;
            }
          }
        }
      }
    }

    @include m.bp_medium {
      padding: 0.75rem;
      .c_modal_body {
        width: 360px;
        height: 450px;

        .swiper {
          .desc {
            padding: 0.75rem 1.5rem 1.5rem 1.5rem;
            h3 {
              font-size: 1rem;
              font-weight: 600;
            }
            p {
              font-size: 0.75rem;
            }
          }
        }
      }
    }
  }

  &.top_active {
    z-index: 10001;
  }

  .c_modal {
    &_header {
      cursor: move;
      width: 100%;
      border-radius: 0.5rem 0.5rem 0 0;
      .c_modal_title {
        font-size: 1.25rem;
        font-weight: 600;
        @include m.flex(center, center);
      }

      .i_close {
        position: absolute;
        top: 1rem;
        right: 1rem;
      }
    }

    &_body {
      width: 100%;
      padding: 0.5rem;
      text-align: center;
    }

    &_footer {
      width: 100%;
      gap: 0.25rem;

      button {
        margin: 0;
      }
    }
  }
}

.c_dim {
  position: fixed;
  width: 100vw;
  height: 100vh;
  z-index: 10000;
  top: 0;
  left: 0;

  &.top_active {
    z-index: 10000;
  }

  &:not(.transparent) {
    background-color: rgba(black, 0.5);
  }
}
