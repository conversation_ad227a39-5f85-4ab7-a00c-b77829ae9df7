import { EventModalProps } from '@components/common/Modal/EventModal';
import { create } from 'zustand'

interface EventStoreProps {
  eventPopupData : EventModalProps['data'][] | null;
  setEventPopupData : (val : EventModalProps['data'][]) => void;
}
export const useEventStore = create<EventStoreProps>((set)=>({
  eventPopupData : null,
  setEventPopupData : (val) => set( (state) => ({ eventPopupData : val }) )
}))
