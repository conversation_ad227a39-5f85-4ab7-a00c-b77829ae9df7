import { useEffect, useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Title from '@components/common/Title/Title';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import type { Page } from './type';
import { getPageList } from '@api/user/pageApi';
import DataGrid from '@components/common/Grid/DataGrid';
import { ColDef, ColGroupDef } from '@ag-grid-community/core';
import { useNavigate } from 'react-router-dom';
import type { PaginationInfo } from '@type/pagination';
import { pageTypeOptions } from '@constants/options';
import SelectBox from '@components/common/SelectBox/SelectBox';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import { pageSizeOptions } from '@constants/options';
import { AgGridReact } from '@ag-grid-community/react';

const AdminPageListPage = () => {
  const gridRef = useRef<AgGridReact>(null);

  const formMethods = useForm({
    defaultValues: {
      pageName: '',
    },
  });

  const [rowData, setRowData] = useState<Page[]>([]);

  const [pagination, setPagination] = useState<PaginationInfo>({
    currentPage: 1,
    pageSize: 10,
    totalCount: 0,
    totalPages: 0,
  });

  const navigate = useNavigate();

  const [selectedPageSize, setSelectedPageSize] = useState<{ label: string; value: number }>(pageSizeOptions[0]);

  const fetchData = async () => {
    const data = await getPageList({
      page: pagination.currentPage - 1,
      size: selectedPageSize.value,
      pageName: formMethods.getValues('pageName'),
    });

    setRowData(utils.addSeqNo(data.data, data.pageInfo));

    setPagination((prev) => ({
      ...prev,
      totalCount: data.pageInfo.totalCount,
    }));
  };

  // 상세 페이지 이동
  const onNavigateDetail = (event) => {
    const pageId = event.data?.id;
    navigate(`/admin/page/detail/${pageId}`);
  };

  useEffect(() => {
    fetchData();
  }, [pagination.currentPage, selectedPageSize]);

  const [columnDefs] = useState<ColDef[]>([
    {
      field: 'no',
      headerName: 'No',
      cellClass: 'text-center',
      width: 90,
    },
    {
      field: 'pageTypeCode',
      headerName: '유형',
      cellClass: 'text-center',
      width: 90,
      valueFormatter: ({ value }) => {
        return utils.getLabel(value, pageTypeOptions);
      },
    },
    {
      field: 'pageName',
      headerName: '페이지명',
    },
    {
      field: 'filePath',
      headerName: '파일경로',
      tooltipField: 'filePath',
    },
    {
      field: 'roleUseYn',
      headerName: '권한',
      cellClass: 'text-center',
      width: 90,
    },
    {
      field: 'pageDesc',
      headerName: '설명',
    },
    {
      field: 'createUser',
      headerName: '등록자',
      width: 120,
      valueGetter: ({ data }) => data?.dateInfo?.createUser ?? '',
    },
    {
      field: 'createDate',
      headerName: '등록일',
      valueGetter: ({ data }) => data?.dateInfo?.createDate ?? '',
    },
    {
      field: 'updateUser',
      headerName: '수정자',
      width: 120,
      valueGetter: ({ data }) => data?.dateInfo?.updateUser ?? '',
    },
    {
      field: 'updateDate',
      headerName: '수정일',
      valueGetter: ({ data }) => data?.dateInfo?.updateDate ?? '',
    },
  ]);

  const onChangePagination = (key: 'currentPage' | 'pageSize', value: number) => {
    setPagination((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  return (
    <div className="admin_page_list">
      <Form onSubmit={fetchData} methods={formMethods}>
        <ControlBox>
          <FormInput name="pageName" label="페이지명" placeholder="페이지명 입력" />
          <Button type="submit" text="조회" clickLog={{ buttonSection: '검색창' }} />
        </ControlBox>
      </Form>
      <div className="admin_board_contents_control">
        <div className="flex space-x-2">
          <p>총 {pagination.totalCount}건</p>
          <SelectBox
            options={pageSizeOptions}
            defaultValue={selectedPageSize}
            selectedValue={selectedPageSize}
            setSelectedValue={setSelectedPageSize}
          />
        </div>
        <div className="admin_board_contents_control_btn">
          <Button
            text="추가"
            onClick={() => {
              navigate('/admin/page/add');
            }}
          />
        </div>
      </div>
      <div className="admin_board_contents">
        <Grid
          ref={gridRef}
          columns={columnDefs}
          rowData={rowData}
          autoSizeStrategy={'onGridSizeChanged'}
          defaultColDef={{
            onCellClicked: onNavigateDetail,
          }}
        />
        <Pagination
          pageCount={10}
          totalCount={pagination.totalCount}
          currentPage={pagination.currentPage}
          itemCountPerPage={pageSizeOptions[0].value}
          onPageChange={(currentPage) => onChangePagination('currentPage', currentPage)}
        />
        {/* <DataGrid
        rowEditMode={false}
        addButtonCallback={() => {
          navigate('/admin/page/add');
        }}
        defaultColDef={{
          onCellClicked: onNavigateDetail,
        }}
        columnDefs={columnDefs}
        rowData={rowData}
        serverPagination={pagination}
        onPageChange={(currentPage) => onChangePagination('currentPage', currentPage)}
        onPageSizeChange={(pageSize) => onChangePagination('pageSize', pageSize)}
        getRowClass={() => 'cursor-pointer'}
      /> */}
      </div>
    </div>
  );
};

export default AdminPageListPage;
