import NavContainer from '@components/common/Navigation/NavContainer';
import { useNavigate } from 'react-router-dom';
import joinClassName from '@utils/joinClassName';

import { useMenuStore } from '@store/useMenuStore';
import { useLayoutStore } from '@store/useLayoutStore';

const LNB = () => {
  const { myMenuList } = useMenuStore();
  const { lnbState } = useLayoutStore();

  const navigate = useNavigate();

  const handleClickLogo = () => {
    navigate('/myInfo');
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <aside className={joinClassName('c_lnb', lnbState ? 'active' : '')}>
      <div className="c_logo_wrapper" onClick={handleClickLogo}>
        <figure className="c_logo" />
        <figcaption>
          <span>BRIT</span>
          <p>NODE PLATFORM</p>
        </figcaption>
      </div>
      {myMenuList && <NavContainer menuList={myMenuList} />}
    </aside>
  );
};

export default LNB;
