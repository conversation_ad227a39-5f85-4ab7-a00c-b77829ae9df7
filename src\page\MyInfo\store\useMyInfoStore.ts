import { create } from 'zustand';

interface MyInfoStore {
  id: number;
  name: string;
  adminId: string;
  activeYn: string;
  roleIds: number[];
  setMyInfo: (val: { id: number; name: string; adminId: string; activeYn: string; roleIds: number[] }) => void;
}

export const useMyInfoStore = create<MyInfoStore>((set) => ({
  id: 0,
  name: '',
  adminId: '',
  activeYn: 'N',
  roleIds: [],
  setMyInfo: (val) =>
    set(() => ({ id: val.id, name: val.name, adminId: val.adminId, activeYn: val.activeYn, roleIds: val.roleIds })),
}));
