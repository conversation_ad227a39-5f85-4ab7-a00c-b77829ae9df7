import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { deleteSinglePost, getPostDetail } from '@api/admin/postApi';
import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import TextEditor from '@components/common/TextEditor/TextEditor';
import Attachment from '@page/Admin/Post/components/Attachment';

import { AdminPostDetailType } from '@page/Admin/Post/type';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';


const AdminPostDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();

  const [data, setData] = useState<AdminPostDetailType | undefined>(undefined);

  const handleGetPostDetail = async () => {
    const response = await getPostDetail({ id });

    setData(response);
  };

  useEffect(() => {
    handleGetPostDetail();
  }, []);

  const handleDeletePost = async () => {
    initConfirmState();

    const response = await deleteSinglePost({ id });

    if (response === 'DELETE') {
      setAlertState({
        isOpen: true,
        content: '게시글이 삭제되었습니다',
        onConfirm: () => {
          navigate(`/admin/post`);
          initAlertState();
        },
      });
    }
  };

  const handleClickDeleteBtn = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: handleDeletePost,
    });
  };

  return (
    <div className="admin_post">
      <div className="admin_post_detail_wrapper">
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={[
                {
                  title: '게시판 유형',
                  contents: data?.templetTypeNm,
                },
                {
                  title: '게시판 명',
                  contents: data?.templetNm,
                },
              ]}
            />
            <TableBodyRow
              rowData={{
                title: '게시글 제목',
                isFullWidth: true,
                contents: data?.title,
              }}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '작성자',
                  contents: data?.dateInfo.createUser,
                },
                {
                  title: '작성 일시',
                  contents: data?.dateInfo.createDate,
                },
              ]}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '수정자',
                  contents: data?.dateInfo.updateUser,
                },
                {
                  title: '수정 일시',
                  contents: data?.dateInfo.updateDate,
                },
              ]}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '공지 여부',
                  contents: data?.notiYn === 'Y' ? '사용' : '미사용',
                },
                {
                  title: '첨부파일',
                  isFullWidth: true,
                  contents: <Attachment data={data?.attchFiles} />,
                },
              ]}
            />
          </TableBody>
        </TableContainer>
        <TextEditor className="admin_post_detail_contents" content={data?.content} readOnly />
        <div className="admin_post_detail_btns">
          <Button text="목록" onClick={() => navigate('/admin/post')} />
          <div className="admin_post_detail_btns_right">
            <Button text="삭제" color="red" onClick={handleClickDeleteBtn} />
            <Button text="수정" onClick={() => navigate(`/admin/post/edit/${id}`)} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPostDetail;
