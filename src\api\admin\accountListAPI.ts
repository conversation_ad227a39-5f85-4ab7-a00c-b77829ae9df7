import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

/**
 * 관리자 계정 목록 API 훅 Props 인터페이스
 */

// 계정 목록 조회

interface GetAccountListParams {
  data: AdminListParams;
  isGetErrorMsg?: boolean;
}

interface AdminListParams {
  page: number | null;
  size: number | null;
  adminId: string | null;
  name: string | null;
  lockYn: string | null;
}

const getAccountList = async ({ data, isGetErrorMsg = false }: GetAccountListParams) => {
  try {
    const response = await api.get('/api-admin/admins', { params: data });
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 목록 조회');
  }
};

// 계정 삭제
interface DeleteAccountParams {
  id: string | number;
  isGetErrorMsg?: boolean;
}

const deleteAccount = async ({ id, isGetErrorMsg = false }: DeleteAccountParams) => {
  try {
    const response = await api.post(`/api-admin/admins/${id}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 삭제');
  }
};

// 계정 목록 삭제
interface DeleteAccountListParams {
  ids: number[];
  isGetErrorMsg?: boolean;
}

const deleteAccountList = async ({ ids, isGetErrorMsg = false }: DeleteAccountListParams) => {
  try {
    const response = await api.post(`/api-admin/admins/delete`, { ids } );
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '계정 목록 삭제');
  }
};

export { getAccountList, deleteAccount, deleteAccountList };
