/**
 * 설정 정보를 위한 타입
 */
export type SettingType = null | string

/**
 * 설정 상세 정보
 */
export interface SettingDetail {
    id: number; // id
    settingKey: string; // 키
    description: string; // 설명
    settingValue: string; // 값
}

/**
 * 확인 모달 상태를 위한 인터페이스
 */
export interface ConfirmStateType {
    isOpen: boolean; // 모달 열림 여부
    content: string | React.ReactNode; // 모달 내용
    onCancel: () => void; // 취소 버튼 클릭 시 실행될 함수
    onConfirm: (() => void) | ((data: any) => void); // 확인 버튼 클릭 시 실행될 함수
}

/**
 * 폼 입력값을 위한 인터페이스
 */
export interface FormValues {
    key: string,
    values: Array<string>
}

/**
 * 알림 모달 내용을 위한 인터페이스
 */
export interface AlertContentsType {
    isOpen: boolean;
    content: string;
    onConfirm: () => void;
}

