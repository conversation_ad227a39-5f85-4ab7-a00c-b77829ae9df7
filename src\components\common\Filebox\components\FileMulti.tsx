import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableCell from '@components/common/Table/TableCell';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import TableRow from '@components/common/Table/TableRow';
import joinClassName from '@utils/joinClassName';
import { useAlertStore } from '@store/useAlertStore';
import Alert from '@components/common/Modal/Alert';
import { useFilePreviewer } from '@components/common/Filebox/store/useFilePreviewer';
import FileImagePreview from './FileImagePreview';

interface FileMultiProps {
  files: FileList;
  onDelete: (e: React.MouseEvent<HTMLButtonElement>, file: File) => void;
  handleAddClick: () => void;
  activeClass: string;
}

const FileMulti = ({ files, onDelete, handleAddClick, activeClass }: FileMultiProps) => {
  const { setPreviewImageInfo, setPreviewModalState, previewModalState } = useFilePreviewer();

  const handlePreview = (e: React.MouseEvent<HTMLImageElement>, file: File) => {
    e.preventDefault();
    setPreviewImageInfo({
      name: file.name,
      url: URL.createObjectURL(file),
      size: (file.size / 1024).toFixed(2),
      type: file.name.split('.').pop(),
    });
    setPreviewModalState(true);
  };

  return (
    <>
      <TableContainer className={joinClassName('c_filebox_list', activeClass)}>
        <colgroup>
          <col width="5%" />
          <col width="40%" />
          <col width="10%" />
          <col width="10%" />
          <col width="10%" />
        </colgroup>
        <TableHeader>
          <TableRow tag="th" cellData={['No', '파일명', '파일타입', '파일크기', '삭제']} />
        </TableHeader>
        <TableBody>
          <>
            {files &&
              Array.from(files).map((file, index) => {
                const fileSize = (file.size / 1024).toFixed(2);
                const fileExt = file.name.split('.').pop();
                const previewImage = URL.createObjectURL(file);
                const isImage = file.type.startsWith('image/');

                return (
                  <tr key={file.name + index}>
                    <TableCell>{index + 1}</TableCell>
                    <TableCell>
                      {isImage && <img src={previewImage} alt={file.name} onClick={(e) => handlePreview(e, file)} />}
                      {file.name}
                    </TableCell>
                    <TableCell>{fileExt}</TableCell>
                    <TableCell>{fileSize}KB</TableCell>
                    <TableCell>
                      <Button text="삭제" onClick={(e) => onDelete(e, file)} />
                    </TableCell>
                  </tr>
                );
              })}
            <tr>
              <TableCell className="add_cover" colSpan={5}>
                <b className="add_cover_init_text">
                  {files?.length === 0 || files === null ? '파일을 드랍하거나 추가해 주세요.' : ''}
                </b>
                <b className="add_cover_text">파일을 드랍해주세요.</b>
                <Button text="파일 추가" onClick={handleAddClick} />
              </TableCell>
            </tr>
          </>
        </TableBody>
      </TableContainer>
      {previewModalState && <FileImagePreview />}
    </>
  );
};

export default FileMulti;
