import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

// VOC 목록 조회
interface VOCListProps {
  data: any;
  isGetErrorMsg?: boolean;
}

const getVOCList = async ({ data, isGetErrorMsg = false }: VOCListProps) => {
  try {
    const response = await api.get('/api-admin/vocs', { params: data });
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 목록 조회');
  }
};

// VOC 카테고리 조회
interface VOCCategoryOptionsProps {
  isGetErrorMsg?: boolean;
}

const getVOCCategoryOptions = async ({ isGetErrorMsg = false }: VOCCategoryOptionsProps) => {
  try {
    const response = await api.get('/api-admin/vocs/category');
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 카테고리 조회');
  }
};

// VOC 상태 조회
interface VOCStatusOptionsProps {
  isGetErrorMsg?: boolean;
}

const getVOCStatusOptions = async ({ isGetErrorMsg = false }: VOCStatusOptionsProps) => {
  try {
    const response = await api.get('/api-admin/vocs/status');
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 상태 조회');
  }
};

// VOC 등록
interface AddVOCProps {
  data: any;
  isGetErrorMsg?: boolean;
}

const addVOC = async ({ data, isGetErrorMsg = false }: AddVOCProps) => {
  try {
    const response = await api.post('/api-admin/vocs', data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 등록');
  }
};

// VOC 상세 조회
interface GetVOCDetailProps {
  vocId: number;
  isGetErrorMsg?: boolean;
}

const getVOCDetail = async ({ vocId, isGetErrorMsg = false }: GetVOCDetailProps) => {
  try {
    const response = await api.get(`/api-admin/vocs/${vocId}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 상세 조회');
  }
};

// VOC 댓글 등록
interface PostVOCCommentProps {
  vocId: number;
  content: string;
  isGetErrorMsg?: boolean;
}

const postVOCComment = async ({ vocId, content, isGetErrorMsg = false }: PostVOCCommentProps) => {
  try {
    const response = await api.post(`/api-admin/vocs/${vocId}/comment`, { content });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 댓글 등록');
  }
};

// VOC 상태 및 카테고리 수정
interface EditVOCStatusAndCategoryProps {
  vocId: number;
  data: any;
  isGetErrorMsg?: boolean;
}

const editVOCStatusAndCategory = async ({ vocId, data, isGetErrorMsg = false }: EditVOCStatusAndCategoryProps) => {
  try {
    const response = await api.post(`/api-admin/vocs/${vocId}`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, 'VOC 상태 및 카테고리 수정');
  }
};

export {
  getVOCList,
  getVOCCategoryOptions,
  getVOCStatusOptions,
  addVOC,
  postVOCComment,
  getVOCDetail,
  editVOCStatusAndCategory,
};
