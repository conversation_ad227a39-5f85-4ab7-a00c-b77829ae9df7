import dayjs from 'dayjs';
import { format as dateFnsFormat, parse } from 'date-fns';
import { ko } from 'date-fns/locale';
import numberFormat from 'number-format.js';
import type { PaginationInfo } from '@type/pagination';

/**
 * 날짜 포맷팅 유틸리티
 */
export const dateUtils = {
  /**
   * Date 객체를 문자열로 변환
   */
  format: (date: Date | string | null, formatStr = 'yyyy.MM.dd'): string => {
    if (!date) return '';
    return dateFnsFormat(new Date(date), formatStr, { locale: ko });
  },

  /**
   * 문자열을 Date 객체로 변환
   */
  parse: (dateStr: string, formatStr = 'yyyy.MM.dd'): Date => {
    return parse(dateStr, formatStr, new Date());
  },

  /**
   * 날짜 범위 생성
   */
  getDateRange: (startDate: Date, endDate: Date) => {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const range = [];

    let current = start;
    while (current.isBefore(end) || current.isSame(end)) {
      range.push(current.toDate());
      current = current.add(1, 'day');
    }
    return range;
  },

  /**
   * 이번달의 시작일과 마지막일 반환
   */
  getCurrentMonthRange: () => {
    const start = dayjs().startOf('month');
    const end = dayjs().endOf('month');
    return {
      startDate: start.toDate(),
      endDate: end.toDate(),
    };
  },

  /**
   * 지난주의 월요일과 일요일 반환
   */
  getLastWeekRange: () => {
    const monday = dayjs().subtract(1, 'week').startOf('week').add(1, 'day');
    const sunday = monday.add(6, 'day');
    return {
      startDate: monday.toDate(),
      endDate: sunday.toDate(),
    };
  },
};

/**
 * Date 객체를 문자열 패턴의 날짜로 변경합니다.
 */
export function dateToString(date, format = 'YYYY.MM.DD') {
  return dayjs(date).format(format);
}

/**
 * 문자열 패턴의 날짜를 Date 객체로 반환합니다.
 */
export function stringToDate(value, format = 'YYYY.MM.DD') {
  return dayjs(value, format);
}

// 시간 문자열 포맷팅
//yyyy-MM-ddTHH:ss.SSSSSS 형식 "2023-09-15T15:05:57.763336"
export function formatTime(date, format) {
  let returnString = '';
  switch (format) {
    case 'yyyy-MM-dd HH:mm':
      returnString = date.substring(0, 16).replace('T', ' ');
      break;
    case 'yyyy.MM.dd HH:mm':
      returnString = date.substring(0, 16).replace(/-/g, '.').replace('T', ' ');
      break;
    case 'MM-dd HH:mm': //MM-dd HH:mm
      returnString = date.substring(5, 16).replace('T', ' ');
      break;
    case 'MM.dd HH:mm': //MM-dd HH:mm
      returnString = date.substring(5, 16).replace(/-/g, '.').replace('T', ' ');
      break;
  }
  return returnString;
}

/**
 * Number 타입을 숫자포멧 적용된 문자열로 변환합니다.
 * @link http://mottie.github.com/javascript-number-formatter/
 * @param value
 * @param format
 */
export function numberToString(value, format = '#,##0.00') {
  return numberFormat(format, value);
}

/**
 *
 * @returns 지난주 일요일 구하기
 */
export function getLastWeekSunday() {
  return dayjs().subtract(parseInt(dayjs().format('d')), 'day');
}

/**
 * byte 데이터를 mega byte 데이터로 변환합니다.
 * @param value - byte
 * @return {number} - mega byte
 */
export function byteToMegabyte(value) {
  return value / 1000000;
}

/**
 * 정규식을 통해 숫자에 ,를 추가합니다.
 *
 * @param value
 * @return {string}
 */
export function numberFilter(value = String()) {
  return String(value).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * n입력에 따른 n번째 소수점 반올림
 */
export function fixRound(v, n) {
  return Math.round(v * 10 ** n) / 10 ** n;
}

export function getDateArray(startDate, endDate) {
  let arr = [];
  let dt = new Date(startDate);
  while (dt <= new Date(endDate)) {
    arr.push(new Date(dt));
    dt.setDate(dt.getDate() + 1);
  }
  return arr;
}

export function getYearAndMonthArray(startDate, endDate) {
  let yearArray = [];
  let monthArray = [];
  let dt = new Date(startDate);

  while (dt <= new Date(endDate)) {
    let month = dt.getMonth() + 1;
    let year = dt.getFullYear();
    let monthStr = `${year}-${month.toString().padStart(2, '0')}`;
    if (!yearArray.includes(year)) {
      yearArray.push(year);
    }
    if (!monthArray.includes(monthStr)) {
      monthArray.push(monthStr);
    }
    dt.setDate(dt.getDate() + 1);
  }
  return {
    yearArray,
    monthArray,
  };
}

export function getMonthDateRange() {
  return dayjs().subtract(31, 'day').format('YYYY-MM-DD') + '~' + dayjs().subtract(1, 'day').format('YYYY-MM-DD');
}

export function parseTableData(data) {
  let result = data.map((obj) => {
    let newArr = [];
    Object.keys(obj).forEach((key) => {
      let newObject = {};
      newObject['classes'] = key; // 기존 키를 'classes'로 맵핑
      newObject['text'] = obj[key]; // 기존 값을 'text'로 맵핑
      newArr.push(newObject);
    });
    return newArr;
  });

  return result;
}

export const getLabel = (value, options): string => {
  return options.find((opt) => opt.value === value)?.label ?? '';
};

export const getValue = (label: string, options): string => {
  return options.find((opt) => opt.label === label)?.value ?? '';
};

type ItemType = Record<string, any>;

export function flattenValues(input: ItemType | ItemType[]): ItemType | ItemType[] {
  const flattenItem = (item: ItemType): ItemType => {
    const result: ItemType = {};
    for (const key in item) {
      if (item[key] && typeof item[key] === 'object' && 'label' in item[key] && 'value' in item[key]) {
        result[key] = item[key].value;
      } else {
        result[key] = item[key];
      }
    }
    return result;
  };

  if (Array.isArray(input)) {
    return input.map(flattenItem);
  } else {
    return flattenItem(input);
  }
}

export const addSeqNo = (data: ItemType[], pageInfo?: PaginationInfo): ItemType[] => {
  if (pageInfo) {
    const startNo = pageInfo.currentPage * pageInfo.pageSize;
    return _.map(data, (item, index) => ({
      no: startNo + index + 1,
      ...item,
    }));
  } else {
    return _.map(data, (item, index) => ({
      no: index + 1,
      ...item,
    }));
  }
};
