@use '@styles/utils/mixin' as m;

input {
  background-color: white;
  color: black;
  border: 1px solid var(--g_06);

  &.default {
    &:focus {
      outline: 3px solid var(--g_01);
    }
  }

  &::placeholder {
    color: var(--g_05);
  }

  &:read-only {
    background-color: var(--g_04);
  }

  &:disabled {
    background-color: var(--g_04);
    color: var(--g_06);
  }

  &.c_input {
    border-radius: 0.25rem;
    padding: 0 0.75rem;
    font-size: 1rem;
    line-height: 1.15rem;
    outline: none;
    @include m.ellipsis();

    &.left {
      text-align: left;
    }
    &.right {
      text-align: right;
    }

    &.largest {
      min-height: 44px;
    }

    &.large {
      min-height: 40px;
    }

    &.medium {
      min-height: 36px;
    }

    &.small {
      min-height: 32px;
    }

    &.smallest {
      min-height: 28px;
    }
  }
}

.c_input_wrapper,
.c_searchbar_wrapper {
  &.error {
    @include m.after_url('@assets/images/icon/icon_error.svg');
    border: 1px solid var(--error);
    input {
      &:focus {
        outline: 3px solid var(--error);
      }
    }
  }
  &.warning {
    @include m.after_url('@assets/images/icon/icon_warning.svg');
    border: 1px solid var(--warning);
    input {
      &:focus {
        outline: 3px solid var(--warning);
      }
    }
  }
  &.confirm {
    @include m.after_url('@assets/images/icon/icon_confirm.svg');
    border: 1px solid var(--confirm);
    input {
      &:focus {
        outline: 3px solid var(--confirm);
      }
    }
  }
}

.c_input_wrapper {
  display: inline-block;
  border-radius: 4px;

  &.error,
  &.warning,
  &.confirm {
    position: relative;

    &::after {
      @include m.content_without_url(1rem, 1rem);
      position: absolute;
      top: 50%;
      right: 0.75rem;
      transform: translateY(-50%);
    }

    .c_input {
      display: block;
      width: 100%;
      padding-right: 2.25rem;
      border: 0;
    }
  }
}

.c_searchbar_wrapper {
  width: 100%;
  position: relative;
  border-radius: 4px;

  .searchbar {
    display: block;
    width: 100%;
    padding-left: 2.25rem;
    &.fill {
      padding: 0 2.25rem;
    }
  }

  .i_circle_close {
    right: 0.75rem;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    padding: 0;

    &:hover {
      background-color: transparent !important;
    }
  }

  &::after,
  &::before,
  .i_circle_close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }

  .c_search_result_lists {
    display: none;
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    z-index: 1;
    width: 100%;
    max-height: 200px;
    border-radius: 4px;
    overflow-y: auto;
    color: var(--font_default);
    background-color: var(--control_box);
    box-shadow: var(--shadow_l);
    border: 1px solid var(--g_06);

    .c_search_result_list {
      width: 100%;
      &_item {
        @include m.ellipsis();
        padding: 0.375rem 0.5rem;
        text-align: left;
        cursor: pointer;

        &:not(:last-child) {
          border-bottom: 1px solid var(--g_05);
        }
        &:hover {
          background-color: rgba(var(--p_05), 0.2);
        }
      }
    }
  }

  &::before {
    @include m.content('@assets/images/icon/icon_search.svg', 1rem, 1rem);
    left: 0.75rem;
  }

  &[aria-disabled],
  &[data-readonly] {
    @include m.before_url('@assets/images/icon/icon_search_disabled.svg');
    .i_circle_close {
      display: none;
    }
  }

  &.open {
    .c_search_result_lists {
      display: block;
    }
  }

  &.error,
  &.warning,
  &.confirm {
    .searchbar {
      padding: 0 2.25rem;
    }
    &::after {
      @include m.content_without_url(1rem, 1rem);
      right: 0.75rem;
    }
  }
}

@include m.bp_large() {
  input {
    &.c_input {
      padding: 0 0.625rem;
      font-size: 0.875rem;
      line-height: 1rem;

      &.largest {
        min-height: 40px;
      }

      &.large {
        min-height: 36px;
      }

      &.medium {
        min-height: 32px;
      }

      &.small {
        min-height: 28px;
      }

      &.smallest {
        min-height: 24px;
      }
    }
  }
}

@include m.bp_medium() {
  input {
    &.c_input {
      padding: 0 0.5rem;
      font-size: 0.75rem;
      line-height: 0.875rem;
      min-height: 28px;

      &.largest {
        min-height: 36px;
      }

      &.large {
        min-height: 32px;
      }

      &.medium {
        min-height: 28px;
      }

      &.small {
        min-height: 24px;
      }

      &.smallest {
        min-height: 20px;
      }
    }
  }
}
