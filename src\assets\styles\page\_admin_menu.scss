@use '@styles/utils/mixin' as m;
@use '@styles/utils/variables' as v;

.manage_admin_menu {
  .manage_admin_contents {
    @include m.flex();

    .manage_admin_contents_left {
      min-width: 260px;
      margin-right: 1.25rem;

      .c_sub_title {
        margin-bottom: 0;
      }

      // 카테고리 리스트
      .admin_menu_category_wrap {
        .admin_menu_button {
          @include m.flex(start, flex-end, row);
          gap: 0.5rem;

          .c_button {
            .c_button_text {
              font-size: 12px;
            }
          }
        }

        .admin_menu_category {
          padding: 1rem;
          border-radius: 0.25rem;
          background-color: var(--lnb_bg);

          .admin_menu_category_top {
            .admin_menu_category_parent_wrap {
              padding: 1rem 0;
              border-top: 1px solid var(--g_06);

              &:last-child {
                border-bottom: 1px solid var(--g_06);
              }

              .admin_menu_category_parent {
                position: relative;
                cursor: pointer;
                padding: 0.5rem 0.5rem 0.5rem 2.25rem;
                font-weight: 600;

                &::before {
                  position: absolute;
                  left: 0.625rem;
                  top: 50%;
                  transform: translateY(-50%);
                  @include m.content('@assets/images/icon/icon_drag_handle_black.svg', 1.1rem, 1.1rem);
                }

                &:hover {
                  background-color: var(--g_03);
                }

                &.clicked {
                  transition: all 0.3s;
                  border-radius: 0.25rem;
                  color: white;
                  background-color: var(--p_04);
                  @include m.before_url('@assets/images/icon/icon_drag_handle_white.svg');
                }
              }

              .admin_menu_category_child_wrap {
                display: flex;
                flex-direction: column;

                .admin_menu_category_child {
                  position: relative;
                  cursor: pointer;
                  margin-left: 1rem;
                  padding: 0.5rem 0.5rem 0.5rem 2rem;
                  display: flex;
                  align-items: center;

                  &::before {
                    position: absolute;
                    left: 0.5rem;
                    top: 50%;
                    transform: translateY(-50%);
                    @include m.content('@assets/images/icon/icon_drag_handle_black.svg', 1.1rem, 1.1rem);
                  }

                  &:hover {
                    background-color: var(--g_03);
                  }

                  &.clicked {
                    transition: all 0.3s;
                    border-radius: 0.25rem;
                    color: white;
                    background-color: var(--p_04);
                    @include m.before_url('@assets/images/icon/icon_drag_handle_white.svg');
                  }
                }
              }
            }
          }
        }
      }
    }

    .manage_admin_contents_right {
      padding-left: 1.25rem;
      width: 100%;

      // 조회 내용 없을 경우
      .no_result {
        @include m.flex(center, center);
        width: 100%;
        height: 600px;
        border-radius: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--g_06);
      }

      .admin_menu_info_wrap {
        .menu_required {
          &::after {
            color: var(--error);
          }
        }
      }

      .admin_menu_info_detail {
        margin-bottom: 1rem;

        .c_table_container_header {
          .right_cont {
            @include m.flex(center, end);
            gap: 0.25rem;
          }
        }

        // button_wrapper
        .button_wrapper {
          @include m.flex(center, end);
          gap: 0.25rem;
        }
      }

      .admin_menu_info_child_menu {
        height: calc(100vh - 500px);

        .c_table_container_header {
          .right_cont {
            @include m.flex(center, end);
            gap: 0.25rem;
          }
        }
      }
    }
  }
}

.menu_icon_select {
  .c_selected_label {
    position: relative;
    padding: 0 2.5rem;

    &::before {
      content: '';
      position: absolute;
      left: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      width: 1.5rem;
      height: 1.5rem;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
    }

    @each $icon in v.$icons {
      &[data-selected-value='#{$icon}'] {
        &::before {
          background-image: url('@assets/images/icon/icon_#{$icon}_black.svg');
        }
      }
    }

    @each $icon in v.$editor_icons {
      &[data-selected-value='#{$icon}'] {
        &::before {
          background-image: url('@assets/images/icon/icon_editor_#{$icon}_black.svg');
        }
      }
    }
  }
}

.menu_icon_select_option_list {
  .c_select_lists_inner {
    .c_select_option_item {
      padding: 0.5rem 2.5rem;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        left: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        width: 1.5rem;
        height: 1.5rem;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }

      @each $icon in v.$icons {
        &[data-value='#{$icon}'] {
          &::before {
            background-image: url('@assets/images/icon/icon_#{$icon}_black.svg');
          }
        }
      }

      @each $icon in v.$editor_icons {
        &[data-value='#{$icon}'] {
          &::before {
            background-image: url('@assets/images/icon/icon_editor_#{$icon}_black.svg');
          }
        }
      }
    }
  }
}

[theme='light'] {
  @mixin icon_styles($icon_name) {
    &.i_#{$icon_name} {
      &::before {
        background-image: url('@assets/images/icon/icon_#{$icon_name}_black.svg') !important;
      }
      &.clicked {
        &::before {
          background-image: url('@assets/images/icon/icon_#{$icon_name}_white.svg') !important;
        }
      }
    }
  }

  @mixin editor_icon_styles($editor_icon_name) {
    &.i_#{$editor_icon_name} {
      &::before {
        background-image: url('@assets/images/icon/icon_editor_#{$editor_icon_name}_black.svg') !important;
      }
      &.clicked {
        &::before {
          background-image: url('@assets/images/icon/icon_editor_#{$editor_icon_name}_white.svg') !important;
        }
      }
    }
  }

  .manage_admin_menu {
    .no_dnd {
      .admin_menu_category_parent {
        @each $icon in v.$icons {
          @include icon_styles($icon);
        }

        @each $icon in v.$editor_icons {
          @include editor_icon_styles($icon);
        }
      }

      .admin_menu_category_child_wrap {
        .admin_menu_category_child {
          @each $icon in v.$icons {
            @include icon_styles($icon);
          }

          @each $icon in v.$editor_icons {
            @include editor_icon_styles($icon);
          }
        }
      }
    }
  }
}
