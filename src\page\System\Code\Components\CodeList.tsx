import { AgGridReact } from '@ag-grid-community/react';
import Button from '@components/common/Button/Button';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  CellValueChangedEvent,
  ColDef,
  GridSizeChangedEvent,
  RowDragEndEvent,
  RowSelectedEvent,
} from '@ag-grid-community/core';
import SubTitle from '@components/common/Title/SubTitle';
import { useConfirmStore } from '@store/useConfirmStore';
import Grid from '@components/common/Grid/Grid';
import { useCodeStore } from '@page/System/Code/store/useCodeStore';
import { deleteCode, getCodeList, postCode } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';

interface CodeData {
  code: string;
  groupCode: string;
  name: string;
  sort: number;
  useYn: string;
  dateInfo: {
    createUser: string;
    createDate: string;
    updateUser: string;
    updateDate: string;
  };
  isNew?: boolean;
}

const CodeList = () => {
  const { groupCode, setAlertState } = useCodeStore();

  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  const gridRef = useRef<AgGridReact<CodeData>>(null);
  const [isSameData, setIsSameData] = useState(false);
  const [originalRowData, setOriginalRowData] = useState<CodeData[]>([]);
  const [rowData, setRowData] = useState<CodeData[]>([]);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    { headerName: 'No.', field: 'sort', width: 110, rowDrag: true, sortable: true, unSortIcon: true },
    { headerName: '코드', field: 'code', flex: 1, editable: (params) => params.data?.isNew },
    {
      headerName: '그룹 코드',
      field: 'groupCode',
      flex: 1,
    },
    { headerName: '코드명', field: 'name', flex: 1, editable: true },
    {
      headerName: '사용여부',
      field: 'useYn',
      width: 90,
      editable: true,
      cellEditor: 'agSelectCellEditor',
      cellEditorParams: { values: ['Y', 'N'] },
    },
    { headerName: '생성자', field: 'dateInfo.createUser', width: 100 },
    { headerName: '생성일', field: 'dateInfo.createDate', flex: 1 },
    { headerName: '수정자', field: 'dateInfo.updateUser', width: 100 },
    { headerName: '수정일', field: 'dateInfo.updateDate', flex: 1 },
  ]);

  const [selectedRows, setSelectedRows] = useState<CodeData[]>([]);

  // 정렬번호(sort 값) 재정렬 로직
  const parsedSortNumber = (data: CodeData[]) => {
    return data.map((item, index) => ({
      ...item,
      sort: index + 1,
    }));
  };

  // 코드 추가를 위한 행 추가 로직
  const addRow = () => {
    setRowData((prev) => {
      const prevData = prev;
      const newData = {
        code: '',
        groupCode: groupCode.code, // 위에서 받아오는 기본 default 값 > 편집 불가능
        name: '',
        sort: prevData[prevData.length - 1]?.sort + 1 || 1,
        useYn: 'Y', // 위에서 받아오는 기본 default 값 > 편집 가능
        dateInfo: { createUser: '', createDate: '', updateUser: '', updateDate: '' },
        isNew: true, // 새로 추가된 데이터 표시 > 편집 가능 ...> false 면 편집 불가능
        checked: false,
      };

      setTimeout(() => {
        // 현재 No. 컬럼의 정렬 상태 확인
        const columnState = gridRef.current?.api.getColumnState();
        const sortColumn = columnState?.find((col) => col.colId === 'sort');
        const isSortColumnSorting = sortColumn?.sort;

        let rowIndex = newData?.sort - 1; // 기본은 마지막 인덱스
        if (isSortColumnSorting === 'desc') rowIndex = 0; // 내림차순정렬일 경우만 첫번째 인덱스로 변경

        gridRef.current!.api.ensureIndexVisible(rowIndex);
        gridRef.current!.api.setFocusedCell(rowIndex, 'name');
        setTimeout(() => {
          gridRef.current!.api.startEditingCell({
            rowIndex: rowIndex,
            colKey: 'code',
          });
        }, 100);
      }, 100);

      return [...prevData, newData];
    });
  };

  const onRowSelected = useCallback((event: RowSelectedEvent) => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data) || [];
    setSelectedRows(selectedData);
  }, []);

  const onCellValueChanged = useCallback(
    (event: CellValueChangedEvent) => {
      // 셀 값이 변경되었을 때 호출되는 함수
      const {
        data,
        colDef: { field },
        oldValue,
      } = event;
      const { code } = data;

      // 원본 데이터에서 동일한 코드를 가진 항목 찾기
      const originalItem = originalRowData.find((item) => item.code === code);

      // 원본 데이터가 존재하고, 현재 데이터와 비교하여 변경 여부 확인
      if (originalItem) {
        const isSameData = originalItem[field] === oldValue;
        setIsSameData(isSameData);
      }
    },
    [originalRowData]
  );

  const onRowDragEnd = useCallback((event: RowDragEndEvent) => {
    // 드래그가 끝난 후 모든 행 데이터를 가져옴
    const updatedData: CodeData[] = [];

    // 드래그된 행의 인덱스 추적
    const draggedRowIndex = event.node.rowIndex;

    event.api.forEachNode((node) => {
      if (node.data) {
        updatedData.push(node.data);
      }
    });

    // 드래그 후 스크롤 위치 유지
    const viewPort = document.querySelector('.ag-body-viewport');
    const viewPortScrollTop = viewPort?.scrollTop;

    // sort 번호 재정렬
    const reorderedData = parsedSortNumber(updatedData);

    // 업데이트된 데이터 설정
    setRowData(reorderedData);

    // 데이터 업데이트 후 드래그된 행에 포커스 유지
    setTimeout(() => {
      viewPort?.scrollTo({
        top: viewPortScrollTop,
      });

      // 드래그된 행에 포커스 유지
      const rowNode = event.api.getDisplayedRowAtIndex(draggedRowIndex);
      rowNode?.setSelected(true);
    }, 100);
  }, []);

  const getCodeListData = async () => {
    const data = await getCodeList({ code: groupCode.code });
    const parsedData = parsedSortNumber(data);
    const newData = parsedData?.map((item) => ({
      ...item,
      isNew: false,
    }));

    setOriginalRowData(newData);
    setRowData(newData);
  };

  const handleDeletePop = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: (
        <>
          <hr className="my-2" />
          <p className="font-bold text-red-500">{selectedRows.map((row) => row.name).join(', ')}</p>
          <hr className="my-2" />
          <p>코드를 삭제하시겠습니까?</p>
          <p>삭제된 코드는 복구할 수 없습니다.</p>
        </>
      ),
      onConfirm: () => {
        handleDeleteRow();
        initConfirmState();
      },
      onCancel: initConfirmState,
    });
  };

  // 선택한 Row 삭제 버튼 클릭 이벤트 핸들러 >> 신규데이터가 있을때, 복합적인 로직 필요
  const handleDeleteRow = async () => {
    const hasNewData = selectedRows.find((code) => code.isNew);

    // 신규 데이터가 있을 경우, 신규 데이터만 삭제
    if (hasNewData) {
      const filteredSelectedRows = selectedRows.filter((row) => row.isNew);
      setRowData((prev) => parsedSortNumber(prev.filter((row) => !filteredSelectedRows.includes(row))));
    }

    // 신규 데이터가 없을 경우, 모든 데이터 삭제
    const parsedDeleteData = {
      groupCode: groupCode.code,
      codes: selectedRows.map(({ code }) => code),
    };

    if (parsedDeleteData.codes.length === 0) return;

    const message = await deleteCode({ data: parsedDeleteData });
    if (message) {
      getCodeListData();
      activeAlert(message);
    }
  };

  // 저장 버튼 클릭 이벤트 핸들러
  const handleSave = async () => {
    // 원본 데이터와 현재 데이터가 같으면 저장하지 않음
    if (isSameData) return;
    const emptyCode = rowData.find((row) => !row.code);
    const emptyName = rowData.find((row) => !row.name);

    // 코드, 코드명이 비어있는 값인 경우
    if (emptyCode || emptyName) {
      activeAlert('코드 또는 코드명을 입력해주세요.');
      return;
    }

    // 저장할 데이터 형식으로 변환
    const parsedPostData = {
      groupCode: groupCode.code,
      codes: rowData.map(({ code, name, useYn }) => ({
        name,
        useYn,
        code,
      })),
    };

    if (parsedPostData.codes.length === 0) return;

    const message = await postCode({ data: parsedPostData });
    if (message) {
      getCodeListData();
      activeAlert(message);
    }
  };

  useEffect(() => {
    if (groupCode.code) {
      getCodeListData();
    }
  }, [groupCode.code]);

  useEffect(() => {
    setIsSameData(
      rowData.length === originalRowData.length &&
        rowData.every((row, index) => {
          const original = originalRowData[index];
          // 텍스트 변화도 감지하기 위해 직접 비교
          return (
            row.code === original.code &&
            row.name === original.name &&
            row.useYn === original.useYn &&
            row.groupCode === original.groupCode &&
            row.sort === original.sort
          );
        })
    );
  }, [rowData, originalRowData]);

  return (
    <>
      {/* 테이블 헤더 영역 */}
      <TableContainerHeader
        leftChildren={<SubTitle>코드 목록</SubTitle>}
        rightChildren={
          <div className="code_list_btn_wrapper">
            <Button
              className="register_btn"
              text="삭제"
              color="red"
              disabled={selectedRows.length < 1}
              onClick={handleDeletePop}
              clickLog={{ buttonSection: '코드 목록' }}
            />
            <Button
              className="register_btn"
              text="저장"
              color="grayscale"
              disabled={isSameData}
              onClick={handleSave}
              clickLog={{ buttonSection: '코드 목록' }}
            />
            <Button
              className="register_btn"
              text="행 추가"
              onClick={addRow}
              clickLog={{ buttonSection: '코드 목록' }}
            />
          </div>
        }
      />
      <div className="code_list">
        <Grid
          sizeReset
          autoSizeStrategy={'onGridSizeChanged'}
          placeholder="데이터가 없습니다. 코드를 추가해주세요."
          ref={gridRef}
          columns={columnDefs}
          rowData={rowData}
          defaultColDef={{
            width: 170,
          }}
          gridOptions={{
            rowDragManaged: true,
            onRowDragEnd,
            onCellValueChanged,
            onRowSelected,
            editType: 'fullRow',
            rowSelection: {
              checkboxes: true,
              headerCheckbox: true,
              mode: 'multiRow',
            },
          }}
        />
      </div>
    </>
  );
};

export default CodeList;
