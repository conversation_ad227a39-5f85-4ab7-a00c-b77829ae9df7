import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import { AuthData } from '@page/Admin/Role/type';
import { Role } from '@page/User/Account/type';
import { UseFormReturn } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import { useUserAccountDetailStore } from '../store/useUserAccountDetailStore';
import { DetailFormValues } from '../type';

/**
 * 사용자 계정 상세 API 훅 Props 인터페이스
 */
interface UseUserAccountDetailAPIProps {
  methods?: UseFormReturn<DetailFormValues>; // 폼 메서드 (선택적)
}

export const useUserAccountDetailAPI = ({ methods }: UseUserAccountDetailAPIProps = {}) => {
  const navigate = useNavigate();
  const params = useParams();
  const USER_ROLE = '/api-admin/user/roles';
  const USER_ACCOUNT = '/api-admin/users';
  const USER_ACCOUNT_DUPLICATE = '/api-admin/users/dup-check';
  const { openAlert } = useAlert();
  const { setRoleListData, setCheckDuplicateId } = useUserAccountDetailStore();
  const { reset } = methods;

  // 권한 목록 조회
  const getRoleList = async () => {
    try {
      const response = await api.get(`${USER_ROLE}`);
      const roleList = response.data.data;
      const parseData = roleList.map(({ name, id }: AuthData): { label: string; value: number } => ({
        label: name,
        value: id,
      }));
      setRoleListData(parseData);
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 계정 상세 조회
  const getAccountDetail = async () => {
    try {
      const id = params.id;
      const response = await api.get(`${USER_ACCOUNT}/${id}`);
      const data = response.data.data;
      const parseCheckRoles =
        data.roles
          .map((role: Role) => role.id) // role.id만 추출
          .filter((id: number | string) => id !== null) || // role.id 가 null 인 경우를 필터링
        []; // roles가 falsy한 값일 경우 빈배열
      reset({
        userId: data.userId,
        name: data.name,
        email: data.email,
        activeYn: data.activeYn,
        roleIds: parseCheckRoles,
      });
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 계정 중복 확인
  const getCheckDuplicateId = async () => {
    try {
      const userId = methods.getValues('userId');
      if (!userId) {
        openAlert('ID는 필수값입니다.');
        return;
      }
      const response = await api.post(`${USER_ACCOUNT_DUPLICATE}`, {
        userId,
      });
      const isDuplicate = response.data.data;
      if (isDuplicate) {
        openAlert('중복된 ID 입니다. 다른 ID를 입력해주세요.');
      } else {
        openAlert('사용가능한 ID 입니다.');
        setCheckDuplicateId(true);
      }
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 계정 등록
  const addAccount = async (data: DetailFormValues) => {
    try {
      const response = await api.post(`${USER_ACCOUNT}`, {
        userId: data.userId,
        name: data.name,
        email: data.email,
        password: data.password,
        activeYn: data.activeYn,
        roleIds: data.roleIds,
      });
      openAlert(response.data.message, () => {
        goList();
      });
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 계정 수정
  const editAccount = async (data: DetailFormValues) => {
    try {
      const id = params.id;
      const response = await api.post(`${USER_ACCOUNT}/${id}`, {
        name: data.name,
        email: data.email,
        password: data.password,
        activeYn: data.activeYn,
        roleIds: data.roleIds,
      });
      openAlert(response.data.message, () => {
        goList();
      });
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  const goList = () => {
    navigate('/user/account/list');
  };

  return { getRoleList, getAccountDetail, getCheckDuplicateId, addAccount, editAccount, goList };
};
