import { useState, useRef, useEffect, HTMLAttributes } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { ko } from 'date-fns/locale';
import { dateUtils } from '@utils/formatter';
import { createPortal } from 'react-dom';
import joinClassName from '@utils/joinClassName';
import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';
// selectedDate : 선택한 날짜 (선택 사항)
// minDate : 선택할 수 있는 최소 날짜 (선택 사항)
// maxDate : 선택할 수 있는 최대 날짜 (선택 사항)
// onClick : 선택한 날짜로 실행할 이벤트 함수

interface CalendarProps {
  selectedDate?: Date | null;
  minDate?: Date | null;
  maxDate?: Date | null;
  includeDates?: Date[] | null;
  onChange?: (date: Date) => void;
  readOnly?: boolean;
  className?: string;
  desc?: string;
  buttonDisabled?: boolean;
  name?: string;
}

// HTMLAttributes에서 onChange를 제외하고 나머지 속성만 상속
type CalendarComponentProps = Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> & CalendarProps;

const Calendar = ({
  selectedDate: initialDate = null,
  minDate = null,
  maxDate = null,
  includeDates = null,
  onChange = () => {},
  readOnly = false,
  className = '',
  desc = '',
  buttonDisabled = false,
  name = '',
  ...props
}: CalendarComponentProps) => {
  const [date, setDate] = useState<Date | null>(initialDate);
  const [isOpen, setIsOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const [calendarPosition, setCalendarPosition] = useState({
    top: 0,
    left: 'auto',
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        calendarRef.current &&
        popupRef.current &&
        !calendarRef.current.contains(event.target as Node) &&
        !popupRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleDateChange = (newDate: Date) => {
    setDate(newDate);
    onChange(newDate);
    setIsOpen(false);
  };

  const toggleCalendar = () => {
    if (!readOnly) {
      setIsOpen(!isOpen);
    }
  };

  useEffect(() => {
    const ref = calendarRef.current;
    const popRef = popupRef.current;
    if (ref && popRef) {
      const setPosition = () => {
        const rect = ref.getBoundingClientRect();
        const popupRect = popRef.getBoundingClientRect();
        const isOverHalf = rect.left > window.innerWidth * 0.5;
        // 만약 캘린더의 위치가 화면 기준 50% 이상이면 오른쪽에 배치
        if (isOverHalf) {
          setCalendarPosition({
            top: rect.bottom + 4,
            left: `${rect.left + rect.width - popupRect.width}px`,
          });
        } else {
          setCalendarPosition({
            top: rect.bottom + 4,
            left: `${rect.left}px`,
          });
        }
      };

      setPosition();

      window.addEventListener('resize', setPosition);
      window.addEventListener('scroll', setPosition);

      return () => {
        window.removeEventListener('resize', setPosition);
        window.removeEventListener('scroll', setPosition);
      };
    }
  }, [isOpen]);

  return (
    <div className={joinClassName('c_calendar_wrapper', className)} ref={calendarRef} {...props}>
      <IconButton
        text={date ? dateUtils.format(date) : '날짜 선택'}
        onClick={toggleCalendar}
        icon={'calendar' as IconType}
        fill="outlined"
        color="grayscale"
        className={joinClassName('c_date_button', isOpen && 'open')}
        disabled={readOnly}
      />

      {isOpen &&
        createPortal(
          <div
            ref={popupRef}
            className="c_calendar_popup"
            style={{
              position: 'fixed',
              top: calendarPosition.top,
              left: calendarPosition.left,
            }}
          >
            <DatePicker
              selected={date}
              onChange={handleDateChange}
              inline
              locale={ko}
              dateFormat="yyyy.MM.dd"
              minDate={minDate}
              maxDate={maxDate}
              renderCustomHeader={({ date: headerDate, decreaseMonth, increaseMonth }) => (
                <div className="c_calendar_header">
                  <IconButton
                    text="이전"
                    onClick={decreaseMonth}
                    iconOnly
                    fill="unfilled"
                    color="grayscale"
                    size='smallest'
                    icon="arrow_left"
                  />
                  <span>{dateUtils.format(headerDate, 'yyyy년 M월')}</span>
                  <IconButton
                    text="다음"
                    onClick={increaseMonth}
                    iconOnly
                    fill="unfilled"
                    color="grayscale"
                    size='smallest'
                    icon="arrow_right"
                  />
                </div>
              )}
            />
            {desc && <p className="c_calendar_desc">{desc}</p>}
            <div className="c_calendar_footer">
              <Button
                text="취소"
                color="grayscale"
                onClick={() => {
                  setDate(initialDate);
                  onChange(initialDate);
                  setIsOpen(false);
                }}
              />
              <Button
                text="확인"
                onClick={() => {
                  if (date) {
                    onChange(date);
                    setIsOpen(false);
                  }
                }}
                color="primary"
              />
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};

export default Calendar;
