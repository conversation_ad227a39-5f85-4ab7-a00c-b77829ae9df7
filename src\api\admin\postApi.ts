import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

// [관리자] 전체 게시글 관리
const API_URL = '/api-admin/bbs-posts';

// 게시판 목록 조회
export const getPostBoardList = async ({ isGetErrorMsg = false }: { isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.get(`${API_URL}/templets`);

    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '전체 게시글 관리 - 게시판 목록 조회');
  }
};

// 전체 게시글 목록 조회
interface GetAllPostParams {
  templetId?: number;
  title?: string;
  page?: number;
  size?: number;
  isGetErrorMsg?: boolean;
}

export const getAllPost = async ({
  templetId,
  title,
  page = 0,
  size = 10,
  isGetErrorMsg = false,
}: GetAllPostParams) => {
  try {
    const params = [templetId !== 0 && `templetId=${templetId}`, title && title.trim() !== '' && `title=${title}`]
      .filter(Boolean)
      .join('&');

    const response = await api.get(`${API_URL}?page=${page}&size=${size}${`&${params}`}`);

    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '전체 게시글 목록 조회');
  }
};

// 게시글 상세 조회
export const getPostDetail = async ({ id, isGetErrorMsg = false }: { id: string; isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.get(`${API_URL}/${id}`);

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시글 상세 조회');
  }
};

// 게시글 등록
interface PostPostsParams {
  templetId: string;
  title: string;
  content: string;
  notiYn: 'Y' | 'N';
  files?: FileList;
  isGetErrorMsg?: boolean;
}

export const postPosts = async ({
  templetId,
  title,
  notiYn,
  files,
  content,
  isGetErrorMsg = false,
}: PostPostsParams) => {
  try {
    const formData = new FormData();

    formData.append('templetId', templetId);
    formData.append('title', title);
    formData.append('content', content);
    formData.append('notiYn', notiYn);

    (files ? Array.from(files) : []).forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post(`${API_URL}/add`, formData);

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시글 등록');
  }
};

// 게시글 수정
interface PutPostParams {
  id: string;
  templetId: string;
  title: string;
  content: string;
  saveFiles?: File[];
  delFileIds?: string[];
  isGetErrorMsg?: boolean;
}

export const PutPost = async ({
  id,
  templetId,
  title,
  content,
  saveFiles,
  delFileIds,
  isGetErrorMsg = false,
}: PutPostParams) => {
  try {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('content', content);
    formData.append('templetId', templetId);

    saveFiles.forEach((file) => formData.append('saveFiles', file));
    delFileIds.forEach((id) => formData.append('delFileIds', id));

    const response = await api.post(`${API_URL}/${id}`, formData);

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시글 수정');
  }
};

// 게시글 삭제
export const deleteSinglePost = async ({ id, isGetErrorMsg = false }: { id: string; isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.post(`${API_URL}/${id}/delete`);

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시글 삭제');
  }
};

// 게시글 다건 삭제
export const deleteMultiPost = async ({ ids, isGetErrorMsg = false }: { ids: string[]; isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.post(`${API_URL}/delete`, { ids });

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시글 삭제');
  }
};
