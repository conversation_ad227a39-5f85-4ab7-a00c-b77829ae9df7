import { getPostDetail } from '@api/admin/postApi';
import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import TextEditor from '@components/common/TextEditor/TextEditor';
import { AdminPostDetailType } from '@page/Admin/Post/type';
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import SubPagesLsit from './components/SubPagesList';

const UserPageDetailPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [data, setData] = useState<AdminPostDetailType | undefined>(undefined);

  const handleGetPostDetail = async () => {
    const response = await getPostDetail({ id });

    setData(response);
  };

  useEffect(() => {
    handleGetPostDetail();
  }, []);

  return (
    <div className="admin_post_detail_wrapper">
      <TableContainer>
        <TableBody>
          <TableBodyRow
            rowData={[
              {
                title: '게시판 유형',
                contents: data?.templetTypeNm,
              },
              {
                title: '게시판 명',
                contents: data?.templetNm,
              },
            ]}
          />
          <TableBodyRow
            rowData={{
              title: '게시글 제목',
              isFullWidth: true,
              contents: data?.title,
            }}
          />
          <TableBodyRow
            rowData={[
              {
                title: '작성자',
                contents: data?.dateInfo.createUser,
              },
              {
                title: '작성 일시',
                contents: data?.dateInfo.createDate,
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '수정자',
                contents: data?.dateInfo.updateUser,
              },
              {
                title: '수정 일시',
                contents: data?.dateInfo.updateDate,
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '공지 여부',
                contents: data?.notiYn,
              },
              {
                title: '첨부파일',
                isFullWidth: true,
                contents: <></>,
              },
            ]}
          />
        </TableBody>
      </TableContainer>
      <SubPagesLsit pageId={id} />
      <div className="admin_post_detail_btns">
        <Button text="목록" onClick={() => navigate('/admin/page/list')} />
        <div className="admin_post_detail_btns_right">
          <Button text="삭제" color="red" />
          <Button text="수정" onClick={() => navigate(`/admin/page/edit/${id}`)} />
        </div>
      </div>
    </div>
  );
};

export default UserPageDetailPage;
