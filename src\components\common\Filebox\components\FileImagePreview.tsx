import ModalPortal from '@components/common/Modal/ModalPortal';
import IconButton from '@components/common/Button/IconButton';
import { useFilePreviewer } from '@components/common/Filebox/store/useFilePreviewer';

const FileImagePreview = () => {
  const { setPreviewModalState, previewImageInfo } = useFilePreviewer();

  const handleClose = () => {
    setPreviewModalState(false);
  };

  if (!previewImageInfo) return null;

  return (
    <ModalPortal onClickedDim={() => setPreviewModalState(false)}>
      <div className="c_filebox_image_preview">
        {previewImageInfo && <img src={previewImageInfo?.url} alt={previewImageInfo?.name} />}
      </div>
      <IconButton
        className="preview_close"
        icon="close"
        iconOnly
        design="circle"
        color="grayscale"
        fill="unfilled"
        size="smallest"
        text="닫기"
        onClick={handleClose}
      />
    </ModalPortal>
  );
};

export default FileImagePreview;
