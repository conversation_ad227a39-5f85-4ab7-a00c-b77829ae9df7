import DefaultModal from '@components/common/Modal/DefaultModal';
import { useForm } from 'react-hook-form';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import Button from '@components/common/Button/Button';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import TextEditor from '@components/common/TextEditor/TextEditor';
import FormTextarea from '@components/common/Form/FormTextarea';
import { Page, PageDTO } from '@page/Admin/Page/type';
import { pageTypeOptions, requiredYnOptions } from '@constants/options';
import { getSubPageDetail, getTemplateOptions, updateSubPage, createSubPage } from '@api/admin/pageApi';
import { useEffect, useState } from 'react';
import useEventBus from '@hooks/useEventBus';

type Props = {
  parentId: string;
  childId: string;
  modalMode: string;
  onClose: () => void;
};

const SubPageUpsertModal = ({ modalMode, onClose, parentId, childId }: Props) => {
  const [subPageInfo, setSubPageInfo] = useState<Page>(null);
  const [templateOptions, setTemplateOptions] = useState([]);
  const [contents, setContents] = useState<string>('');
  const defaultValues = {
    pageName: '',
    filePath: '',
    pageTypeCode: {
      label: pageTypeOptions[0].label,
      value: pageTypeOptions[0].value,
    },
    roleUseYn: {
      label: requiredYnOptions[0].label,
      value: requiredYnOptions[0].value,
    },
    pageContent: '',
    pageLink: '',
    pageDesc: '',
    pageUrl: '',
    boardTempletId: { label: '선택하세요.', value: 0 },
  };

  const formMethods = useForm({ defaultValues });
  const { eventBus } = useEventBus();

  useEffect(() => {
    if (modalMode === 'add') {
      // 모드가 'add'일 경우에만 초기값으로 reset
      formMethods.reset(defaultValues);
      setContents(''); // 에디터 내용도 초기화
    }
    loadData();
  }, [modalMode]);

  const loadData = async () => {
    if (modalMode === 'add') {
      const templateData = await getTemplateOptions(parentId);
      const options = templateData.map((item) => ({
        value: item.id,
        label: item.templetNm,
      }));
      setTemplateOptions(options);
    } else if (modalMode === 'edit' && childId) {
      const [subPageDetail, templateData] = await Promise.all([
        getSubPageDetail(childId),
        getTemplateOptions(parentId),
      ]);
      const options = templateData.map((item) => ({
        value: item.id,
        label: item.templetNm,
      }));
      setTemplateOptions(options);
      setSubPageInfo(subPageDetail);

      if (subPageDetail.pageContent) {
        setContents(subPageDetail.pageContent);
      }

      formMethods.reset({
        pageName: subPageDetail.pageName,
        filePath: subPageDetail.filePath,
        pageTypeCode: {
          label: utils.getLabel(subPageDetail.pageTypeCode, pageTypeOptions),
          value: subPageDetail.pageTypeCode,
        },
        roleUseYn: {
          label: utils.getLabel(subPageDetail.roleUseYn, requiredYnOptions),
          value: subPageDetail.roleUseYn,
        },
        pageContent: subPageDetail.pageContent,
        pageLink: subPageDetail.pageLink,
        pageDesc: subPageDetail.pageDesc,
        pageUrl: subPageDetail.pageUrl,
        boardTempletId: {
          label: utils.getLabel(subPageDetail.boardTempletId, options),
          value: subPageDetail.boardTempletId,
        },
      });
    }
  };

  const getInvalidFields = (formData) => {
    const isBlank = (val) => _.isEmpty(_.trim(val));
    const { pageName, pageTypeCode, boardTempletId, pageLink, pageContent, filePath } = formData;

    const validList = [
      { name: 'pageName', value: pageName },
      { name: 'filePath', value: filePath },
    ];

    if (pageTypeCode === 'C') {
      validList.push({ name: 'pageContent', value: pageContent });
    } else if (pageTypeCode === 'N') {
      validList.push({ name: 'pageLink', value: pageLink });
    } else if (pageTypeCode === 'B') {
      validList.push({ name: 'boardTempletId', value: boardTempletId });
    }

    // 유효하지 않은 필드들만 배열로 반환
    return validList.filter((field) => isBlank(field.value)).map((field) => field.name);
  };

  const getReqPayload = (formData: PageDTO) => {
    const newFormData = utils.flattenValues(formData);

    newFormData.parentId = parentId;

    if (contents) {
      newFormData.pageContent = contents;
    }

    if (newFormData.pageTypeCode === 'C') {
      delete newFormData.pageLink;
      delete newFormData.boardTempletId;
    } else if (newFormData.pageTypeCode === 'N') {
      delete newFormData.pageContent;
      delete newFormData.boardTempletId;
    } else if (newFormData.pageTypeCode === 'B') {
      delete newFormData.pageContent;
      delete newFormData.pageLink;
    } else {
      delete newFormData.pageLink;
      delete newFormData.pageContent;
      delete newFormData.boardTempletId;
    }

    return newFormData;
  };

  const getAlertMsg = (inValidFieldList) => {
    const fieldMessages = {
      pageName: '페이지명을 입력해주세요.',
      boardTempletId: '게시판을 선택해주세요.',
      pageLink: '페이지 링크를 입력해주세요.',
      pageContent: '페이지 콘텐츠를 입력해주세요.',
    };

    const messages = inValidFieldList.map((field) => fieldMessages[field]);
    return messages[0];
  };

  const formSubmit = async (formData: PageDTO) => {
    const reqPayload = getReqPayload(formData);

    const inValidFieldList = getInvalidFields(reqPayload);

    if (!_.isEmpty(inValidFieldList)) {
      const message = getAlertMsg(inValidFieldList);
      utils.showAlert(message);
      return;
    }

    utils.showConfirm('save', async () => {
      const res = modalMode === 'edit' ? await updateSubPage(childId, reqPayload) : await createSubPage(reqPayload);

      if (!res) return;

      utils.showAlert(res, () => {
        onClose();
        eventBus.emit('upsert', { message: 'save' });
      });
    });
  };

  const onClickBtnCancel = () => {
    utils.showConfirm('cancel', onClose);
  };

  return (
    <DefaultModal
      isOpenModal={Boolean(modalMode)}
      className="page_add_modal"
      title={modalMode === 'add' ? '하위 페이지 등록' : '하위 페이지 수정'}
      footer={false}
      setIsOpenModal={onClose}
    >
      <Form methods={formMethods} onSubmit={formSubmit}>
        <TableContainer>
          <colgroup>
            <col width="150px" />
            <col width="calc(50% - 150px)" />
            <col width="150px" />
            <col width="calc(50% - 150px)" />
          </colgroup>
          <TableBody>
            <tr>
              <th className={formMethods.watch('pageTypeCode').value ? 'required' : ''}>유형</th>
              <td
                colSpan={
                  formMethods.watch('pageTypeCode').value === 'B' || formMethods.watch('pageTypeCode').value === 'N'
                    ? 1
                    : 3
                }
              >
                <FormSelectBox name="pageTypeCode" options={pageTypeOptions} placeholder="카테고리" />
              </td>
              {formMethods.watch('pageTypeCode').value === 'B' && (
                <>
                  <th className="required">게시판 선택</th>
                  <td>
                    <FormSelectBox name="boardTempletId" options={templateOptions} placeholder="게시판 선택" />
                  </td>
                </>
              )}
              {formMethods.watch('pageTypeCode').value === 'N' && (
                <>
                  <th className="required">URL</th>
                  <td>
                    <FormInput name="pageLink" placeholder="URL 입력" />
                  </td>
                </>
              )}
            </tr>
            <tr>
              <th className={modalMode === 'add' ? 'required' : ''}>페이지명</th>
              <td>
                <FormInput name="pageName" placeholder="페이지명 입력" />
              </td>
              <th className="required">로그인 필요 여부</th>
              <td>
                <FormSelectBox name="roleUseYn" options={requiredYnOptions} />
              </td>
            </tr>
            <tr>
              <th className="required">페이지 파일 경로</th>
              <td>
                <FormInput name="filePath" placeholder="페이지 파일 경로 입력" />
              </td>
              <th>페이지 URL</th>
              <td>
                <FormInput name="pageUrl" placeholder="페이지 URL 입력" />
              </td>
            </tr>

            <tr>
              <th>페이지 설명</th>
              <td colSpan={3}>
                <FormTextarea name="pageDesc" placeholder="페이지 설명 입력" />
              </td>
            </tr>
            {formMethods.watch('pageTypeCode').value === 'C' && (
              <tr>
                <th className="required">콘텐츠 내용</th>
                <td colSpan={3}>
                  <TextEditor
                    name="content"
                    className="voc_text_editor_content"
                    content={contents}
                    onChange={setContents}
                  />
                </td>
              </tr>
            )}
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button color="grayscale" text="취소" onClick={onClickBtnCancel} />
          <Button type="submit" text="저장" />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default SubPageUpsertModal;
