@use '@styles/utils/mixin' as m;

header {
  position: fixed;
  top: 0;
  left: 75px;
  width: calc(100% - 75px);
  z-index: 9999;
  transition: all 0.3s;
  &.expand {
    left: 250px;
    width: calc(100% - 250px);
  }
}

@include m.bp_large() {
  header {
    left: 65px;
    width: calc(100% - 65px);

    &.expand {
      left: 225px;
      width: calc(100% - 225px);
    }
  }
}



@include m.bp_medium() {
  header {
    left: 55px;
    width: calc(100% - 55px);

    &.expand {
      left: 200px;
      width: calc(100% - 200px);
    }
  }
}


