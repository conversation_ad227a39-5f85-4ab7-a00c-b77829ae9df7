import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

// 서비스 목록 조회
interface GetServiceListParams {
  serviceGroupId?: number;
  isGetErrorMsg?: boolean;
}

// 서비스 목록 조회
interface GetServiceListParams {
  serviceGroupId?: number;
  isGetErrorMsg?: boolean;
}

const getServiceList = async ({ serviceGroupId, isGetErrorMsg = false }: GetServiceListParams) => {
  try {
    const response = serviceGroupId
      ? await api.get(`/api-admin/admin/services/group/${serviceGroupId}`) // 그룹별 서비스 호출
      : await api.get('/api-admin/admin/services'); // 전체 서비스 호출

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '서비스 목록 조회');
  }
};

// 서비스 삭제
interface DeleteServiceDataParams {
  id: number;
  isGetErrorMsg?: boolean;
}

const deleteServiceData = async ({ id, isGetErrorMsg = false }: DeleteServiceDataParams) => {
  try {
    const response = await api.post(`/api-admin/admin/services/${id}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '서비스 삭제');
  }
};

// 서비스 등록
interface AddServiceDataParams {
  data: any;
  isGetErrorMsg?: boolean;
}

const addServiceData = async ({ data, isGetErrorMsg = false }: AddServiceDataParams) => {
  try {
    const response = await api.post('/api-admin/admin/services', data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '서비스 등록');
  }
};

// 서비스 수정
interface UpdateServiceDataParams {
  id: number;
  data: any;
  isGetErrorMsg?: boolean;
}

const updateServiceData = async ({ id, data, isGetErrorMsg = false }: UpdateServiceDataParams) => {
  try {
    const response = await api.post(`/api-admin/admin/services/${id}`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '서비스 수정');
  }
};

export { getServiceList, deleteServiceData, addServiceData, updateServiceData };
