import joinClassName from '@utils/joinClassName';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';

interface FormProps {
  children: React.ReactNode;
  onSubmit: (data: any) => void;
  methods: any;
  className?: string;
}

const Form = ({ children, onSubmit, methods, className }: FormProps) => {
  return (
    <FormProvider {...methods}>
      <form className={joinClassName('c_form', className)} onSubmit={methods.handleSubmit(onSubmit)}>
        {children}
      </form>
    </FormProvider>
  );
};

export default Form;
