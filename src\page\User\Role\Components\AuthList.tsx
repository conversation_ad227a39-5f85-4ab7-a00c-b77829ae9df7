import IconButton from '@components/common/Button/IconButton';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import { useAuthStore } from '@page/User/Role/store/useAuthStore';
import { AuthData } from '@page/User/Role/type';

interface AuthListProps {
  handleAuthInfo: (id: number) => void;
  handleOnClickDeleteAuth: (e: React.MouseEvent, id: number) => void;
}

const AuthList = ({ handleAuthInfo, handleOnClickDeleteAuth }: AuthListProps) => {
  const { authListData, authData } = useAuthStore();

  return (
    <TableContainer className="admin_authority_list">
      <TableHeader sticky>
        <tr>
          <th>권한명</th>
        </tr>
      </TableHeader>
      <TableBody>
        {authListData ? (
          authListData.map((data: AuthData, idx) => (
            <tr key={`auth-list-${idx}`} className={data.id === authData?.id ? 'active' : ''} onClick={() => handleAuthInfo(data.id)}>
              <td>
                <div className="td_inner">
                  <span>{data.name}</span>
                  <IconButton
                    icon="close"
                    iconOnly
                    fill="unfilled"
                    design="circle"
                    color='grayscale'
                    text="삭제"
                    onClick={(e: React.MouseEvent) => handleOnClickDeleteAuth(e, data.id)}
                  />
                </div>
              </td>
            </tr>
          ))
        ) : (
          <tr className="no_result">
            <td>조회된 데이터가 없습니다.</td>
          </tr>
        )}
      </TableBody>
    </TableContainer>
  );
};

export default AuthList;
