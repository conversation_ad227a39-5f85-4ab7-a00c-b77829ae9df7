import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

const SocialLoginSuccess = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const handleAuthCallback = async () => {
            try {
                // URL 파라미터에서 정보 추출
                const urlParams = new URLSearchParams(location.search);
                const success = urlParams.get('success');
                const userId = urlParams.get('userId');
                const userName = urlParams.get('userName');

                // URL Fragment에서 토큰 추출 (보안상 Fragment 사용)
                const fragment = location.hash.substring(1); // # 제거
                const fragmentParams = new URLSearchParams(fragment);
                const accessToken = fragmentParams.get('token');

                if (success === 'true' && accessToken) {
                    // 토큰을 localStorage에 저장 (또는 secure cookie 사용)
                    // localStorage.setItem('accessToken', accessToken);
                    // localStorage.setItem('loginUsername', decodeURIComponent(userName || ''));

                    // 인증 상태 업데이트 (Context API, Redux 등 사용)
                    // setAuthState({ isAuthenticated: true, user: { userId, userName } });

                    console.log('소셜 로그인 성공:', { userId, userName });

                    // 메인 페이지로 리다이렉트
                    // navigate('/', { replace: true });
                } else {
                    throw new Error('토큰을 받지 못했습니다.');
                }
            } catch (err) {
                console.error('소셜 로그인 콜백 처리 오류:', err);
                setError(err.message);

                // 3초 후 로그인 페이지로 리다이렉트
                // setTimeout(() => {
                //     navigate('/social/login', { replace: true });
                // }, 3000);
            } finally {
                setLoading(false);
            }
        };

        handleAuthCallback();
    }, [location, navigate]);

    if (loading) {
        return (
            <div className="auth-callback-loading">
                <div className="spinner"></div>
                <p>로그인 처리 중...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="auth-callback-error">
                <h2>로그인 오류</h2>
                <p>{error}</p>
            </div>
        );
    }

    return null;
};

export default SocialLoginSuccess;