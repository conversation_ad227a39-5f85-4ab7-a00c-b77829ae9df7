import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import "./SocialLoginSuccess.css";

const SocialLoginSuccess = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [userInfo, setUserInfo] = useState(null);

    useEffect(() => {
        const handleAuthCallback = async () => {
            try {
                // URL 파라미터에서 정보 추출
                const urlParams = new URLSearchParams(location.search);
                const userId = urlParams.get('userId');
                const userName = urlParams.get('userName');
                const provider = urlParams.get('provider');
                const providerId = urlParams.get('providerId');

                // URL Fragment에서 토큰 추출 (보안상 Fragment 사용)
                const fragment = location.hash.substring(1); // # 제거
                const fragmentParams = new URLSearchParams(fragment);
                const accessToken = fragmentParams.get('token');

                if (accessToken) {
                    // 토큰을 localStorage에 저장
                    // localStorage.setItem('accessToken', accessToken);
                    // localStorage.setItem('loginUsername', decodeURIComponent(userName || ''));

                    // 사용자 정보 가져오기
                    try {
                        const userResponse = await fetch('/api/my/info', {
                            headers: {
                                'Authorization': `Bearer ${accessToken}`
                            }
                        });
                        const userData = await userResponse.json();
                        if (userData.data) {
                            setUserInfo(userData.data);
                        } else {
                            setUserInfo({
                                userId: userId,
                                name: decodeURIComponent(userName || ''),
                                email: null,
                                socialAccounts: [
                                    {
                                        provider: provider,
                                        providerId: providerId,
                                        profileImageUrl: null
                                    }
                                ]
                            });
                        }
                    } catch (userError) {
                        console.warn('사용자 정보 조회 실패, 기본 정보 사용:', userError);
                        setUserInfo({
                            userId: userId,
                            name: decodeURIComponent(userName || ''),
                            email: null,
                            socialAccounts: [
                                {
                                    provider: provider,
                                    providerId: providerId,
                                    profileImageUrl: null
                                }
                            ]
                        });
                    }

                    console.log('로그인 성공:', { userId, userName, accessToken });
                } else {
                    throw new Error('토큰을 받지 못했습니다.');
                }
            } catch (err) {
                console.error('소셜 로그인 콜백 처리 오류:', err);
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };

        handleAuthCallback();
    }, [location, navigate]);

    const handleLogout = () => {
        // localStorage.removeItem('accessToken');
        // localStorage.removeItem('loginUsername');
        navigate('/social', { replace: true });
    };

    const handleGoHome = () => {
        navigate('/', { replace: true });
    };

    const handleUnlinkSocialAccount = async (provider, providerId) => {
        if (!confirm(`${provider} 계정 연동을 해제하시겠습니까?`)) {
            return;
        }

        try {
            const token = localStorage.getItem('accessToken');
            const response = await fetch(`/auth/social/unlink`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                // data: {
                //     provider: provider,
                //     providerId: providerId
                // }
            });

            if (response.data.success) {
                // 사용자 정보 다시 조회하여 업데이트
                const userResponse = await fetch('/api/my/info', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (userResponse.data.success) {
                    setUserInfo(userResponse.data.data);
                }

                alert(`${provider} 계정 연동이 해제되었습니다.`);
            } else {
                alert('연동 해제에 실패했습니다.');
            }
        } catch (error) {
            console.error('소셜 계정 연동 해제 오류:', error);
            alert('연동 해제 중 오류가 발생했습니다.');
        }
    };

    if (loading) {
        return (
            <div className="auth-callback-loading">
                <div className="spinner"></div>
                <p>로그인 처리 중...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="auth-callback-error">
                <h2>로그인 오류</h2>
                <p>{error}</p>
                <button
                    className="error-button"
                    onClick={() => navigate('/social', { replace: true })}
                >
                    로그인 페이지로 돌아가기
                </button>
            </div>
        );
    }

    return (
        <div className="login-success-container">
            <div className="login-success-card">
                <div className="success-header">
                    <div className="success-icon">✓</div>
                    <h2>로그인 성공!</h2>
                    <p>성공적으로 로그인했습니다.</p>
                </div>

                {userInfo && (
                    <div className="user-info-section">
                        <h3>사용자 정보</h3>
                        <div className="user-info-card">
                            <div className="user-avatar">
                                {userInfo.profileImage ? (
                                    <img src={userInfo.profileImage} alt="프로필 이미지" />
                                ) : (
                                    <div className="avatar-placeholder">
                                        {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : '?'}
                                    </div>
                                )}
                            </div>
                            <div className="user-details">
                                <div className="info-row">
                                    <span className="info-label">ID:</span>
                                    <span className="info-value">{userInfo.userId || '정보 없음'}</span>
                                </div>
                                <div className="info-row">
                                    <span className="info-label">이메일:</span>
                                    <span className="info-value">{userInfo.email || '정보 없음'}</span>
                                </div>
                                <div className="info-row">
                                    <span className="info-label">이름:</span>
                                    <span className="info-value">{userInfo.name || '정보 없음'}</span>
                                </div>

                                {/* 소셜 계정 정보 표시 */}
                                {userInfo.socialAccounts && userInfo.socialAccounts.length > 0 && (
                                    <div className="social-accounts-section">
                                        <div className="info-row">
                                            <span className="info-label">연결된 소셜 계정:</span>
                                        </div>
                                        {userInfo.socialAccounts.map((account, index) => (
                                            <div key={index} className="social-account-item">
                                                <div className="social-account-info">
                                                    <div className="social-account-details">
                                                        <div className="social-account-header">
                                                            <span className="social-provider-text">
                                                                {account.provider.toUpperCase()}
                                                            </span>
                                                            <span className="social-account-name">
                                                                {account.name || account.displayName || '이름 없음'}
                                                            </span>
                                                        </div>
                                                        <div className="social-account-id">
                                                            ID: {account.providerId || account.socialId || '정보 없음'}
                                                        </div>
                                                        {account.email && (
                                                            <div className="social-account-email">
                                                                {account.email}
                                                            </div>
                                                        )}
                                                    </div>
                                                    <button
                                                        className="unlink-button"
                                                        onClick={() => handleUnlinkSocialAccount(
                                                            account.provider,
                                                            account.providerId || account.socialId
                                                        )}
                                                        title="연동 해제"
                                                    >
                                                        연동 해제
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                )}

                <div className="action-buttons">
                    <button className="secondary-button" onClick={handleLogout}>
                        로그아웃
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SocialLoginSuccess;