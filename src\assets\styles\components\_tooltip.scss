$c_tooltip-size: 1.25rem;

.c_tooltip {
  display: inline-block;
  width: $c_tooltip-size;
  height: $c_tooltip-size;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;

  &.theme_light {
    background-image: url('@assets/images/icon/icon_tooltip_light.svg');
  }

  &.theme_dark {
    background-image: url('@assets/images/icon/icon_tooltip_dark.svg');
  }

  &.is-hoverable:hover {
    filter: brightness(1.2); // 밝기 증가
    transform: scale(1.1); // 살짝 확대
    cursor: pointer;
    transition:
      transform 0.2s ease-in-out,
      filter 0.2s ease-in-out;
  }
}
