@use "@styles/utils/mixin" as *;
.swiper {
  &-button {
    &-prev,
    &-next {
      width: 50px;
      height: 50px;
      &::after {
        @include flex(center, center);
        width: 100%;
        height: 100%;
        background-size: contain;
        background-color: rgba(white, 0.5);
        color: rgba(var(--p_05), 0.3);
        font-size: 2rem;
      }
    }
    
    &-disabled {
      color: rgba(black, 0.3);
    }
    // &-prev {
    //   @include after_url('@assets/images/icon/icon_arrow_left_white.svg');
    // }
    // &-next {
    //   @include after_url('@assets/images/icon/icon_arrow_right_white.svg');
    // }
  }
}
