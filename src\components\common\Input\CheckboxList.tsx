import { CheckboxOptionProps } from './types';
import joinClassName from '@utils/joinClassName';
import Checkbox from '@components/common/Input/Checkbox';
import ErrorMsg from '@components/common/Error/ErrorMsg';

interface CheckboxListProps {
  options: CheckboxOptionProps[];
  checkedValues: CheckboxOptionProps[];
  setCheckedValues: React.Dispatch<React.SetStateAction<CheckboxOptionProps[]>>;
  className?: string;
  error?: string;
  hasLabel?: boolean;
}

const CheckboxList = ({
  options, // 체크박스 option 리스트:  {label, value}[]
  checkedValues, // 체크된 options값들이 담긴 state
  setCheckedValues, // 체크된 option값들을 업데이트해주는 state 함수
  hasLabel, // 체크박스 리스트 제목의 표시여부를 결정하는 boolean
  className, // 추가 클래스 요소
  error,
}: CheckboxListProps) => {

  const handleCheckboxChange = (idx: number) => {
    const selectedOption = options[idx];
    const isAlreadyChecked = checkedValues.some(item => item.value === selectedOption.value);

    let newChecked: CheckboxOptionProps[];

    if (isAlreadyChecked) {
      // 체크 해제
      newChecked = checkedValues.filter(item => item.value !== selectedOption.value);
    } else {
      // 체크
      newChecked = [...new Set([...checkedValues, selectedOption])];
    }

    setCheckedValues(newChecked);
  };

  const classes = joinClassName('c_checkbox_list', className);

  return (
    <>
      <ul className={classes}>
        {options.map((option, idx) => {
          const isChecked = checkedValues.some(item => item.value === option.value);

          return (
            <li key={option.label} className="c_checkbox_list_item">
              <Checkbox
                label={option.label}
                value={option.value}
                checked={isChecked}
                hasLabel={hasLabel}
                onChange={() => handleCheckboxChange(idx)}
              />
            </li>
          );
        })}
      </ul>
      <ErrorMsg text={error} />
    </>
  );
};

export default CheckboxList;
