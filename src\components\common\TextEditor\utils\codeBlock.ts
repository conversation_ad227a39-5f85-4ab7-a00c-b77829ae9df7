import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import joinClassName from '@utils/joinClassName';

export const CustomCodeBlock = CodeBlockLowlight.extend({
  addOptions() {
    return {
      ...this.parent?.(),
      languageClassPrefix: 'language-',
      HTMLAttributes: {
        class: 'code-block-wrapper',
      },
    };
  },

  renderHTML({ HTMLAttributes, node }) {
    return [
      'div',
      { ...HTMLAttributes, class: 'code-block-wrapper relative' },
      ['pre', {}, ['code', { class: `language-${node.attrs.language}` }, 0]],
    ];
  },

  addCommands() {
    return {
      ...this.parent?.(),
      setDefaultLanguage:
        (language: string) =>
        ({ commands }) => {
          this.options.defaultLanguage = language;
          return true;
        },
    };
  },
});
