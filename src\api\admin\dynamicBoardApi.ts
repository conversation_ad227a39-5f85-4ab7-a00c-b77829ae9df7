import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

const API_URL = '/api-admin/dynamic-bbs';

// 일반형 게시판 목록 조회
interface GetGneralBoardListParams {
  bbsId?: number;
  boardTempletId?: number;
  page: number;
  size: number;
  title: string;
  isGetErrorMsg?: boolean;
}

export const getGeneralBoardList = async ({
  boardTempletId,
  page = 1,
  size = 10,
  title = '',
  isGetErrorMsg = false,
}: GetGneralBoardListParams) => {
  try {
    const apiUrl = `${API_URL}/${boardTempletId}/?page=${page}&size=${size}&title=${title}`;
    const response = await api.get(apiUrl);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '동적 게시판 일반형 목록 조회');
  }
};

// 일반형 게시판 상세 조회
interface GetGeneralBoardDetailParams {
  postId: number | string;
  bbsId: number;
  isGetErrorMsg?: boolean;
}

export const getGeneralBoardDetail = async ({ bbsId, postId, isGetErrorMsg = false }: GetGeneralBoardDetailParams) => {
  try {
    const apiUrl = `${API_URL}/${bbsId}/${postId}`;
    const response = await api.get(apiUrl);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '동적 게시판 상세 조회');
  }
};

// 일반형 게시판 등록
interface createGeneralBoardPostParams {
  bbsId: string;
  title: string;
  contents: string;
  notiYn: 'Y' | 'N';
  files?: FileList;
  isGetErrorMsg?: boolean;
}

export const createGeneralBoardPost = async ({
  bbsId,
  title,
  contents,
  notiYn,
  files,
  isGetErrorMsg = false,
}: createGeneralBoardPostParams) => {
  try {
    const formData = new FormData();
    formData.append('templetId', bbsId);
    formData.append('title', title);
    formData.append('content', contents);
    formData.append('notiYn', notiYn);

    if (files) {
      Array.from(files).forEach((file) => {
        formData.append('files', file);
      });
    }

    const apiUrl = `${API_URL}/${bbsId}/add`;
    const response = await api.post(apiUrl, formData);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '동적 게시판 일반형 등록');
  }
};

//  일반형 게시판 수정
interface updateGeneralBoardPostParams {
  bbsId: string | number;
  postId: string | number;
  title: string;
  content: string;
  notiYn: 'Y' | 'N';
  saveFiles?: File[];
  delFileIds?: string[];
  isGetErrorMsg?: boolean;
}

export const updateGeneralBoardPost = async ({
  bbsId,
  postId,
  title,
  contents,
  notiYn,
  saveFiles,
  delFileIds,
  isGetErrorMsg = false,
}: any) => {
  try {
    const formData = new FormData();
    formData.append('title', title);
    formData.append('content', contents);
    formData.append('templetId', bbsId);
    formData.append('notiYn', notiYn);
    saveFiles.forEach((file) => formData.append('saveFiles', file));
    delFileIds.forEach((id) => formData.append('delFileIds', id));
    const response = await api.post(`${API_URL}/${bbsId}/${postId}/edit`, formData);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '동적 게시판 일반형 수정');
  }
};

// 일반형 게시판 삭제

export const deleteGeneralBoardPost = async ({ bbsId, postId, isGetErrorMsg = false }: GetGeneralBoardDetailParams) => {
  try {
    const apiUrl = `${API_URL}/${bbsId}/${postId}/delete`;
    const response = await api.get(apiUrl);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '동적 게시판 일반형 삭제');
  }
};
