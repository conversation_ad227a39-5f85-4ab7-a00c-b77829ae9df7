import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

/**
 * 그룹 코드 목록을 조회하는 API 함수 (검색 조건 사용)
 */

interface GetGroupCodeListProps {
  code?: string | number;
  isGetErrorMsg?: boolean;
}

const getGroupCodeList = async ({ code, isGetErrorMsg = false }: GetGroupCodeListProps) => {
  try {
    let url = '/api-admin/group-codes';

    if (code) {
      url += `?code=${code}`;
    }

    const response = await api.get(url);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 목록 조회');
  }
};

/**
 * 그룹 코드 상세 정보를 조회하는 API 함수 (검색 조건 미사용)
 * @param code 조회할 그룹 코드
 */
interface GetGroupCodeDetailProps {
  code: string;
  isGetErrorMsg?: boolean;
}

const getGroupCodeDetail = async ({ code, isGetErrorMsg = false }: GetGroupCodeDetailProps) => {
  try {
    const response = await api.get(`/api-admin/group-codes/${code}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 상세 조회');
  }
};

/**
 * 코드 목록을 조회하는 API 함수 (검색 조건 미사용)
 * @param code 조회할 그룹 코드
 */
interface GetCodeListProps {
  code: string;
  isGetErrorMsg?: boolean;
}

const getCodeList = async ({ code, isGetErrorMsg = false }: GetCodeListProps) => {
  try {
    const response = await api.get(`/api-admin/codes/group/${code}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '코드 목록 조회');
  }
};

/**
 * 그룹 코드 삭제 API
 * @param code 삭제할 그룹 코드
 */

interface DeleteGroupCodeParams {
  code: string;
  isGetErrorMsg?: boolean;
}

const deleteGroupCode = async ({ code, isGetErrorMsg = false }: DeleteGroupCodeParams) => {
  try {
    const response = await api.post(`/api-admin/group-codes/${code}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 삭제');
  }
};

/**
 * 그룹 코드 추가 API
 * @param formData 추가할 그룹 코드 데이터
 */

interface AddGroupCodeProps {
  formData: any;
  isGetErrorMsg?: boolean;
}

const addGroupCode = async ({ formData, isGetErrorMsg = false }: AddGroupCodeProps) => {
  try {
    const response = await api.post('/api-admin/group-codes', formData);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 추가');
  }
};

/**
 * 그룹 코드 수정 API
 * @param code 수정할 그룹 코드
 * @param formData 수정할 데이터
 */

interface EditGroupCodeProps {
  code: string;
  formData: any;
  isGetErrorMsg?: boolean;
}

const editGroupCode = async ({ code, formData, isGetErrorMsg = false }: EditGroupCodeProps) => {
  try {
    const response = await api.post(`/api-admin/group-codes/${code}`, formData);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '그룹 코드 수정');
  }
};

/**
 * 코드 추가 API
 * @param data 추가할 코드 데이터
 */

interface PostCodeProps {
  data: any;
  isGetErrorMsg?: boolean;
}

const postCode = async ({ data, isGetErrorMsg = false }: PostCodeProps) => {
  try {
    const response = await api.post('/api-admin/codes/change', data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '코드 추가 및 수정');
  }
};

/**
 * 코드 삭제 API
 * @param code 삭제할 코드
 */

interface DeleteCodeProps {
  data: {
    groupCode: string;
    codes: Array<string | number>;
  };
  isGetErrorMsg?: boolean;
}

const deleteCode = async ({ data, isGetErrorMsg = false }: DeleteCodeProps) => {
  try {
    const response = await api.post('/api-admin/codes/delete', { ...data });
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '코드 삭제');
  }
};

export {
  getGroupCodeList,
  getGroupCodeDetail,
  getCodeList,
  deleteGroupCode,
  addGroupCode,
  editGroupCode,
  postCode,
  deleteCode,
};
