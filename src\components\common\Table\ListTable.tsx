import React from 'react';

interface Props {
  title: string | React.ReactNode;
  list: React.ReactNode[];
}

const ListTable = ({ title, list }: Props) => {
  return (
    <div className="c_list_table_wrapper">
      <p className="c_list_table_title">{title}</p>
      <div className="c_list_table_contents">
        {Array.isArray(list) && list.length !== 0 ? list : <div className="no_result">조회된 데이터가 없습니다</div>}
      </div>
    </div>
  );
};

export default ListTable;
