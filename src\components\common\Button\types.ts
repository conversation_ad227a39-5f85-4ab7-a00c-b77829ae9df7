import { ButtonHTMLAttributes } from 'react';

// 디자인 (: default)
type Design = 'default' | 'capsule' | 'rect' | 'circle' | 'handle' | 'tab' | 'switch';

// 색상이 채워지는 형태 (: filled)
type Fill = 'filled' | 'unfilled' | 'outlined';

// Size => Design 이 Circle 일 경우만 사용
type Size = 'largest' | 'large' | 'medium' | 'small' | 'smallest';

// 색상 (: primary)
type Color = 'primary' | 'grayscale' | 'error' | 'warning' | 'red';

// 위치 (: left)
type Position = 'top' | 'bottom' | 'left' | 'right';

// 아이콘 (: none)
export const iconTypes = [
  'arrow_up',
  'arrow_down',
  'arrow_left',
  'arrow_right',
  'double_arrow_left',
  'double_arrow_right',
  'close',
  'docs',
  'swagger',
  'circle_close',
  'bold',
  'italic',
  'underline',
  'strike',
  'format_clear',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'text_color',
  'highlight',
  'image',
  'video',
  'table',
  'link',
  'list_num',
  'list_bullet',
  'list_check',
  'align_init',
  'align_justify',
  'align_left',
  'align_center',
  'align_right',
  'blockquote',
  'codeblock',
  'table_merge',
  'play',
  'pause',
  'delete',
] as const;

export type IconType = (typeof iconTypes)[number];

/**--------------------------------------------------------------------------------- */

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  text: string;
  type?: 'button' | 'submit' | 'reset';
  design?: Design;
  size?: Size;
  fill?: Fill;
  color?: Color;
  className?: string;
  isActive?: boolean;
  iconPosition?: Position;
  clickLog?: {
    buttonSection: string;
  };
}

export interface IconButtonProps extends ButtonProps {
  icon: IconType;
  iconOnly?: boolean;
  isActive?: boolean;
  children?: React.ReactNode;
}
