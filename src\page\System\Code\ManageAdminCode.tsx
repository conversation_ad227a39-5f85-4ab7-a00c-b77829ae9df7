import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { FormValues } from './Components/types';
import { useCodeStore } from './store/useCodeStore';
import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import GroupCodeList from './Components/GroupCodeList';
import GroupCodeDetail from './Components/GroupCodeDetail';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import CodeList from './Components/CodeList';
import { getGroupCodeDetail, getGroupCodeList } from '@api/admin/systemCodeAPI';

const ManageAdminCode = () => {
  // 로딩 상태 관리
  const {
    groupCode, // 현재 선택된 그룹 코드 정보
    setGroupCodeDetailData, // 그룹 코드 상세 정보를 설정하는 함수
    setGroupCodeListData, // 그룹 코드 목록 데이터를 설정하는 함수
  } = useCodeStore();

  // 검색 폼 관리
  const searchMethods = useForm<FormValues>({
    defaultValues: {
      code: '', // 그룹 코드 검색어 초기값
    },
  });

  // 그룹 코드 목록 조회
  const getGroupCodeListData = async (formData?: FormValues) => {
    const responseData = await getGroupCodeList({ code: formData?.code });
    setGroupCodeListData(responseData);
  };

  // 그룹 코드 상세 조회
  const getGroupCodeDetailData = async () => {
    const responseData = await getGroupCodeDetail({ code: groupCode.code });
    setGroupCodeDetailData(responseData);
  };

  // 컴포넌트 마운트 시 그룹 코드 목록 조회
  useEffect(() => {
    getGroupCodeListData();
  }, []);

  // 그룹 코드 선택 시 상세 정보와 코드 목록 조회
  useEffect(() => {
    if (groupCode.state === 'add') {
      setGroupCodeDetailData({});
    } else if (groupCode.state === 'edit') {
      getGroupCodeDetailData();
    }
  }, [groupCode]);

  return (
    <div className="manage_admin_code">

      {/* 검색 폼 */}
      <Form onSubmit={getGroupCodeListData} methods={searchMethods}>
        <ControlBox>
          <FormInput name="code" label="그룹 코드" placeholder="그룹 코드를 입력하세요" />
          <Button type="submit" text="조회" onClick={() => {}} clickLog={{ buttonSection: '검색창' }}/>
        </ControlBox>
      </Form>

      <div className="content horizontal">
        <div className="left_content vertical">
          <GroupCodeList searchMethods={searchMethods} />
        </div>
        <div className="right_content vertical">
          <GroupCodeDetail searchMethods={searchMethods} />
          {groupCode.code && <CodeList />}
        </div>
      </div>
    </div>
  );
};

export default ManageAdminCode;
