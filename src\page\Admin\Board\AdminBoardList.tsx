import { useCallback, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { ColDef, ColGroupDef } from '@ag-grid-community/core';

import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import FormInput from '@components/common/Form/FormInput';
import SelectBox from '@components/common/SelectBox/SelectBox';

import { AdminBoardFormValue, AdminBoardSearchOptions } from '@page/Admin/Board/type';
import { AgGridReact } from '@ag-grid-community/react';
import { useNavigate } from 'react-router-dom';
import { deleteBoard, getBoardList } from '@api/admin/boardApi';
import { calcNoSort } from '@utils/calcNoSort';
import { getCodeList } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';

const BOARD_TYPE_GROUP = 'TEMPLET_TYPE';

const AdminBoardList = () => {
  // 셀렉트 옵션
  const pageSize: AdminBoardSearchOptions['pageSize'] = [
    { label: '10', value: 10 },
    { label: '20', value: 20 },
    { label: '30', value: 30 },
    { label: '40', value: 40 },
    { label: '50', value: 50 },
  ];

  const [boardTypes, setBoardTypes] = useState<AdminBoardSearchOptions['boardTypes']>([
    { label: '전체', value: '전체' },
  ]);

  const boardUseYn: AdminBoardSearchOptions['boardUseYn'] = [
    { label: '전체', value: '전체' },
    { label: '사용', value: 'Y' },
    { label: '미사용', value: 'N' },
  ];

  // ----------------------------------------------------------------------------------------------------

  const navigate = useNavigate();
  const [selectedPageSize, setSelectedPageSize] = useState<{ label: string; value: number }>(pageSize[0]);
  const [page, setPage] = useState<number>(0);
  const [totalDataCnt, setTotalDataCnt] = useState<number>(0);
  const gridRef = useRef<AgGridReact>(null);
  const [column] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: 'seq', headerName: 'No.', width: 130 },
    { field: 'templetNm', headerName: '게시판명' },
    { field: 'templetTypeNm', headerName: '게시판 유형' },
    { field: 'useYn', headerName: '사용유무' },
    { field: 'dateInfo.createUser', headerName: '등록자' },
    { field: 'dateInfo.createDate', headerName: '등록시간' },
    { field: 'dateInfo.updateUser', headerName: '수정자' },
    { field: 'dateInfo.updateDate', headerName: '수정시간' },
  ]);
  const [gridData, setGridData] = useState([]);
  const [selectedGridData, setSelectedGridData] = useState([]);
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();

  const formMethods = useForm<AdminBoardFormValue>({
    defaultValues: {
      boardType: boardTypes[0],
      boardUseYn: boardUseYn[0],
      boardName: '',
    },
  });
  3;

  const { getValues } = formMethods;

  const getBoardTypes = async () => {
    const boardTypeRes = await getCodeList({ code: BOARD_TYPE_GROUP });
    setBoardTypes([
      { label: '전체', value: '전체' },
      ...boardTypeRes.map((boardType) => {
        return { label: boardType.name, value: boardType.code };
      }),
    ]);
  };

  const getBoardListData = async ({ templetTypeCd, templetName, useYn, page, size }) => {
    const response = await getBoardList({ templetTypeCd, templetName, useYn, page, size });

    setTotalDataCnt(response.pageInfo.totalCount);

    setGridData(
      response.data.map((data, index) => {
        return {
          seq: calcNoSort({
            totalCount: response.pageInfo.totalCount,
            currentPage: page,
            pageSize: selectedPageSize.value,
            index,
          }),
          ...data,
        };
      })
    );
  };

  // 조회
  useEffect(() => {
    getBoardTypes();
  }, []);

  useEffect(() => {
    setPage(0); // page size 변화 시 1페이지로 이동
    getBoardListData({
      templetTypeCd: getValues('boardType').value,
      templetName: getValues('boardName'),
      useYn: getValues('boardUseYn').value,
      page: 0,
      size: selectedPageSize.value,
    });
  }, [selectedPageSize]);

  // 검색
  const handleSearchSubmit = (data: AdminBoardFormValue) => {
    const { boardType, boardUseYn, boardName } = data;

    getBoardListData({
      templetTypeCd: boardType.value,
      templetName: boardName,
      useYn: boardUseYn.value,
      page: 0,
      size: selectedPageSize.value,
    });
  };

  // 페이지 변경
  const handleChangePage = (e) => {
    getBoardListData({
      templetTypeCd: getValues('boardType').value,
      templetName: getValues('boardName'),
      useYn: getValues('boardUseYn').value,
      page: e - 1,
      size: selectedPageSize.value,
    });

    setPage(e - 1);
  };

  // row 선택
  const handleRowSelected = useCallback(() => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data) || [];

    setSelectedGridData(selectedData);
  }, []);

  // 상세 페이지 이동
  const handleBoardDetail = (event) => {
    const boardId = event.data.id;

    navigate(`/admin/board/detail/${boardId}`);
  };

  // 삭제
  const handleDeleteBoard = async () => {
    initConfirmState();

    const isUsed = selectedGridData.some((selected) => selected.useYn === 'Y');

    if (isUsed) {
      setAlertState({
        isOpen: true,
        content: '사용중인 게시판은 삭제할 수 없습니다',
        onConfirm: () => {
          initAlertState();
        },
      });
    } else {
      const ids = selectedGridData.map((selected) => selected.id.toString());

      const response = await deleteBoard({ ids });

      if (response === 'DELETE') {
        setAlertState({
          isOpen: true,
          content: '게시판이 삭제되었습니다',
          onConfirm: () => {
            setPage(0);
            getBoardListData({
              templetTypeCd: getValues('boardType').value,
              templetName: getValues('boardName'),
              useYn: getValues('boardUseYn').value,
              page: 0,
              size: selectedPageSize.value,
            });
            initAlertState();
          },
        });
      }
    }
  };

  const handleClickDeleteBtn = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: handleDeleteBoard,
    });
  };

  return (
    <div className="admin_board">
      <div className="manage_admin_board">
        <Form onSubmit={handleSearchSubmit} methods={formMethods}>
          <ControlBox>
            <FormSelectBox name="boardType" label="게시판 유형" options={boardTypes} />
            <FormSelectBox name="boardUseYn" label="사용유무" options={boardUseYn} />
            <FormInput name="boardName" label="게시판명" />
            <Button type="submit" text="조회" />
          </ControlBox>
        </Form>
        <div className="admin_board_contents">
          <div className="admin_board_contents_control">
            <div className="admin_board_contents_control_cnt">
              <p>총 {totalDataCnt}건</p>
              <SelectBox
                options={pageSize}
                defaultValue={selectedPageSize}
                selectedValue={selectedPageSize}
                setSelectedValue={setSelectedPageSize}
              />
            </div>
            <div className="admin_board_contents_control_btn">
              <Button text="삭제" color="red" disabled={selectedGridData.length === 0} onClick={handleClickDeleteBtn} />
              <Button text="추가" onClick={() => navigate('/admin/board/add')} />
            </div>
          </div>
          <Grid
            ref={gridRef}
            columns={column}
            rowData={gridData}
            autoSizeStrategy={'onGridSizeChanged'}
            defaultColDef={{
              onCellClicked: handleBoardDetail,
            }}
            gridOptions={{
              onRowSelected: handleRowSelected,
              rowSelection: {
                checkboxes: true,
                headerCheckbox: true,
                mode: 'multiRow',
              },
            }}
          />
          <Pagination
            pageCount={10}
            totalCount={totalDataCnt}
            currentPage={page + 1}
            itemCountPerPage={selectedPageSize.value}
            onPageChange={(e) => {
              handleChangePage(e);
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default AdminBoardList;
