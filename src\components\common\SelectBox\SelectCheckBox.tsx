import Checkbox from '@components/common/Input/Checkbox';
import joinClassName from '@utils/joinClassName';
import { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

type OptionType = { label: string; value: string | number };

interface SelectCheckBoxProps {
  placeholder?: string; // 기본 문구
  options: OptionType[]; // 옵션
  selectedValue: OptionType[] | []; // 현재 선택된 값 배열(부모 컴포넌트와 연결된 값)
  setSelectedValue: React.Dispatch<React.SetStateAction<OptionType[] | []>>; // 외부로 value 전달
  defaultValues?: OptionType[] | OptionType; // default value
}

/**
 *
 * TODO)
 * 1. disabled 옵션 추가
 * 2. 키보드 조작 시 추가
 */

/** 체크박스 리스트로 이루어진 selectBox(option 다중선택 가능) */
const SelectCheckBox = ({
  placeholder = '선택이 필요합니다', // placeholder
  options, // 옵션 리스트(*) {label, value}[]
  selectedValue, // 선택된 값이 담겨진 state (*)
  setSelectedValue, // 선택된 값을 업데이트하는 state 함수 (*)
  defaultValues, // 기본 선택값
}: SelectCheckBoxProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const isOpenRef = useRef(null);
  const [optionCheckList, setOptionCheckList] = useState(Array(options.length).fill(false));
  const [selectedLabels, setSelectedLabels] = useState<string>(placeholder);
  const [selectedOptions, setSelectedOptions] = useState<OptionType[]>([]);
  const selectBoxRef = useRef(null);
  const [optionPosition, setOptionPosition] = useState({ top: 0, left: 0, width: 0 });

  useEffect(() => {
    /** 선택값이 외부에서 초기화되었을 경우 */
    if (selectedValue.length === 0 && selectedOptions.length > 0) {
      setOptionCheckList(Array(options.length).fill(false));
    }
  }, [selectedValue]);

  /**기본으로 선택된 값이 있는 경우 */
  useEffect(() => {
    const valueArr = options.map((option) => option.value);

    // defaultValue가 들어왔는지 확인
    if (typeof defaultValues !== 'undefined') {
      if (Array.isArray(defaultValues)) {
        // 기본으로 선택된 값이 여러 개인 경우(배열로 들어온 경우)
        const selectedValueArr = defaultValues.map((option) => option.value);
        const selectedValueChecked = valueArr.map((option) => (selectedValueArr.includes(option) ? true : false));
        setOptionCheckList(selectedValueChecked);
      } else {
        // 기본으로 선택된 값이 1개인 경우(object로 들어온 경우)
        const selectedValueChecked = valueArr.map((option) => (option === defaultValues.value ? true : false));
        setOptionCheckList(selectedValueChecked);
      }
    }
  }, []);

  const handleClickSelectBox = () => {
    setIsOpen((prev) => !prev);
  };

  const handleCheckboxChange = (idx: number) => {
    setOptionCheckList((prev) =>
      prev.map((checked, optionIdx) => {
        return optionIdx === idx ? !checked : checked;
      })
    );
  };

  useEffect(() => {
    const hasValues = optionCheckList.some((option) => option); // 선택된 값이 하나라도 있는지 판별

    if (hasValues) {
      const checkedValue = optionCheckList.reduce((checkedValue, checked, idx) => {
        if (checked === true) checkedValue.push(options[idx]);
        return checkedValue;
      }, []);

      const checkedLabels = checkedValue.map((value) => value.label).join(', ');

      setSelectedLabels(checkedLabels);
      setSelectedValue(checkedValue);
      setSelectedOptions(checkedValue);
    } else {
      // 선택된 값 없으면 selectedValue 초기화
      setSelectedLabels(placeholder);
      setSelectedValue([]);
      setSelectedOptions([]);
    }
  }, [optionCheckList]);

  /** selectBox 외부 클릭 시 selectBox 닫기 */
  const handleClickOutside = (event: MouseEvent) => {
    if (
      selectBoxRef.current &&
      !selectBoxRef.current.contains(event.target as Node) &&
      !document.getElementById('select-options').contains(event.target as Node) &&
      isOpenRef.current
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    isOpenRef.current = isOpen; // portal로 열린 option dom 있는지 없는지 확인
  }, [isOpen]);

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleGetOptionPosition = (entry = null) => {
    if (!selectBoxRef.current) return;

    const rect = selectBoxRef.current.getBoundingClientRect(); // viewport 기준 위치
    const scrollX = window.scrollX; // 문서의 X 스크롤 값
    const scrollY = window.scrollY; // 문서의 Y 스크롤 값

    const width = entry ? entry.contentRect.width : rect.width; // 초기에는 getBoundingClientRect로 width 확인
    const height = entry ? entry.contentRect.height : rect.height;

    // 위치 및 크기 계산
    setOptionPosition({
      width: width,
      left: rect.left + scrollX,
      top: rect.top + scrollY + height + 16,
    });
  };

  useEffect(() => {
    // ResizeObserver - size 및 position 변화 감지
    const resizeObserver = new ResizeObserver((entries) => {
      for (let entry of entries) {
        handleGetOptionPosition(entry); // observer에서 함수 호출
      }
    });

    if (selectBoxRef.current) {
      resizeObserver.observe(selectBoxRef.current);
    }

    // 클린업
    return () => {
      resizeObserver.disconnect();
    };
  }, [isOpen]);

  let optionStyle: React.CSSProperties = {
    top: `${optionPosition.top}px`,
    left: `${optionPosition.left}px`,
    width: `${optionPosition.width}px`,
  };

  return (
    <div className={joinClassName('c_select', isOpen && 'open')} ref={selectBoxRef}>
      {/* 라벨 영역 */}
      <div className="c_selected_label" onClick={handleClickSelectBox}>
        <span>{selectedLabels}</span>
      </div>
      {/* 옵션 영역 */}
      {createPortal(
        <>
          {isOpen && (
            <div className="c_select_lists" style={optionStyle} ref={isOpenRef}>
              <ul className="c_select_lists_inner">
                {options.map((option, idx) => (
                  <li className="c_select_option_item" key={option.value}>
                    <Checkbox
                      value={option.value}
                      checked={optionCheckList[idx]}
                      onChange={() => handleCheckboxChange(idx)}
                      hasLabel
                      label={option.label}
                    />
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>,
        document.getElementById('select-options')
      )}
    </div>
  );
};

export default SelectCheckBox;
