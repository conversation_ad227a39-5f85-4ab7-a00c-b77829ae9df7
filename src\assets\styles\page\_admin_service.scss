@use '@styles/utils/mixin' as m;

.manage_admin_service {
  .c_table_container_header {
    margin-bottom: 0;
  }

  .c_control_box {
    width: 100%;
  }

  .c_table_wrapper {
    overflow-x: auto;

    td {
      .c_select,
      .c_input {
        width: 200px;
      }

      > span {
        max-width: 160px;
      }
    }
  }

  .left_content {
    .c_list_table_contents {
      > div {
        @include m.flex(center, space-between);
      }
    }
  }

  .right_content {
    .c_table_container_header {
      .left_cont {
        .c_sub_title {
          display: inline-block;
        }
        .total_cnt {
          font-size: 0.875rem;
          margin-left: 1rem;
        }
      }
    }
  }

  .service_list {
    width: 100%;
    height: calc(100vh - 320px);

    .ag-theme-quartz {
      .ag-header-cell-label {
        justify-content: center;

        .ag-checkbox {
          margin: 0;
        }
      }

      .c_button {
        line-height: 1;
      }

      .custom_filter {
        padding: 0.625rem;
        .custom_filter_checkbox {
          @include m.flex(start, center, column);
          gap: 0.5rem;
        }
      }
    }

    @include m.bp_large() {
      height: calc(100vh - 290px);
    }

    @include m.bp_medium() {
      height: calc(100vh - 260px);
    }
  }

  .service_group_detail_wrapper {
    position: relative;
    width: 100%;
    margin-bottom: 0.5rem;

    .no_result {
      @include m.position_center(absolute);
      @include m.flex(center, center);
      width: 100%;
      height: 100%;
      border-radius: 0.5rem;
      font-weight: 600;
      background-color: var(--g_02);
    }
  }

  .modify_btn {
    display: block;
    margin-left: auto;
  }
}

// Modal
.service_confirm_modal,
.service_group_confirm_modal {
  width: 700px;

  .c_modal_body {
    padding: 0 0.5rem;

    .c_select_wrapper {
      width: 100%;
    }

    .th_service_group_manage {
      @include m.flex(center, center);
    }

    .group_desc,
    .service_desc {
      height: 150px;
    }

    .button_wrapper {
      margin-top: 1rem;
      @include m.flex(center, flex-end);
      gap: 0.25rem;
    }
  }
}
