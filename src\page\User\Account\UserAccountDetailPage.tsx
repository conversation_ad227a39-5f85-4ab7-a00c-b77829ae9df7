import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormCheckbox from '@components/common/Form/FormCheckbox';
import FormCheckboxList from '@components/common/Form/FormCheckboxList';
import FormInput from '@components/common/Form/FormInput';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import Title from '@components/common/Title/Title';
import { useAlert } from '@hooks/useAlert';
import { useConfirm } from '@hooks/useConfirm';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation } from 'react-router-dom';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useUserAccountDetailAPI } from './hooks/useUserAccountDetailAPI';
import { useUserAccountDetailStore } from './store/useUserAccountDetailStore';
import { BodyRowProps, DetailFormValues, pageType } from './type';

const BodyRow = ({ title = '', required, children }: BodyRowProps) => {
  return (
    <tr>
      <th>
        <span className={required ? 'required' : ''}>{title}</span>
      </th>
      <td>{children}</td>
    </tr>
  );
};

const UserAccountDetailPage = () => {
  // hooks
  const location = useLocation();
  const { roleListData, checkDuplicateId, setCheckDuplicateId, detailFormValues } = useUserAccountDetailStore();
  const { alertState } = useAlertStore();
  const { confirmState } = useConfirmStore();
  const { openAlert } = useAlert();
  const { openConfirm } = useConfirm();

  // form
  const methods = useForm<DetailFormValues>({
    defaultValues: detailFormValues,
  });

  const { reset } = methods;
  const { getRoleList, getCheckDuplicateId, addAccount, editAccount, getAccountDetail, goList } =
    useUserAccountDetailAPI({ methods });

  // add or edit > add: 등록페이지, edit: 수정페이지 >
  // '/admin/account/add' or '/admin/account/edit/:id'
  const pageType: pageType = location.pathname.includes('add') ? 'add' : 'edit';

  useEffect(() => {
    getRoleList();
    // 수정일 때는 데이터를 가져오고, 추가일 때는 초기화
    pageType === 'edit' ? getAccountDetail() : initValue();
  }, []);

  // Function
  const handleOnSubmit = (data: DetailFormValues) => {
    if (pageType === 'add') {
      if (!checkDuplicateId) {
        openAlert('ID 중복 확인을 해주세요.');
        return;
      }
      openConfirm('등록하시겠습니까?', async () => {
        addAccount(data);
      });
    } else {
      openConfirm('수정하시겠습니까?', async () => {
        editAccount(data);
      });
    }
  };

  const initForm = () => {
    openConfirm(
      <>
        기존에 작성하셨던 내용이 초기화됩니다. <br />
        초기화하시겠습니까?
      </>,
      () => {
        pageType === 'add' ? initValue() : getAccountDetail();
      }
    );
  };

  const initValue = () => {
    // input 필드값 초기화
    reset(detailFormValues);
    setCheckDuplicateId(false);
  };

  return (
    <>
      <Title />
      <Form methods={methods} onSubmit={handleOnSubmit}>
        <TableContainer className="admin_detail_account">
          <colgroup id="admin_account_colgroup">
            <col />
            <col />
          </colgroup>
          <TableBody>
            <BodyRow title="사용자 ID" required>
              <FormInput
                name="userId"
                placeholder="ID를 입력해주세요"
                readOnly={pageType === 'edit'}
                onInput={() => {
                  setCheckDuplicateId(false);
                }}
                rules={{
                  required: 'ID는 필수값입니다.',
                }}
              />
              {pageType === 'add' && (
                <Button className="check_duplicated" text="중복확인" onClick={getCheckDuplicateId} />
              )}
            </BodyRow>
            <BodyRow title="이름" required>
              <FormInput
                name="name"
                placeholder="이름을 입력해주세요"
                rules={{
                  required: '이름은 필수값입니다.',
                }}
              />
            </BodyRow>
            <BodyRow title="이메일" required>
              <FormInput
                name="email"
                placeholder="이메일을 입력해주세요"
                rules={{
                  required: '이메일은 필수값입니다.',
                }}
              />
            </BodyRow>
            <BodyRow title="비밀번호">
              <FormInput
                name="password"
                type="password"
                placeholder="비밀번호를 입력해주세요"
                rules={{
                  minLength: {
                    value: 8,
                    message: '최소 8자 이상 입력해주세요.',
                  },
                }}
              />
            </BodyRow>
            <BodyRow title="계정 활성화" required={false}>
              <FormCheckbox name="activeYn" label="활성" />
            </BodyRow>
            <BodyRow title="권한" required={false}>
              {roleListData && (
                <FormCheckboxList
                  name="roleIds"
                  rules={{
                    required: '권한을 선택해주세요',
                  }}
                  options={roleListData}
                />
              )}
            </BodyRow>
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <div className="left">
            <Button text="목록" onClick={() => goList()} />
          </div>
          <div className="right">
            <Button text="초기화" color="grayscale" onClick={initForm} />
            <Button type="submit" text={pageType === 'add' ? '등록' : '수정'} />
          </div>
        </div>
      </Form>
      <Alert isOpenAlert={alertState.isOpen} children={alertState.content} onConfirm={alertState.onConfirm} />
      <Confirm
        isOpenConfirm={confirmState.isOpen}
        title="알림"
        children={confirmState.content}
        onLeftButton={confirmState.onCancel}
        onRightButton={confirmState.onConfirm}
        rightButtonText={confirmState.confirmType === 'delete' ? '삭제' : '확인'}
        isDelete={confirmState.confirmType === 'delete'}
      />
    </>
  );
};

export default UserAccountDetailPage;
