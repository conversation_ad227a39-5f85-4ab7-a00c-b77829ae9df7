interface TableCellProps {
  title?: string | React.ReactNode;
  required?: boolean;
  contents?: React.ReactNode;
  isFullWidth?: boolean;
  isHalf?: boolean;
}

type MaxTwoCells<T> = [T] | [T, T]; // table Cell 2개까지만 허용

interface Props {
  rowData: TableCellProps | MaxTwoCells<TableCellProps>;
}

const TableCell = ({ title = '', required = false, isFullWidth = false, contents, isHalf = false }: TableCellProps) => {
  return (
    <>
      <th>{typeof title === 'string' ? <p className={required ? 'required' : ''}>{title}</p> : title}</th>
      <td colSpan={isFullWidth ? 3 : 1}>
        <div className={utils.joinClassName('row_content', isHalf && 'row_half')}>{contents}</div>
      </td>
    </>
  );
};

const TableBodyRow = ({ rowData }: Props) => {
  const isArray = Array.isArray(rowData);

  return (
    <tr>
      {isArray ? (
        <>
          {rowData.map((data, idx) => (
            <TableCell
              key={`table-row-${idx}`}
              title={data.title}
              required={data.required}
              isFullWidth={data.isFullWidth}
              contents={data.contents}
              isHalf={rowData.length === 2}
            />
          ))}
        </>
      ) : (
        <TableCell
          title={rowData.title}
          required={rowData.required}
          isFullWidth={rowData.isFullWidth}
          contents={rowData.contents}
        />
      )}
    </tr>
  );
};

export default TableBodyRow;
