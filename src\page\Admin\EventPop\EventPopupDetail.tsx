import Button from '@components/common/Button/Button';
import CalendarRange from '@components/common/Calendar/CalendarRange';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import Filebox, { ResponseFile } from '../../../components/common/Filebox/Filebox';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import dayjs from 'dayjs';
import { uploadFileAPI } from '@api/admin/fileAPI';
import ErrorMsg from '@components/common/Error/ErrorMsg';
import { useAlertStore } from '@store/useAlertStore';
import { addEventPopup, deleteEventPopupData, editEventPopup, getEventPopupDetail } from '@api/admin/eventPopupAPI';
import { useConfirmStore } from '@store/useConfirmStore';
import EventPopupPreview from '@page/Admin/EventPop/components/EventPopupPreview';

const EventPopupDetail = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const { activeAlert } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const [pageType, setPageType] = useState<'detail' | 'edit' | 'add'>(null);
  const [imageFiles, setImageFiles] = useState<FileList | null>(null);
  const [responseOriginalImageList, setResponseOriginalImageList] = useState<ResponseFile[] | null>(null);
  const [responseImageList, setResponseImageList] = useState<ResponseFile[] | null>(null);
  const methods = useForm({
    defaultValues: {
      title: '',
      subTitle: '',
      period: { startDate: null, endDate: null },
      notOpenDay: { label: '하루', value: 1 },
      fileIds: [],
      linkUrl: '',
    },
  });

  const getPageType = () => {
    if (location.pathname.includes('edit')) setPageType('edit');
    if (location.pathname.includes('add')) setPageType('add');
    if (location.pathname.includes('detail')) setPageType('detail');
  };

  const getEventPopupDetailData = async () => {
    const detailData = await getEventPopupDetail({ id: params.id });

    if (detailData) {
      const notOpenDayLabel = detailData.notOpenDay === 7 ? '일주일' : detailData.notOpenDay === 1 ? '하루' : '';
      setResponseImageList(detailData.fileInfo);
      setResponseOriginalImageList(detailData.fileInfo);

      methods.reset({
        title: detailData.title,
        subTitle: detailData.subTitle,
        period: { startDate: new Date(detailData.exposureStart), endDate: new Date(detailData.exposureEnd) },
        notOpenDay: { label: notOpenDayLabel, value: detailData.notOpenDay },
        fileIds: detailData.fileIds,
        linkUrl: detailData.linkUrl || '',
      });
    }
  };

  const deleteEventPopup = async () => {
    const deleteMessage = await deleteEventPopupData({ id: params.id });
    if (deleteMessage) {
      initConfirmState();
      activeAlert(deleteMessage);
      goList();
    }
  };

  const goList = () => {
    navigate('/admin/event_popup');
  };

  const goDetail = () => {
    navigate(`/admin/event_popup/detail/${params.id}`);
  };

  const handleCalendarChange = (data) => {
    methods.setValue('period', data);
    if (data.startDate && data.endDate) {
      methods.clearErrors('period');
    }
  };

  const handleInit = () => {
    setImageFiles(null);

    if (pageType === 'add') {
      methods.reset({
        title: '',
        subTitle: '',
        period: { startDate: null, endDate: null },
        notOpenDay: { label: '하루', value: 1 },
        fileIds: [],
        linkUrl: '',
      });
    }

    if (pageType === 'edit') {
      getEventPopupDetailData();
    }
  };

  const uploadImageFile = async () => {
    if (imageFiles?.length > 0) {
      const formData = new FormData();

      // FormData에 파일 추가 - 각 파일을 개별적으로 추가
      for (let i = 0; i < imageFiles.length; i++) {
        formData.append('files', imageFiles[i]);
      }
      formData.append('type', 'NORMAL');

      const fileList = await uploadFileAPI({ formData });

      return fileList.map((respFile) => respFile.id);
    } else {
      return [];
    }
  };

  const checkResponseImageDeleted = () => {
    if (pageType === 'edit') {
      // 삭제된 이미지 확인
      const deletedImageList = responseOriginalImageList.filter(
        (originImg) => !responseImageList.some((image) => image.id === originImg.id)
      );

      if (deletedImageList.length > 0) {
        return deletedImageList.map((image) => image.id);
      }
      return [];
    }
  };

  const handleSubmit = async (data) => {
    // 이벤트 노출 기간 체크 Validation
    if (!data.period.startDate || !data.period.endDate) {
      methods.setError('period', {
        type: 'required',
        message: '이벤트 노출 기간을 설정해주세요.',
      });
      return;
    }

    // 파일 ID 처리
    let fileIds = responseOriginalImageList ? [...responseOriginalImageList.map((image) => image.id)] : [];

    // 삭제된 이미지 처리 (편집 모드)
    if (pageType === 'edit') {
      const deletedImageList = checkResponseImageDeleted();
      if (deletedImageList?.length > 0) {
        fileIds = fileIds.filter((id) => !deletedImageList.includes(id));
      }
    }

    // 새 이미지 업로드 및 ID 가져오기
    const uploadFileIds = await uploadImageFile();

    // 이미지 유효성 검사
    const totalImageCount = pageType === 'add' ? uploadFileIds.length : responseImageList.length + uploadFileIds.length;

    if (totalImageCount === 0) {
      activeAlert('이미지를 1개 이상 등록해야합니다.');
      return;
    }

    // 새 파일 ID 추가
    fileIds.push(...uploadFileIds);

    // 이벤트 팝업 데이터 구성
    const parsedData = {
      title: data.title,
      subTitle: data.subTitle,
      linkUrl: data.linkUrl,
      exposureStart: dayjs(data.period.startDate).format('YYYY-MM-DDTHH:mm:ss'),
      exposureEnd: dayjs(data.period.endDate).format('YYYY-MM-DDTHH:mm:ss'),
      fileIds: fileIds,
      notOpenDay: data.notOpenDay.value,
    };

    // API 호출 및 응답 처리
    let response;
    if (pageType === 'edit') {
      response = await editEventPopup({ id: params.id, data: parsedData });
      if (response) {
        activeAlert(response.message, () => {
          setImageFiles(null);
          navigate('/admin/event_popup/detail/' + params.id);
        });
      }
    } else if (pageType === 'add') {
      response = await addEventPopup({ data: parsedData });
      if (response) {
        activeAlert(response.message, () => {
          setImageFiles(null);
          navigate('/admin/event_popup/detail/' + response.data.id);
        });
      }
    }
  };

  const handleClickDelete = () => {
    setConfirmState({
      isOpen: true,
      confirmType: 'delete',
      title: '알림',
      content: `${methods.getValues('title')} 이벤트 팝업을 삭제하시겠습니까?`,
      onConfirm: () => deleteEventPopup(),
      onCancel: initConfirmState,
    });
  };

  useEffect(() => {
    getPageType();
  }, [location]);

  useEffect(() => {
    if (pageType === 'detail' || pageType === 'edit') {
      getEventPopupDetailData();
    }
  }, [pageType]);

  useEffect(() => {}, [methods.watch(['title', 'subTitle', 'notOpenDay', 'linkUrl'])]);

  return (
    <div className="admin_event_popup">
      <div className="admin_event_popup_detail">
        <Form methods={methods} onSubmit={handleSubmit}>
          <div className="content reverse horizontal">
            <div className="left_content">
              <TableContainer>
                <TableBody>
                  <TableBodyRow
                    rowData={{
                      title: '타이틀',
                      required: true,
                      contents: (
                        <>
                          {pageType === 'detail' ? (
                            methods.getValues('title')
                          ) : (
                            <FormInput
                              wrapperClassName="full"
                              name="title"
                              placeholder="이벤트 제목을 입력하세요."
                              rules={{ required: '이벤트 제목을 입력해주세요.' }}
                            />
                          )}
                        </>
                      ),
                    }}
                  />
                  <TableBodyRow
                    rowData={{
                      title: '서브 타이틀',
                      required: false,
                      contents: (
                        <>
                          {pageType === 'detail' ? (
                            methods.getValues('subTitle')
                          ) : (
                            <FormInput
                              wrapperClassName="full"
                              name="subTitle"
                              placeholder="이벤트 부제목을 입력하세요."
                            />
                          )}
                        </>
                      ),
                    }}
                  />
                  <TableBodyRow
                    rowData={{
                      title: '이벤트 노출 기간',
                      required: true,
                      contents: (
                        <>
                          {pageType === 'detail' && (
                            <>
                              {dayjs(methods.getValues('period').startDate).format('YYYY-MM-DD HH:mm:ss')} ~{' '}
                              {dayjs(methods.getValues('period').endDate).format('YYYY-MM-DD HH:mm:ss')}
                            </>
                          )}
                          {pageType === 'edit' &&
                            methods.getValues('period').startDate &&
                            methods.getValues('period').endDate && (
                              <CalendarRange
                                selectedDate={methods.getValues('period')}
                                onChange={handleCalendarChange}
                                showTime={true}
                              />
                            )}
                          {pageType === 'add' && (
                            <CalendarRange
                              selectedDate={methods.getValues('period')}
                              onChange={handleCalendarChange}
                              showTime={true}
                            />
                          )}
                          {methods.formState.errors.period && (
                            <ErrorMsg text={methods.formState.errors.period.message} />
                          )}
                        </>
                      ),
                    }}
                  />
                  <TableBodyRow
                    rowData={{
                      title: '다시 보지 않기 기간',
                      required: true,
                      contents: (
                        <>
                          {pageType === 'detail' ? (
                            methods.getValues('notOpenDay').label
                          ) : (
                            <FormSelectBox
                              name="notOpenDay"
                              options={[
                                { label: '하루', value: 1 },
                                { label: '일주일', value: 7 },
                              ]}
                              rules={{ required: '다시 보지 않기를 선택해주세요.' }}
                            />
                          )}
                          {methods.formState.errors.notOpenDay && (
                            <ErrorMsg text={methods.formState.errors.notOpenDay.message} />
                          )}
                        </>
                      ),
                    }}
                  />
                  <TableBodyRow
                    rowData={{
                      title: '이미지',
                      required: true,
                      contents: (
                        <Filebox
                          files={imageFiles}
                          setFiles={setImageFiles}
                          imageList={responseImageList}
                          setImageList={setResponseImageList}
                          multiple
                          type="image"
                          readOnly={pageType === 'detail'}
                        />
                      ),
                    }}
                  />
                  <TableBodyRow
                    rowData={{
                      title: '연결링크 ',
                      required: false,
                      contents: (
                        <>
                          {pageType === 'detail' ? (
                            methods.getValues('linkUrl')
                          ) : (
                            <FormInput wrapperClassName="full" name="linkUrl" placeholder="연결링크를 입력하세요." />
                          )}
                        </>
                      ),
                    }}
                  />
                </TableBody>
              </TableContainer>
              <div className="button_wrapper">
                <div className="left">
                  <Button
                    text={pageType === 'detail' ? '목록' : '취소'}
                    color="grayscale"
                    onClick={pageType === 'edit' ? goDetail : goList}
                  />
                </div>
                <div className="right">
                  {pageType !== 'add' && <Button text="삭제" color="red" onClick={handleClickDelete} />}
                  {pageType !== 'detail' && <Button text="초기화" color="grayscale" onClick={handleInit} />}
                  {pageType === 'detail' ? (
                    <Button text="수정" onClick={() => navigate(`/admin/event_popup/edit/${params.id}`)} />
                  ) : (
                    <Button type="submit" text="저장" />
                  )}
                </div>
              </div>
            </div>
            <div className="right_content">
              <EventPopupPreview
                title={methods.getValues('title')}
                subTitle={methods.getValues('subTitle')}
                linkUrl={methods.getValues('linkUrl')}
                notOpenDay={methods.getValues('notOpenDay')}
                responseImageList={responseImageList}
                imageFiles={imageFiles}
              />
            </div>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default EventPopupDetail;
