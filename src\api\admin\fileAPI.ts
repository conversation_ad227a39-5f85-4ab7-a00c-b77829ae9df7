import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';
import { buildQueryString } from '@utils/queryString';

// 파일 목록 조회
interface FileListResponse {
  formData: {
    page: number;
    size: number;
    tableName: string;
    fileName: string;
  };
  isGetErrorMsg?: boolean;
}

export const getFileList = async ({ formData, isGetErrorMsg = false }: FileListResponse) => {
  try {
    const queryString = buildQueryString(formData);

    const response = await api.get(`/api-admin/files${queryString}`);

    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '파일 목록 조회 중 오류 발생');
  }
};

// 파일 조회
export const getFile = async ({ id, isGetErrorMsg = false }: { id: string; isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.get(`/api-admin/files/${id}`, {
      responseType: 'arraybuffer',
    });

    const contentType = response.headers['content-type'];
    const blob = new Blob([response.data], { type: contentType });

    return blob;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '파일 조회 중 오류 발생');
  }
};

// 파일 업로드
interface UploadFileResponse {
  formData: FormData;
  isGetErrorMsg?: boolean;
}

export const uploadFileAPI = async ({ formData, isGetErrorMsg = false }: UploadFileResponse) => {
  try {
    const response = await api.post('/api-admin/files/upload', formData);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '파일 업로드 중 오류 발생');
  }
};

// 파일 삭제 단건
interface DeleteFileResponse {
  id: string;
  isGetErrorMsg?: boolean;
}

export const deleteFile = async ({ id, isGetErrorMsg = false }: DeleteFileResponse) => {
  try {
    const response = await api.post(`/api-admin/files/${id}/delete`);

    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '파일 삭제 중 오류 발생');
  }
};

// 파일 삭제 여러건
interface DeleteFilesResponse {
  ids: string[];
  isGetErrorMsg?: boolean;
}

export const deleteFiles = async ({ ids, isGetErrorMsg = false }: DeleteFilesResponse) => {
  try {
    const response = await api.post(`/api-admin/files/delete`, { ids });

    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '파일 삭제 중 오류 발생');
  }
};
