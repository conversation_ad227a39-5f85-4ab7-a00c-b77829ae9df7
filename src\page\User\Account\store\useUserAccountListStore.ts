import { PaginationData, UserListItem } from '@page/User/Account/type';
import { create } from 'zustand';

interface UserAccountListStore {
  // 현재 페이지
  currentPage: number;
  setCurrentPage: (page: number) => void;

  // 페이지 사이즈
  pageSize: number;
  setPageSize: (size: number) => void;

  // 사용자 계정 목록
  userListData: UserListItem[];
  setUserListData: (data: UserListItem[]) => void;

  // 페이지네이션 데이터
  paginationData: PaginationData;
  setPaginationData: (data: PaginationData) => void;
}

export const useUserAccountListStore = create<UserAccountListStore>((set) => ({
  currentPage: 1,
  setCurrentPage: (page) => set((state) => ({ currentPage: page })),

  pageSize: 10,
  setPageSize: (size) => set((state) => ({ pageSize: size })),

  userListData: [],
  setUserListData: (data) => set((state) => ({ userListData: data })),

  paginationData: null,
  setPaginationData: (data) => set((state) => ({ paginationData: data })),
}));
