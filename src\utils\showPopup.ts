import { useConfirmStore } from '@store/useConfirmStore';
import { useAlertStore } from '@store/useAlertStore';
import { ReactNode } from 'react';

export const showConfirm = (type: string, onConfirm: () => void, onCancel?: () => void, content?: ReactNode) => {
  let title: string;
  let newContent: string;

  switch (type) {
    case 'delete':
      title = '삭제';
      newContent = '삭제된 데이터는 다시 복구할 수 없습니다.<br>정말 삭제하시겠습니까?';
      break;
    case 'cancel':
      title = '알림';
      newContent = '작성 중인 내용이 사라집니다.<br>정말 취소하시겠습니까?';
      break;
    default:
      title = '알림';
      newContent = '저장하시겠습니까?';
      break;
  }

  const { setConfirmState } = useConfirmStore.getState();

  setConfirmState({
    isOpen: true,
    confirmType: type,
    title: title,
    content: content ? content : newContent,
    onCancel: () => {
      setConfirmState((prev) => ({ ...prev, isOpen: false }));
      onCancel?.();
    },
    onConfirm: () => {
      setConfirmState((prev) => ({ ...prev, isOpen: false }));
      onConfirm();
    },
  });
};

export const showAlert = (content: string | ReactNode, onConfirm?: () => void | Promise<void>) => {
  const { setAlertState } = useAlertStore.getState();

  setAlertState({
    isOpen: true,
    content,
    onConfirm: () => {
      setAlertState((prev) => ({ ...prev, isOpen: false }));
      onConfirm?.();
    },
  });
};
