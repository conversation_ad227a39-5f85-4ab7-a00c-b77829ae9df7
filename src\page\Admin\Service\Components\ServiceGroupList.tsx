import React, { useEffect } from 'react';
import IconButton from '@components/common/Button/IconButton';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import useClickLog from '@hooks/useClickLog';
import { useAdminServiceGroupAPI } from '@api/admin/serviceGroupAPI';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';
import { ServiceGroupData } from '@page/Admin/Service/type';
import { useConfirmStore } from 'store/useConfirmStore';
import { useAlertStore } from '@store/useAlertStore';
import useService from '@page/Admin/Service/hooks/useService';
import ListTable from '@components/common/Table/ListTable';

const ServiceGroupList = () => {
  const { serviceGroupList, serviceGroupDetail, setServiceGroupDetail } = useServiceStore();
  const { deleteServiceGroup } = useAdminServiceGroupAPI();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();
  const { handleClickLog } = useClickLog();
  const { handleGetServiceGroupList } = useService();

  // functions
  const handleDeleteServiceGroup = (e: React.MouseEvent, id: number) => {
    e.stopPropagation(); // 이벤트 버블링 방지
    const targetGroup = serviceGroupList.find((item) => item.id === id);
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: (
        <>
          <hr /> <br />
          <b className="text-red-500">"{targetGroup?.name}"</b> <br />
          <br />
          <hr /> <br />
          삭제한 그룹은 되돌릴 수 없습니다. <br />
          정말 삭제 하시겠습니까?
        </>
      ),
      onConfirm: async () => {
        const responseMsg = await deleteServiceGroup({ id });
        if (responseMsg) {
          // 삭제 성공 시 id가 일치하는 서비스 그룹 상세 정보 초기화
          if (id === serviceGroupDetail?.id) {
            console.log('삭제 성공 시 id가 일치하는 서비스 그룹 상세 정보 초기화');
            setServiceGroupDetail(null);
          }
          // 삭제 성공 시 서비스 그룹 목록 조회
          handleGetServiceGroupList();
          // 삭제 성공 시 알림 표시
          activeAlert(responseMsg);
        }
        initConfirmState();
      },
      onCancel: () => {
        initConfirmState();
      },
    });
  };

  const handleGroupSelect = (item: ServiceGroupData | null) => {
    setServiceGroupDetail(item);
    handleClickLog({
      eventType: 'click',
      button: '메뉴',
      buttonSection: '서비스 그룹 목록',
    });
  };

  useEffect(() => {
    if (!serviceGroupList || serviceGroupList?.length === 0) {
      handleGetServiceGroupList();
    }
  }, []);

  return (
    <>
      <ListTable
        title="서비스 그룹명"
        list={[
          <div
            key="service-group-all"
            className={!serviceGroupDetail ? 'active' : ''}
            onClick={() => handleGroupSelect(null)}
          >
            <p>전체 그룹</p>
          </div>,
          ...(serviceGroupList || []).map((item, idx) => (
            <div
              key={`service-group-${idx}`}
              className={item.id === serviceGroupDetail?.id ? 'active' : ''}
              onClick={() => handleGroupSelect(item)}
            >
              <span>{item.name}</span>
              <IconButton
                text="삭제"
                onClick={(e: React.MouseEvent) => handleDeleteServiceGroup(e, item.id)}
                design="circle"
                size="smallest"
                fill="unfilled"
                color="grayscale"
                icon="close"
                iconOnly
                clickLog={{ buttonSection: '서비스 그룹 목록' }}
              />
            </div>
          )),
        ]}
      />
    </>
  );
};

export default ServiceGroupList;
