import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Input from '@components/common/Input/Input';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Title from '@components/common/Title/Title';
import ServiceGroupDetail from '@page/Admin/Service/Components/ServiceGroupDetail';
import ServiceGroupList from '@page/Admin/Service/Components/ServiceGroupList';
import ServiceGroupModal from '@page/Admin/Service/Components/ServiceGroupModal';
import ServiceList from '@page/Admin/Service/Components/ServiceList';
import { searchByKeywordAndKey } from '@utils/searchByKeywordAndKey';
import { useState } from 'react';
import SubTitle from '@components/common/Title/SubTitle';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';

const AdminServicePage = () => {
  // global state
  const {
    serviceGroupList,
    setServiceGroupList,
    setIsOpenServiceGroupModal,
    setModalType,
    serviceGroupDetail,
    originalServiceGroupList,
  } = useServiceStore();

  // value
  const [serviceGroupName, setServiceGroupName] = useState(''); // 서비스 그룹 이름

  const handleServiceGroupName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServiceGroupName(e.target.value);
  };

  const handleRegisterServiceGroup = () => {
    setModalType('add');
    setIsOpenServiceGroupModal(true);
  };

  const handleModifyServiceGroup = () => {
    setModalType('edit');
    setIsOpenServiceGroupModal(true);
  };

  const handleSearchGroupByKeyword = () => {
    if (serviceGroupName === '') {
      setServiceGroupList(originalServiceGroupList);
      return;
    }
    const searchResult = searchByKeywordAndKey(serviceGroupList, serviceGroupName, 'name');
    setServiceGroupList(searchResult);
  };

  const handleKeydownSearchGroupByKeyword = (e: React.KeyboardEvent<HTMLButtonElement | HTMLInputElement>) => {
    e.key === 'Enter' && handleSearchGroupByKeyword();
  };

  return (
    <>
      <div className="manage_admin_service">
        <ControlBox>
          <label htmlFor="serviceGroupName">서비스 그룹명</label>
          <Input
            id="serviceGroupName"
            placeholder="서비스 그룹명을 입력하세요"
            value={serviceGroupName}
            onChange={handleServiceGroupName}
            onKeyDown={(e) => handleKeydownSearchGroupByKeyword(e)}
          />
          <Button
            text="조회"
            onClick={handleSearchGroupByKeyword}
            onKeyDown={(e) => handleKeydownSearchGroupByKeyword(e)}
            clickLog={{ buttonSection: '검색창' }}
          />
        </ControlBox>
        <div className="content horizontal">
          <div className="left_content vertical">
            <TableContainerHeader
              leftChildren={<SubTitle>서비스 그룹 목록</SubTitle>}
              rightChildren={
                <Button
                  text="추가"
                  onClick={handleRegisterServiceGroup}
                  clickLog={{ buttonSection: '서비스 그룹 목록' }}
                />
              }
            />
            <ServiceGroupList />
          </div>
          <div className="right_content vertical">
            {serviceGroupDetail && (
              <>
                <TableContainerHeader
                  leftChildren={<SubTitle>서비스 그룹 상세</SubTitle>}
                  rightChildren={
                    <Button
                      text="그룹 정보 수정"
                      onClick={handleModifyServiceGroup}
                      clickLog={{ buttonSection: '서비스 그룹 상세' }}
                    />
                  }
                />
                <ServiceGroupDetail />
              </>
            )}
            <ServiceList />
          </div>
        </div>
      </div>
      <ServiceGroupModal />
    </>
  );
};

export default AdminServicePage;
