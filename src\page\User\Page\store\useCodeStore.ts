import { GroupCodeType } from '@page/System/Code/type';
import { AlertContentsType, ConfirmStateType } from '@page/System/Setting/types';
import { create } from 'zustand';

interface CodeStore {
  // Alert 상태
  alertState: AlertContentsType;
  setAlertState: (content: AlertContentsType | ((prev: AlertContentsType) => AlertContentsType)) => void;

  // Confirm 상태
  confirmState: ConfirmStateType;
  setConfirmState: (state: ConfirmStateType | ((prev: ConfirmStateType) => ConfirmStateType)) => void;

  // CodeModal 상태
  codeModalState: ConfirmStateType;
  setCodeModalState: (state: ConfirmStateType | ((prev: ConfirmStateType) => ConfirmStateType)) => void;

  // 그룹 코드 상태
  groupCode: GroupCodeType;
  setGroupCode: (code: GroupCodeType) => void;

  // 데이터 상태
  groupCodeListData: any;
  setGroupCodeListData: (data: any) => void;
  groupCodeDetailData: any;
  setGroupCodeDetailData: (data: any) => void;
  codeListData: any;
  setCodeListData: (data: any) => void;
  codeDetail: any;
  setCodeDetail: (data: any) => void;
}

export const useCodeStore = create<CodeStore>((set) => ({
  // Alert 초기 상태 및 설정 함수
  alertState: {
    isOpen: false,
    content: '',
    onConfirm: () => {},
  },
  setAlertState: (content) =>
    set((state) => ({
      alertState: typeof content === 'function' ? content(state.alertState) : content,
    })),

  // Confirm 초기 상태 및 설정 함수
  confirmState: {
    isOpen: false,
    confirmType: null,
    content: '',
    onCancel: () => {},
    onConfirm: () => {},
  },
  setConfirmState: (state) =>
    set((prev) => ({
      confirmState: typeof state === 'function' ? state(prev.confirmState) : state,
    })),

  // Confirm 초기 상태 및 설정 함수
  codeModalState: {
    isOpen: false,
    confirmType: null,
    content: '',
    onCancel: () => {},
    onConfirm: () => {},
  },
  setCodeModalState: (state) =>
    set((prev) => ({
      codeModalState: typeof state === 'function' ? state(prev.codeModalState) : state,
    })),

  // 그룹 코드 초기 상태 및 설정 함수
  groupCode: {
    code: '',
    name: '',
    state: null,
  },
  setGroupCode: (code) => set({ groupCode: code }),

  // 데이터 초기 상태 및 설정 함수
  groupCodeListData: null,
  setGroupCodeListData: (data) => set({ groupCodeListData: data }),
  groupCodeDetailData: null,
  setGroupCodeDetailData: (data) => set({ groupCodeDetailData: data }),
  codeListData: null,
  setCodeListData: (data) => set({ codeListData: data }),
  codeDetail: null,
  setCodeDetail: (data) => set({ codeDetail: data }),
}));
