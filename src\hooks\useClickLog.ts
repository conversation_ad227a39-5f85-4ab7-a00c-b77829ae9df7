import { LogParams } from '@components/common/Log/Components/types';
import { useLogger } from '@components/common/Log/hooks/useLogger';
import { useLogStore } from '@store/useLogStore';

const useClickLog = () => {
  const { clickLog } = useLogger();
  const { setLogParams } = useLogStore();

  const handleClickLog = ({
    eventType,
    buttonSection,
    button,
  }: {
    eventType: LogParams['logType'];
    buttonSection: string;
    button: string;
  }) => {
    setLogParams((prev) => ({
      ...prev,
      buttonSection,
      button,
      logType: eventType,
    }));

    clickLog();
  };

  return { handleClickLog };
};

export default useClickLog;
