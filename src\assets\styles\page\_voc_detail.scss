@use '@styles/utils/mixin' as m;

.voc {
  .voc_detail {
    .voc_detail_inner {
      .left_content {
        .voc_card_list {
          .voc_card {
            padding: 1.5rem;
            background-color: var(--voc_card_bg);
            border-bottom: 1px solid var(--g_04);

            &:first-child {
              border-radius: 0.5rem 0.5rem 0 0;
            }

            .voc_card_inner {
              display: flex;
              gap: 1rem;
              .left_cont {
                .user_icon {
                  display: block;
                  width: 50px;
                  height: 50px;
                  border-radius: 50%;
                  background-color: var(--g_04);
                }
              }
              .right_cont {
                flex: 1;
                .voc_card_header {
                  @include m.flex(center, space-between);
                  margin-bottom: 0.75rem;
                  .user_name {
                    font-size: 1.25rem;
                    font-weight: 700;
                  }
                  .date_info {
                    font-size: 0.875rem;
                  }
                }
                .update_info {
                  text-align: right;
                  font-size: 0.875rem;
                  color: var(--g_06);
                }
              }
            }
          }
        }
        .voc_text_editor {
          padding: 1.5rem;
          border-radius: 0 0 0.5rem 0.5rem;
          background-color: var(--voc_card_bg);

          &_inner {
            display: flex;
            gap: 1rem;
            .left_cont {
              .user_icon {
                display: block;
                width: 50px;
                height: 50px;
                border-radius: 50%;
              }
            }
            .right_cont {
              flex: 1;
              .voc_text_editor_header {
                @include m.flex(center, space-between);
                margin-bottom: 0.75rem;
                .user_name {
                  font-size: 1.25rem;
                  font-weight: 700;
                }
              }
              .voc_text_editor_content {
                margin-bottom: 0.75rem;
              }
              .voc_text_editor_footer {
                @include m.flex(center, space-between);
              }
            }
          }
        }
      }
      .right_content {
        position: sticky;
        top: 80px;

        .voc_detail_info {
          @include m.flex(start, start, column);
          gap: 0.625rem;

          .detail_info_item {
            width: 100%;
            .label,
            span::not(.voc_status_card) {
              display: block;
              width: 100%;
              @include m.ellipsis;
            }

            .label {
              font-size: 0.875rem;
              font-weight: 500;
              margin-bottom: 0.125rem;
              color: var(--g_07);
            }
            .value {
              font-weight: 700;
            }
          }
        }
        .voc_detail_footer {
          margin-top: 1.25rem;
          @include m.flex(center, space-between);
          gap: 0.5rem;
          .voc_detail_edit_btn,
          .voc_detail_cancel_btn {
            flex: 1;
          }
        }
      }
    }
  }
}

@include m.bp_large {
  .voc {
    .voc_detail {
      .voc_detail_inner {
        .right_content {
          top: 75px;
        }
      }
    }
  }
}

@include m.bp_medium {
  .voc {
    .voc_detail {
      .voc_detail_inner {
        .right_content {
          top: 65px;
        }
      }
    }
  }
}
