import React, { TableHTMLAttributes } from 'react';
import joinClassName from '@utils/joinClassName';

interface TableSectionProps extends TableHTMLAttributes<HTMLTableSectionElement> {}

const TableBody = ({ children, ...attributes }: TableSectionProps) => {
  return (
    <tbody className={joinClassName('c_table_body')} {...attributes}>
      {children}
    </tbody>
  );
};

export default TableBody;
