import FontSize from 'tiptap-extension-font-size';

export const CustomFontSize = FontSize.extend({
  addKeyboardShortcuts() {
    return {
      'Mod-ArrowUp': () => {
        const currentSize = parseInt(this.editor.getAttributes('textStyle').fontSize) || 16;
        const newSize = currentSize + 2;
        return this.editor.commands.setFontSize(`${newSize}px`);
      },
      'Mod-ArrowDown': () => {
        const currentSize = parseInt(this.editor.getAttributes('textStyle').fontSize) || 16;
        const newSize = Math.max(currentSize - 2, 8); // 최소 8px로 제한
        return this.editor.commands.setFontSize(`${newSize}px`);
      },
    };
  },
});