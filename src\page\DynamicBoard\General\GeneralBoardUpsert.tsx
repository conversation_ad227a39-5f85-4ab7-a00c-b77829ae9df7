import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import TextEditor from '@components/common/TextEditor/TextEditor';
import FormInput from '@components/common/Form/FormInput';
import Filebox from '@components/common/Filebox/Filebox';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Form from '@components/common/Form/Form';
import { DateInfo } from '@page/DynamicBoard/General/type';
import { createGeneralBoardPost, getGeneralBoardDetail, updateGeneralBoardPost } from '@api/admin/dynamicBoardApi';
import { getFile } from '@api/admin/fileAPI';
import { fileAcceptList, useYnOptions } from '@constants/options';
import type { Menu } from './type';

const GeneralBoardUpsert = (menuInfo: Menu) => {
  const { id } = useParams();
  const mode = id ? 'edit' : 'add';
  const navigate = useNavigate();
  const initValues: any = { title: '', notiYn: { label: useYnOptions[0].label, value: useYnOptions[0].value } };

  const [defaultValues, setDefaultValues] = useState(initValues);
  const [dateInfo, setDateInfo] = useState<DateInfo>(null);
  const [fileList, setFileList] = useState<FileList | null>(null);
  const [originalContents, setOriginalContents] = useState<string>('<p style=""><span></span></p>');
  const [originalFileList, setOriginalFileList] = useState<FileList | null>(null);
  const [originalFileMeta, setOriginalFileMeta] = useState([]);
  const [contents, setContents] = useState<string>('');
  const formMethods = useForm({
    defaultValues,
  });

  const getFileObj = async ({ fileId, fileName }: { fileId: string; fileName: string }) => {
    const response = await getFile({ id: fileId });
    const file = new File([response], fileName, { type: response.type });
    return file;
  };

  useEffect(() => {
    if (mode === 'edit') {
      const handleGetPostDetail = async () => {
        const response = await getGeneralBoardDetail({ bbsId: menuInfo.boardTempletId, postId: id });
        const detailData: any = {
          title: response.title,
          notiYn: {
            label: utils.getLabel(response.notiYn, useYnOptions),
            value: response.notiYn,
          },
        };
        const files = (await Promise.all(
          response.attchFiles.map((fileData) => getFileObj({ fileId: fileData.id, fileName: fileData.originName }))
        )) as File[];
        const toFileList = (files: File[]): FileList => {
          const dataTransfer = new DataTransfer();
          files.forEach((file) => dataTransfer.items.add(file));
          return dataTransfer.files;
        };
        formMethods.reset(detailData);
        setDefaultValues(detailData);

        setDateInfo({
          createUser: response.dateInfo.createUser,
          createDate: response.dateInfo.createDate,
          updateUser: response.dateInfo.updateUser,
          updateDate: response.dateInfo.updateDate,
        });
        setContents(response.content);
        setFileList(toFileList(files));
        setOriginalContents(response.content);
        setOriginalFileList(toFileList(files));
        setOriginalFileMeta(response.attchFiles);
      };
      handleGetPostDetail();
    }
  }, []);

  // 수정 시 파일 객체 비교
  const addedFileKey = (file: File) => `${file.name}_${file.size}_${file.lastModified}`;
  const deletedfileKey = (name: string, size: number) => `${name}_${size}`;

  const compareFileLists = () => {
    const originalFiles = Array.from(originalFileList);
    const currentFiles = Array.from(fileList);

    const currentKeys = new Set(Array.from(currentFiles).map((file) => deletedfileKey(file.name, file.size)));
    const originalKeys = new Set(originalFiles.map(addedFileKey));

    const deletedFileIds = originalFileMeta
      .filter((meta) => !currentKeys.has(deletedfileKey(meta.originName, meta.size)))
      .map((meta) => meta.id);
    const addedFiles = currentFiles.filter((f) => !originalKeys.has(addedFileKey(f)));

    return {
      deletedFileIds,
      addedFiles,
    };
  };

  const getRequiredMsg = (inValidFieldList) => {
    const fieldMessages = {
      title: '제목을 입력해주세요.',
      contents: '내용을 입력해주세요.',
    };

    const messages = inValidFieldList.map((field) => fieldMessages[field]);
    return messages[0];
  };

  const getInvalidFields = (formData) => {
    const isBlank = (val) => _.isEmpty(_.trim(val));

    const stripHtml = (html) => {
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    };

    const { title, contents } = formData;
    const invalidFields: string[] = [];

    // 제목 검증
    if (isBlank(title)) {
      invalidFields.push('title');
    }

    // 내용 검증
    const plainText = stripHtml(contents);
    const isContentEmpty = isBlank(plainText);

    const isAddAndSameAsOriginal = mode === 'add' && contents === originalContents;

    if (isContentEmpty || isAddAndSameAsOriginal) {
      invalidFields.push('contents');
    }

    return invalidFields;
  };

  // 저장/수정
  const formSubmit = async (formData: any) => {
    const newData = utils.flattenValues(formData);
    newData.contents = contents;

    const inValidFieldList = getInvalidFields(newData);

    if (!_.isEmpty(inValidFieldList)) {
      const message = getRequiredMsg(inValidFieldList);
      utils.showAlert(message);
      return;
    }

    let res;
    newData.bbsId = menuInfo.boardTempletId;
    if (mode === 'add') {
      newData.files = { files: fileList };
      res = await createGeneralBoardPost(newData);
    } else if (mode == 'edit') {
      const { addedFiles, deletedFileIds } = compareFileLists();
      const editPostData = {
        ...newData,
        ...(mode === 'edit' && addedFiles && { saveFiles: addedFiles }),
        ...(mode === 'edit' && deletedFileIds && { delFileIds: deletedFileIds }),
      };
      editPostData.postId = id;
      newData.files = res = await updateGeneralBoardPost(editPostData);
    }

    if (mode === 'add') {
      utils.showAlert(res.message, onNavigateList);
    } else {
      utils.showAlert(res.message, onNavigateDetail);
    }
  };

  const onNavigateList = () => {
    navigate(`${menuInfo.url}`);
  };

  // 초기화
  const handleReset = () => {
    formMethods.reset(defaultValues);
    setContents(originalContents);
    setFileList(originalFileList);
  };

  const onClickBtnCancel = () => {
    if (mode === 'add') {
      utils.showConfirm('cancel', onNavigateList);
    } else if (mode === 'edit') {
      utils.showConfirm('cancel', onNavigateDetail);
    } else {
      return;
    }
  };

  const onNavigateDetail = () => {
    navigate(`${menuInfo.url}/detail/${id}`);
  };

  return (
    <>
      <Form onSubmit={formSubmit} methods={formMethods} className="admin_post_detail_wrapper">
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '제목',
                required: true,
                isFullWidth: true,
                contents: <FormInput name="title" wrapperClassName="full" placeholder="제목을 입력해 주세요" />,
              }}
            />
            <TableBodyRow
              rowData={{
                title: '공지 여부',
                required: true,
                isFullWidth: true,
                contents: <FormSelectBox name="notiYn" options={useYnOptions} />,
              }}
            />
            {mode === 'edit' && (
              <>
                <TableBodyRow
                  rowData={[
                    {
                      title: '작성자',
                      contents: dateInfo?.createUser,
                    },
                    {
                      title: '작성 일시',
                      contents: dateInfo?.createDate,
                    },
                  ]}
                />
                <TableBodyRow
                  rowData={[
                    {
                      title: '수정자',
                      contents: dateInfo?.updateUser,
                    },
                    {
                      title: '수정 일시',
                      contents: dateInfo?.updateDate,
                    },
                  ]}
                />
              </>
            )}
            <TableBodyRow
              rowData={[
                {
                  title: '첨부파일',
                  isFullWidth: true,
                  contents: (
                    <Filebox
                      className="admin_post_filebox"
                      files={fileList}
                      setFiles={setFileList}
                      multiple
                      accept={fileAcceptList}
                    />
                  ),
                },
              ]}
            />
          </TableBody>
        </TableContainer>
        <TextEditor
          className={utils.joinClassName('admin_post_detail_contents', mode === 'add' && 'add')}
          content={contents}
          onChange={setContents}
        />
        <div className="admin_post_detail_btns">
          <Button text="취소" onClick={onClickBtnCancel} />
          <div className="admin_post_detail_btns_right">
            <Button text="초기화" color="grayscale" onClick={handleReset} />
            <Button text="저장" type="submit" />
          </div>
        </div>
      </Form>
    </>
  );
};

export default GeneralBoardUpsert;
