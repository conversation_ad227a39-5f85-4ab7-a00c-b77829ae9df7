import { useState } from 'react';
import TestTable from '../TestTable';
import SelectBox from '@components/common/SelectBox/SelectBox';
import SelectCheckBox from '@components/common/SelectBox/SelectCheckBox';

const TestSelectBox = () => {
  const options = [
    { label: '예제1', value: '값1' },
    { label: '예제2', value: '값2' },
    { label: '예제3', value: '값3' },
    { label: '예제4', value: '값4' },
  ];
  const checkOptions = [
    { label: '예제1', value: '값1' },
    { label: '예제2', value: '값2' },
    { label: '예제3', value: '값3' },
    { label: '예제4', value: '값4' },
  ];
  const [selectedValue, setSelectedValue] = useState(null);
  const [checkSelectedValue, setCheckSelectedValue] = useState([]);
  return (
    <TestTable
      compName="select"
      headChild={
        <>
          <tr>
            <th colSpan={3}>SelectBox</th>
          </tr>
          <tr>
            <th style={{width: '10%'}}>Status</th>
            <th style={{width: '40%'}}>Single Selection (Default)</th>
            <th style={{width: '40%'}}>Multiple Selection</th>
          </tr>
        </>
      }
      bodyChild={
        <tr>
          <th>Default</th>
          <td>
            <SelectBox options={options} selectedValue={null} setSelectedValue={setSelectedValue} />
          </td>
          <td>
            <SelectCheckBox
              options={checkOptions}
              selectedValue={checkSelectedValue}
              setSelectedValue={setCheckSelectedValue}
            />
          </td>
        </tr>
      }
    />
  );
};

export default TestSelectBox;
