import { DateInfo } from '@page/Admin/Account/type';
import React from 'react';
import TextEditor from '@components/common/TextEditor/TextEditor';

interface VOCCardProps {
  content: string;
  createUserType: string;
  updateUserType: string;
  id: number;
  vocId: number;
  dateInfo: DateInfo;
}

const VOCCard = ({ content, createUserType, updateUserType, id, vocId, dateInfo }: VOCCardProps) => {
  return (
    <li className="voc_card">
      <div className="voc_card_inner">
        <div className="left_cont">
          <div className="user_icon"></div>
        </div>
        <div className="right_cont">
          <div className="voc_card_header">
            <h3 className="user_name">
              {dateInfo.createUser} <span>{createUserType === 'ADMIN' ? '<관리자>' : '<사용자>'}</span>
            </h3>
            <p className="date_info">{dateInfo.createDate}</p>
          </div>
          {/* 텍스트 에디터 뷰어 */}
          <TextEditor className="voc_card_content" content={content} readOnly />
          {/* 수정 정보 */}
          {updateUserType && dateInfo.updateUser && (
          <div className="update_info">
            <p className="update_user">
              최종 수정자 : {dateInfo?.updateUser}{' '}
              <span>{updateUserType ? (updateUserType === 'ADMIN' ? '<관리자>' : '<사용자>') : ''}</span>
            </p>
            <p className="update_date">{dateInfo?.updateDate}</p>
          </div>
          )}
        </div>
      </div>
    </li>
  );
};

export default VOCCard;
