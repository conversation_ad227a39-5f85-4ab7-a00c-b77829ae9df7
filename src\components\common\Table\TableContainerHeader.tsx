import joinClassName from '@utils/joinClassName';

interface TableContainerHeaderProps {
  leftChildren?: React.ReactNode | string | number;
  rightChildren?: React.ReactNode | string | number;
  className?: string;
}

const TableContainerHeader = ({ leftChildren, rightChildren, className }: TableContainerHeaderProps) => {
  return (
    <div className={joinClassName('c_table_container_header', className)}>
      <div className="left_cont">{leftChildren}</div>
      <div className="right_cont">{rightChildren}</div>
    </div>
  );
};

export default TableContainerHeader;
