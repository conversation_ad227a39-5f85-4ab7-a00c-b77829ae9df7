import Button from '@components/common/Button/Button';
import DefaultModal from '@components/common/Modal/DefaultModal';
import QRCode from 'qrcode-generator';
import { useEffect, useRef, useState } from 'react';
import useLoginAPI from '@api/common/useLoginAPI';
import joinClassName from '@utils/joinClassName';

interface QRLoginProps {
  isOpenQRLoginModal: boolean;
  setIsOpenQRLoginModal: (value: boolean) => void;
}

export type SessionData = {
  sessionId: string;
  expirationTime: string;
};

const QRLogin = ({ isOpenQRLoginModal, setIsOpenQRLoginModal }: QRLoginProps) => {
  const { handlePostSessionData, connectSocket } = useLoginAPI();
  const [sessionData, setSessionData] = useState<SessionData>({
    sessionId: '',
    expirationTime: '',
  });
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [isExpired, setIsExpired] = useState<boolean>(false);
  const qrRef = useRef<HTMLDivElement>(null);

  const handleCloseModal = () => {
    setIsOpenQRLoginModal(false);
  };

  const makeQRCode = (data: string) => {
    const qrCode = QRCode(10, 'H');
    qrCode.addData(data);
    qrCode.make();

    return qrCode.createImgTag();
  };

  useEffect(() => {
    let socket: WebSocket | null = null;
    if (sessionData.sessionId) {
      socket = connectSocket(sessionData.sessionId, handleCloseModal, setRemainingTime, setIsExpired);

      if (qrRef.current) {
        const qrImg = makeQRCode(sessionData.sessionId);
        qrRef.current.innerHTML = qrImg;
        setIsExpired(false);
      }
    } else {
      handlePostSessionData(setSessionData, setRemainingTime);
    }
    return () => {
      if (socket) {
        socket.close();
        socket = null;
      }
    };
  }, [sessionData.sessionId]);

  useEffect(() => {
    if (remainingTime > 0) {
      const interval = setInterval(() => {
        setRemainingTime(remainingTime - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [remainingTime]);

  return (
    <DefaultModal
      isOpenModal={isOpenQRLoginModal}
      setIsOpenModal={setIsOpenQRLoginModal}
      onClickedDim={handleCloseModal}
      className="c_qr_login_modal"
      title="QR 로그인"
      footer={<Button text="닫기" onClick={handleCloseModal} />}
    >
      <div className="c_qr_login_modal_content">
        <p className="desc">
          모바일 기기로 QR 코드를 촬영하시면 <br />
          자동으로 인증후 로그인이 됩니다.
        </p>
        <div className="qr_code_wrapper">
          <div className={joinClassName('qr_code_img', isExpired ? 'expired' : '')} ref={qrRef} />
          <p className="remaining_time">{isExpired ? '만료되었습니다.' : `남은 시간: ${remainingTime}초`}</p>
        </div>
      </div>
    </DefaultModal>
  );
};

export default QRLogin;
