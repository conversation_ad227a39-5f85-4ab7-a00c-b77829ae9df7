import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';

const ServiceGroupDetail = ({ handleModifyServiceGroup }) => {
  const { serviceGroupDetail } = useServiceStore();
  return (
    <>
      <div className="service_group_detail_wrapper">
        <TableContainer className="service_group_detail">
          <colgroup>
            <col width={'15%'} />
            <col width={'35%'} />
            <col width={'15%'} />
            <col width={'35%'} />
          </colgroup>
          <TableBody>
            <tr>
              <th>서비스 그룹명</th>
              <td>{serviceGroupDetail?.name}</td>
              <th>서비스 그룹ID</th>
              <td>{serviceGroupDetail?.id}</td>
            </tr>
            <tr>
              <th>등록자</th>
              <td>{serviceGroupDetail?.dateInfo?.createUser}</td>
              <th>등록일자</th>
              <td>{serviceGroupDetail?.dateInfo?.createDate}</td>
            </tr>
            <tr>
              <th>수정자</th>
              <td>{serviceGroupDetail?.dateInfo?.updateUser}</td>
              <th>수정일자</th>
              <td>{serviceGroupDetail?.dateInfo?.updateDate}</td>
            </tr>
            <tr>
              <th>서비스 그룹 설명</th>
              <td colSpan={3}>{serviceGroupDetail?.groupDesc}</td>
            </tr>
          </TableBody>
        </TableContainer>
        {!serviceGroupDetail && (
          <div className="no_result">
            서비스 그룹이 선택되지 않았습니다. <br />
            조회할 서비스 그룹을 선택해주세요.
          </div>
        )}
      </div>
      {serviceGroupDetail && <Button className="modify_btn" text="그룹 정보 수정" onClick={handleModifyServiceGroup} />}
    </>
  );
};

export default ServiceGroupDetail;
