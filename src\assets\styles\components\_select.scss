@use '@styles/utils/mixin' as m;

.c_select {
  position: relative;

  .c_selected_label {
    @include m.flex(center, center);
    position: relative;
    padding: 0 2.25rem 0 0.625rem;
    border-radius: 0.25rem;
    min-height: 2.25rem;
    cursor: pointer;
    color: var(--font_black);
    background-color: white;
    border: 1px solid var(--g_06);

    span {
      width: 100%;
      @include m.ellipsis();
    }

    &::after {
      @include m.content('@assets/images/icon/icon_triangle_down.svg', 1rem, 1rem);
      position: absolute;
      top: 50%;
      right: 0.75rem;
      transform: translateY(-50%);
    }

    &[aria-disabled='true'] {
      color: var(--font_disabled);
      background-color: var(--g_04);
    }

    &.open {
      .c_selected_label {
        @include m.after_url('@assets/images/icon/icon_triangle_up.svg');
      }
    }
  }
}

.c_select_lists {
  position: absolute;
  max-height: 300px;
  border-radius: 4px;
  overflow-y: auto;
  z-index: 10002;
  border: 1px solid var(--g_06);
  background-color: var(--modal_bg);
  box-shadow: 0px 4px 5px rgba(black, 0.3);

  .c_select_lists_inner {
    .c_select_option_item {
      min-height: 2.25rem;
      padding: 0.225rem 0.625rem;
      @include m.ellipsis();
      cursor: pointer;
      color: var(--font_default);
      background-color: var(--modal_bg);

      &:not(:last-child) {
        border-bottom: 0.5px dotted var(--g_07);
      }

      &:hover,
      &:focus {
        background-color: var(--g_02);
      }

      &.selected {
        background-color: var(--p_01);
      }

      .c_checkbox {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
}

@include m.bp_large() {
  .c_select {
    .c_selected_label {
      padding: 0 2rem 0 0.5rem;
      min-height: 2rem;

      span {
        font-size: 0.875rem;
      }

      &::after {
        @include m.content_without_url(0.875rem, 0.875rem);
        right: 0.625rem;
      }
    }
  }

  .c_select_lists {
    .c_select_lists_inner {
      .c_select_option_item {
        @include m.flex(center);

        min-height: 2rem;
        font-size: 0.875rem;
      }
    }
  }
}

@include m.bp_medium() {
  .c_select {
    .c_selected_label {
      padding: 0 1.75rem 0 0.5rem;
      min-height: 1.75rem;

      span {
        font-size: 0.75rem;
      }

      &::after {
        @include m.content_without_url(0.75rem, 0.75rem);
        right: 0.5rem;
      }
    }
  }

  .c_select_lists {
    .c_select_lists_inner {
      .c_select_option_item {
        @include m.flex(center);

        min-height: 1.75rem;
        font-size: 0.75rem;
      }
    }
  }
}
