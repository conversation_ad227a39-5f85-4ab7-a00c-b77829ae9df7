import api from '@api/api';

export const useEditorAPI = () => {
  const uploadFile = async (files: FileList) => {
    try {
      const formData = new FormData();
      formData.append('files', files[0], files[0].name);
      formData.append('type', 'BBS');

      const apiUrl = '/api/files/upload';
      const response = await api.post(apiUrl, formData);

      if (!response.data.data?.[0]) {
        throw new Error('파일 업로드에 실패했습니다.');
      }

      return response.data.data[0];
    } catch (error) {
      console.error('파일 업로드 중 오류 발생:', error);
      throw error;
    }
  };

  return { uploadFile };
};
