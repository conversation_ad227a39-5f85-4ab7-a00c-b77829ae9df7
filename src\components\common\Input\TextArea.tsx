import { forwardRef, TextareaHTMLAttributes } from 'react';
import { TextAreaProps } from './types';
import joinClassName from '@utils/joinClassName';

const TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(
  ({ placeholder, design = 'default', className, error, ...attributes }, ref) => {
    const textAreaClass = joinClassName('c_textarea', design, className);

    return (
      <>
        <textarea ref={ref} className={textAreaClass} placeholder={placeholder} {...attributes} />
        {error && <p className="error_msg">{error}</p>}
      </>
    );
  }
);

export default TextArea;
