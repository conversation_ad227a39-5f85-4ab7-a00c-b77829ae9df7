import { useForm } from 'react-hook-form';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import Button from '@components/common/Button/Button';
import { AdminBoardAddFormValue, AdminBoardDetailType } from '@page/Admin/Board/type';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { deleteBoard, getBoardDetail, postBoard, putBoard, getBoardCategoryList } from '@api/admin/boardApi';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';


const AdminBoardDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const [defaultValues, setDefaultValues] = useState<AdminBoardDetailType | undefined>(undefined);
  const methods = useForm<AdminBoardDetailType>({ defaultValues });
  const { reset, getValues } = methods;

  useEffect(() => {
    const getBoardDetailData = async () => {

      const response = await getBoardDetail({ id });
      const detailData: AdminBoardDetailType = {
          boardType : response.templetTypeNm,
          boardName : response.templetNm,
          fileUseYn : response.fileUseYn,
          commentUseYn : response.replyUseYn,
          categoryA : response.cateGrpCd1,
          categoryB : response.cateGrpCd2,
          categoryANm : response.cateGrpCdNm1,
          categoryBNm : response.cateGrpCdNm2,
          extraInfo : response.additionInfoDesc,
          boardUseYn : response.useYn
      };

      reset(detailData);
      setDefaultValues(detailData);

    };

    getBoardDetailData();
  }, []);

  // 삭제
  const handleDeleteBoard = async () => {
    initConfirmState();

    if (getValues('boardUseYn') === 'Y') {
      setAlertState({
        isOpen: true,
        content: '사용중인 게시판은 삭제할 수 없습니다',
        onConfirm: () => {
          initAlertState();
        },
      });
    } else {
      const response = await deleteBoard({ ids: [id] });

      if (response === 'DELETE') {
        setAlertState({
          isOpen: true,
          content: '게시판이 삭제되었습니다',
          onConfirm: () => {
            navigate(`/admin/board`);
            initAlertState();
          },
        });
      }
    }
  };

  const handleClickDeleteBtn = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: handleDeleteBoard,
    });
  };

  const useYnTextMap: Record<string, string> = {
    Y : '사용',
    N : '미사용', 
  };

  return (
    <div className="admin_board">
      <TableContainer className="admin_board_add_form">
        <TableBody>
          <TableBodyRow
            rowData={{
              title: '게시판 유형',
              contents: (
                <>
                  <p>{defaultValues?.boardType}</p>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '게시판 명',
              contents: (
                <>
                  <p>{defaultValues?.boardName}</p>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '첨부파일 사용 유무',
              contents: (
                <>
                  <p>{useYnTextMap[defaultValues?.fileUseYn ?? '']}</p>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '댓글 사용 유무',
              contents: (
                <>
                  <p>{useYnTextMap[defaultValues?.commentUseYn ?? '']}</p>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '카테고리1',
              contents: (
                <>
                  <div>
                    <p>{defaultValues?.categoryANm}</p>
                  </div>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '카테고리2',
              contents: (
                <>
                  <div>
                    <p>{defaultValues?.categoryBNm}</p>
                  </div>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '부가 정보',
              contents: (
                <>
                  <p>{defaultValues?.extraInfo}</p>
                </>
              ),
            }}
          />
          <TableBodyRow
            rowData={{
              title: '게시판 사용 유무',
              contents: (
                <>
                  <p>{useYnTextMap[defaultValues?.boardUseYn ?? '']}</p>
                </>
              ),
            }}
          />
        </TableBody>
      </TableContainer>
      <div className="admin_board_add_btn">
        <Button
          text="목록"
          onClick={() => navigate(`/admin/board`)}
        />
        <div className="btn_right">
          <>
            <Button text="삭제" color="red" onClick={handleClickDeleteBtn} />
            <Button text="수정" onClick={() => navigate(`/admin/board/edit/${id}`)} />
          </>
        </div>
      </div>
    </div>
  );
};

export default AdminBoardDetail;
