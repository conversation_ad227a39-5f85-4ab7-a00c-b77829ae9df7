import IconButton from '@components/common/Button/IconButton';
import Input from '@components/common/Input/Input';
import React, { useEffect, useState } from 'react';
import TestTable from '../TestTable';
import SearchBox from '@components/common/Input/SearchBar';

const TestSearchbarField = () => {
  const [inputVal, setInputVal] = useState('');
  const [focusedInput, setFocusedInput] = useState(false);
  const handleInputVal = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputVal(e.target.value);
  };

  const handleFocusedInput = () => {
    setFocusedInput((prev) => !prev);
  };

  const handleReset = () => {
    setInputVal('');
  };


  const searchList = [
    { label: 'keword 1', value: 1 },
    { label: 'keword 2', value: 2 },
    { label: 'keword 3', value: 3 },
    { label: 'keword 4', value: 4 },
    { label: 'keword 5', value: 5 },
    { label: 'keword 6', value: 6 },
    { label: 'keword 7', value: 7 },
    { label: 'keword 8', value: 8 },
  ];

  return (
    <TestTable
      compName="searchbar"
      headChild={
        <>
          <tr>
            <th colSpan={3}>SearchBar Type ( ** Single/Multi Chips 추후 개발예정 )</th>
          </tr>
          <tr>
            <th>Status</th>
            <th>Text (Default)</th>
            {/*
                추후 개발 예정 
                <th>SingleChip</th>
                <th>MultipleChip</th> 
              */}
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr className="p_default">
            <th>Default</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} />
            </td>
          </tr>
          <tr className="p_default">
            <th>Focused - Typing</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} searchList={searchList} />
            </td>
          </tr>
          <tr className="p_disabled">
            <th>Disabled</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} disabled />
            </td>
          </tr>
          <tr className="p_readonly">
            <th>Readonly</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} readonly />
            </td>
          </tr>
          <tr className="p_error">
            <th>Error</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} design="error" />
            </td>
          </tr>
          <tr className="p_warning">
            <th>Warning</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} design="warning" />
            </td>
          </tr>
          <tr className="p_confirm">
            <th>confirm</th>
            <td>
              <SearchBox searchValue={inputVal} setSearchValue={setInputVal} design="confirm" />
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestSearchbarField;
