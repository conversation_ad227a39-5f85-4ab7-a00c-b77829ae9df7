import { getMyMenus } from '@api/admin/menuManageApi';
import Loading from '@components/common/Loading';
import Error from '@page/Error';
import Login from '@page/Login/Login';
import Logout from '@page/Login/Logout';
import MyInfo from '@page/MyInfo/MyInfo';
import { useLoginStore } from '@store/useLoginStore';
import { useMenuStore } from '@store/useMenuStore';
import App from 'App';
import { createElement, lazy, useEffect, useMemo, useState } from 'react';
import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom';
import ProtectedRoute from 'route/ProtectedRoute';
const pageModules = import.meta.glob(
  '../page/**/*.tsx'
) as Record<string, () => Promise<{ default: React.ComponentType<any> }>>;

export const RoutingDev = () => {
  const { myMenuList, setMyMenuList } = useMenuStore();
  const { dynamicMenuList, setDynamicMenuList } = useMenuStore();
  const [isMenuLoaded, setIsMenuLoaded] = useState(false);
  const { isLogin, setIsLogin } = useLoginStore();

  // 본인 권한 메뉴 조회 목록 >> 최초 권한 조회
  const getMyMenusData = async () => {
    try {
      setIsMenuLoaded(false);
      const response = await getMyMenus({});
      // 메뉴목록이 없으면 추가를 안함
      if (!response) {
        setMyMenuList([]);
        return;
      };
      setMyMenuList(response);
    } catch(e) {
      console.log('getMyMenusData error', e);
    } finally {
      setIsMenuLoaded(true);
    }
  };

  useEffect(() => {
    const excludedPaths = ['/login', '/logout', '/error'];
    if (isLogin && !excludedPaths.includes(window.location.pathname)) {
      // 로그인 관련 페이지가 아닐 때에만 메뉴 조회
      getMyMenusData();
    } else {
      // 바로 로딩 완료 처리
      setIsMenuLoaded(true);
    }
  }, [isLogin]);

  const createMenus = (menuList) => {
    const routes = [];

    const buildRoutes = (menus) => {
      menus.forEach((menu) => {
        // index 처리 
        if (menu.parentId === null && menu.childMenus && menu.childMenus.length > 0) {
          routes.push({
            path: menu.url,
            element: <Navigate to={menu.childMenus[0].url} replace />,
          });
        }

        if (menu.filePath) {
          const path = `../page${menu.filePath}.tsx`;
          const loader = pageModules[path];
          if (loader) {
            routes.push({
              path: menu.url,
              element: createElement(lazy(loader), menu),
            });
          } else {
            console.log('페이지 경로를 찾을 수 없습니다:', path);
          }
        }

        if (menu.childPages) {
          menu.childPages.forEach((page) => {
            let pageUrl = menu.url;
            if (page.pageTypeCode === 'L' || page.pageTypeCode === 'B') {
              pageUrl += '/list';
            } else if (page.pageTypeCode === 'P') {
              pageUrl += '/detail/:id';
            } else if (page.pageTypeCode === 'R') {
              pageUrl += '/add';
            } else if (page.pageTypeCode === 'M') {
              pageUrl += '/edit/:id';
            } else if (page.pageTypeCode === 'T' && page.pageUrl) {
              pageUrl += page.pageUrl;
            }
            const path = `../page${page.filePath}.tsx`;
            const loader = pageModules[path];
            if (loader) {
              routes.push({
                path: pageUrl,
                element: createElement(lazy(loader), page),
              });
            } else {
              console.log('페이지 경로를 찾을 수 없습니다:', path);
            }
          });
        }
        if (menu.childMenus && menu.childMenus.length > 0) {
          buildRoutes(menu.childMenus);
        }
      });
    };

    buildRoutes(menuList);
    return routes;
  };

  useEffect(() => {
    const myInfo = [
      {
        path: 'myInfo',
        element: <MyInfo />,
      },
    ];
    if (myMenuList.length > 0) {
      const dynamicMenus = createMenus(myMenuList);
      setDynamicMenuList([...myInfo, ...dynamicMenus]);
    } else {
      setDynamicMenuList([...myInfo]);
    }
  }, [myMenuList]);

  const dynamicRoute = useMemo(() => {
    return createBrowserRouter([
      {
        element: <App />,
        children: [
          {
            path: '/',
            element: <ProtectedRoute />,
            children: [
              // 동적 메뉴 생성
              ...dynamicMenuList,
            ],
          },
          {
            path: '*',
            element: <Navigate to="/error" replace={true} />,
          },
          {
            path: 'error',
            element: <Error />,
          },
          {
            path: 'login',
            element: <Login />,
          },
          {
            path: 'logout',
            element: <Logout />,
          },
        ],
      },
    ])
  }, [dynamicMenuList]);

  if (!isMenuLoaded) {
    return (
      <Loading isLoading={true} />
    );
  }

  return <RouterProvider router={dynamicRoute} />;
};
