@use '@styles/utils/mixin' as m;

[data-is-open-lnb='true'] {
  main {
    .c_main_content {
      min-width: 1080px;
    }
  }
}

[data-is-open-lnb='false'] {
  main {
    .c_main_content {
      min-width: 1225px;
    }
  }
}

main {
  @include m.flex(start, space-between);

  .c_main_content {
    width: 100%;
    margin-top: 60px;
    margin-left: 75px; // LNB 너비만큼 여백 추가
    padding: 1.5rem;
    transition: all 0.3s;
    min-height: calc(100vh - 60px);
  }
  
  @include m.bp_large() {
    .c_main_content {
      margin-left: 65px;
      padding: 1.25rem;
    }
  }
  
  @include m.bp_medium() {
    .c_main_content {
      min-height: calc(100vh - 55px);
      margin-left: 55px;
      margin-top: 55px; // LNB 너비만큼 여백 추가
      padding: 1rem;
    }
  }
}
