import { useCurrentEditor } from '@tiptap/react';
import EditorImage from './EditorImage';
import EditorVideo from './EditorVideo';
import EditorTable from './EditorTable';
import EditorLink from './EditorLink';

const Embeds = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  return (
    <>
      <div className="item_box">
        <EditorImage />
        <EditorVideo />
        <EditorTable />
        <EditorLink />
      </div>
    </>
  );
};

export default Embeds;
