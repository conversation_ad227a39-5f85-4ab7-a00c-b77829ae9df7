@use '@styles/utils/mixin' as m;

.c_radio {
  position: relative;
  @include m.inline_flex(center, center);
  line-height: 1.25rem;
  cursor: pointer;

  input {
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    height: 1.25rem;
    width: 1.25rem;
    z-index: -1;
  }
  span {
    position: relative;
    display: inline-block;
    padding-left: 1.5rem;

    &.sr-only {
      min-height: 1.25rem;
      padding-left: 1.25rem;
    }

    &::before {
      @include m.content_without_url(1.25rem, 1.25rem);
      display: inline-block;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      border-radius: 0.25rem;
      margin-right: 8px;
      min-width: 1.25rem;
      // transition: all 0.3s;
    }
  }

  input + span {
    &::before {
      border: 1px solid var(--g_06);
      border-radius: 50rem;
      background-color: var(--modal_bg);
    }
  }

  input:checked + span {
    &::before {
      border: none;
      background-color: var(--font_default);
    }
    @include m.before_url('@assets/images/icon/icon_radio.svg');
  }

  input:disabled + span {
    opacity: 0.5;
  }

  input:checked:disabled + span {
    @include m.before_url('@assets/images/icon/icon_radio_disabled.svg');
  }
}

.c_radio_list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  .c_radio_group {
    display: flex;
    gap: 1rem;

    &.vertical {
      flex-direction: column;
    }

    &.horizontal {
      flex-direction: row;
      flex-wrap: wrap;
    }
  }
}
