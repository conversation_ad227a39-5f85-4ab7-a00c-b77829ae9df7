@use '@styles/utils/mixin' as m;

.c_map {
  width: 100%;
  height: 100%;

  img {
    transition: filter 0.3s ease-in-out;
    &:hover {
      filter: brightness(0.8);
    }
  }
}

.no_map {
  width: 100%;
  height: 100%;
  @include m.flex(center, center, column);
  gap: 1rem;

  &_icon {
    width: 100px;
    height: 100px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;

    &[data-type='map'] {
      background-image: url('@assets/images/icon/icon_maps_gray.svg');
    }
    &[data-type='error'] {
      background-image: url('@assets/images/icon/icon_error_gray.svg');
    }
  }

  p {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--g_06);
  }
}
