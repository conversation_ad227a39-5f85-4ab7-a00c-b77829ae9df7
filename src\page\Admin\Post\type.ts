export interface AdminPostSearchOptions {
  pageSize: { label: string; value: number }[];
  boardList: { label: string; value: number }[];
}

export interface AdminPostFormValue {
  board: { label: string; value: number };
  postTitle: string;
}

export interface AdminPostDetailType {
  id: number;
  title: string;
  content: string;
  viewCnt: number;
  delYn: 'Y' | 'N';
  dateInfo: {
    createUser: string;
    createDate: string;
    updateUser: string;
    updateDate: string;
  };
  templetId: number | null;
  notiYn: 'Y' | 'N';
  templetNm: string;
  templetTypeCd: string;
  templetTypeNm: string;
  attchFiles: any[];
}

export interface AdminPostDetailOptions {
  templetList: { label: string; value: string }[];
  notiYn: { label: string; value: 'Y' | 'N' }[];
}
export interface AdminPostDetailFormValue {
  templetType: { label: string; value: string };
  templetList: { label: string; value: string };
  notiYn: { label: string; value: 'Y' | 'N' };
  title: string;
}
