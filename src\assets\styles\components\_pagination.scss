@use '@styles/utils/mixin' as m;

.c_pagination {
  .c_pagination_inner {
    @include m.flex(center, center);
    gap: 1rem;
    .prev_btn_wrapper,
    .next_btn_wrapper {
      @include m.flex(center, center);

      .c_button_icon {
        width: 18px;
        height: 18px;
      }

      .c_button_icon:hover {
        background-color: transparent !important;
      }
    }

    .c_pagination_num_list {
      @include m.flex(center, center);
      gap: 0.25rem;
      .page_num {
        @include m.inline_flex(center, center);
        padding: 0;
        min-width: 1.5rem;
        min-height: 1.5rem;

        &.current {
          color: var(--modal_bg);
          background-color: var(--font_default);
        }

        @include m.bp_large() {
          font-size: 0.875rem;
          min-width: 1.25rem;
          min-height: 1.25rem;
        }

        @include m.bp_medium() {
          font-size: 0.75rem;
          min-width: 1rem;
          min-height: 1rem;
        }
      }
    }
  }
}
