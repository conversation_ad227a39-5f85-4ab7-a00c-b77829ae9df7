import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import Loading from '@components/common/Loading';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Title from '@components/common/Title/Title';
import { useConfirm } from '@hooks/useConfirm';
import AuthDetail from '@page/User/Role/Components/AuthDetail';
import AuthList from '@page/User/Role/Components/AuthList';
import MenuAuthList from '@page/User/Role/Components/MenuAuthList';
import ServiceAuthList from '@page/User/Role/Components/ServiceAuthList';
import { useUserRoleAPI } from '@page/User/Role/hooks/useUserRoleAPI';
import { useAuthStore } from '@page/User/Role/store/useAuthStore';
import { DetailFormValues, SearchFormValues } from '@page/User/Role/type';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useLoadingStore } from '@store/useLoadingStore';

const UserRolePage = () => {
  const { isLoading } = useLoadingStore();
  const { detailState, setDetailState, menuListData, serviceListData, authData, setAuthData } = useAuthStore();
  const { alertState } = useAlertStore();
  const { confirmState } = useConfirmStore();
  const { openConfirm } = useConfirm();
  // 검색 폼 관리
  const searchMethods = useForm<SearchFormValues>({
    defaultValues: {
      name: '', // 권한명 검색어 초기값
    },
  });

  // 상세 폼 관리
  const detailMethods = useForm<DetailFormValues>({
    defaultValues: {
      name: '', // 권한명 초기값
      menuIds: [], // 메뉴 아이디 초기값
      serviceIds: [], // 서비스 아이디 초기값
    },
  });

  const { reset } = detailMethods;
  const { getAuthList, searchAuth, getMenuAndServiceData, getAuthInfoData, addAuth, updateAuth, deleteAuth } =
    useUserRoleAPI({ searchMethods, detailMethods });

  // 권한 목록 조회
  useEffect(() => {
    setDetailState(null);
    setAuthData(null);
    getAuthList();
    getMenuAndServiceData();
  }, []);

  // Function
  const handleAuthInfo = (id: number) => {
    getAuthInfoData(id);
    setDetailState('edit');
  };

  const handleOnClickAddAuth = () => {
    setDetailState('add');
    reset({
      name: '',
      menuIds: [],
      serviceIds: [],
    });
  };

  const handleSubmit = (data: DetailFormValues) => {
    if (detailState === 'add') {
      addAuth(data);
    } else {
      if (authData) {
        updateAuth(authData.id, data);
      }
    }
  };

  const handleOnClickDeleteAuth = (e: React.MouseEvent, id: number) => {
    e.stopPropagation(); // 이벤트 버블링 방지
    openConfirm(
      '삭제하시겠습니까?',
      () => {
        deleteAuth(id);
      },
      'delete'
    );
  };

  return (
    <>
      <div className="manage_admin_authority">
        <h1 className="c_title">
          <p className="c_title_text">사용자 권한 관리</p>
        </h1>
        {/* 검색 폼 */}
        <Form onSubmit={searchAuth} methods={searchMethods}>
          <ControlBox>
            <FormInput name="name" placeholder="권한명" />
            <Button type="submit" text="조회" />
          </ControlBox>
        </Form>
        <Form className="content horizontal" onSubmit={handleSubmit} methods={detailMethods}>
          <div className="left_content vertical">
            <TableContainerHeader
              leftChildren={
                <h1 className="c_title">
                  <p className="c_title_text">권한리스트</p>
                </h1>
              }
              rightChildren={<Button text="추가" onClick={handleOnClickAddAuth} />}
            />
            {/* 권한 리스트 */}
            <AuthList handleAuthInfo={handleAuthInfo} handleOnClickDeleteAuth={handleOnClickDeleteAuth} />
          </div>
          <div className="right_content vertical">
            <TableContainerHeader
              leftChildren={
                <h1 className="c_title">
                  <p className="c_title_text">권한정보</p>
                </h1>
              }
              rightChildren={
                detailState && (
                  <Button
                    type="submit"
                    text={`권한 및 정보 ${detailState === 'add' ? '추가' : '수정'}`}
                    onClick={() => {}}
                  />
                )
              }
            />
            {/* 권한 상세 */}
            <AuthDetail />
            {detailState && (
              <div className="authority_list horizontal">
                <div className="menu_authority vertical">
                  <h1 className="c_title">
                    <p className="c_title_text">메뉴권한</p>
                  </h1>
                  <MenuAuthList menuListData={menuListData} />
                </div>
                <div className="service_authority vertical">
                  <h1 className="c_title">
                    <p className="c_title_text">서비스 권한</p>
                  </h1>
                  <ServiceAuthList serviceListData={serviceListData} />
                </div>
              </div>
            )}
          </div>
        </Form>
        <Loading isLoading={isLoading} />
      </div>
      <Alert isOpenAlert={alertState.isOpen} children={alertState.content} onConfirm={alertState.onConfirm} />
      <Confirm
        isOpenConfirm={confirmState.isOpen}
        title="알림"
        children={confirmState.content}
        onLeftButton={confirmState.onCancel}
        onRightButton={confirmState.onConfirm}
        rightButtonText={confirmState.confirmType === 'delete' ? '삭제' : '확인'}
        isDelete={confirmState.confirmType === 'delete'}
      />
    </>
  );
};

export default UserRolePage;
