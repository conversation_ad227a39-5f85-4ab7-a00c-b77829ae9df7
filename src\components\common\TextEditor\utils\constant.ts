export const EditorConstant = {
  makeFontSizeOptions: (defaultFontSize: number = 16, maxFontSize: number = 30) => {
    return Array.from({ length: maxFontSize }, (_, idx) => {
      const fontSize = idx * 2 + 8;
      const label = fontSize === defaultFontSize ? 'Default' : `${fontSize}px`;
      const value = fontSize === defaultFontSize ? 'default' : `${fontSize}px`;
      return {
        label,
        value,
      };
    });
  },

  FONT_FAMILIES: [
    { label: '맑은 고딕', value: 'Malgun Gothic' },
    { label: '굴림', value: 'Guli<PERSON>' },
    { label: '돋움', value: 'Dotum' },
    { label: '바탕', value: 'Batang' },
    { label: 'Arial', value: 'Arial' },
    { label: 'Times New Roman', value: 'Times New Roman' },
    { label: 'Courier New', value: 'Courier New' },
  ],

  TEXT_STYLES: ['bold', 'italic', 'underline', 'strike', 'format_clear'],
};
