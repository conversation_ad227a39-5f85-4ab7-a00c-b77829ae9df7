import { AdminListItem, PaginationData } from '@page/Admin/Account/type';
import { create } from 'zustand';

interface AdminAccountListStore {
  // 현재 페이지
  currentPage: number;
  setCurrentPage: (page: number) => void;

  // 페이지 사이즈
  pageSize: number;
  setPageSize: (size: number) => void;

  // 관리자 계정 목록
  adminListData: AdminListItem[];
  setAdminListData: (data: AdminListItem[]) => void;

  // 페이지네이션 데이터
  paginationData: PaginationData;
  setPaginationData: (data: PaginationData) => void;
}

export const useAdminAccountListStore = create<AdminAccountListStore>((set) => ({
  currentPage: 0,
  setCurrentPage: (page) => set((state) => ({ currentPage: page })),

  pageSize: 20,
  setPageSize: (size) => set((state) => ({ pageSize: size })),

  adminListData: [],
  setAdminListData: (data) => set((state) => ({ adminListData: data })),

  paginationData: null,
  setPaginationData: (data) => set((state) => ({ paginationData: data })),
}));
