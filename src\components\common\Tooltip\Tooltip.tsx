import 'react-tooltip/dist/react-tooltip.css';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { ReactNode } from 'react';
import { useThemeStore } from '@store/useThemeStore';
import joinClassName from '@utils/joinClassName';
import { TooltipProps } from './types';

/**
 *자세한 옵션 설명은 {@link TooltipProps} 참고
 */

const { themeState } = useThemeStore.getState();

const renderTooltipContent = (id?: string, children?: ReactNode) => {
  if (!children) {
    //툴팁아이콘
    return (
      <div
        data-tooltip-id={id}
        className={joinClassName('c_tooltip', `theme_${themeState}`, id ? 'is-hoverable' : '')}
      ></div>
    );
  }
  // children
  return <span data-tooltip-id={id}>{children}</span>;
};

const setVariant = (variant) => {
  if (variant) return variant;
  return themeState === 'light' ? 'dark' : 'light';
};

const Tooltip = ({ id, children, variant, ...props }: TooltipProps) => {
  return (
    <>
      {renderTooltipContent(id, children)}
      <ReactTooltip id={id} variant={setVariant(variant)} {...props} />
    </>
  );
};

export default Tooltip;
