import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

// [관리자] 게시판 관리
const API_URL = '/api-admin/bbs-templets';

// 게시판 목록 조회
interface GetBoardListParams {
  templetTypeCd?: string;
  templetName?: string;
  useYn?: 'Y' | 'N' | '전체';
  page: number;
  size: number;
  isGetErrorMsg?: boolean;
}

export const getBoardList = async ({
  templetTypeCd,
  templetName,
  useYn,
  page = 1,
  size = 10,
  isGetErrorMsg = false,
}: GetBoardListParams) => {
  try {
    const isValid = (value) => value && value.trim() !== '';

    const params = [
      isValid(templetTypeCd) && templetTypeCd !== '전체' && `templetTypeCd=${templetTypeCd}`,
      isValid(templetName) && `templetNm=${templetName}`,
      isValid(useYn) && useYn !== '전체' && `useYn=${useYn}`,
    ]
      .filter(Boolean)
      .join('&');

    const apiUrl = `${API_URL}?page=${page}&size=${size}${`&${params}`}`;
    const response = await api.get(apiUrl);

    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 목록 조회');
  }
};

// 게시판 상세 조회
interface GetBoardDetailParams {
  id: string;
  isGetErrorMsg?: boolean;
}

export const getBoardDetail = async ({ id, isGetErrorMsg = false }: GetBoardDetailParams) => {
  try {
    const apiUrl = `${API_URL}/${id}`;
    const response = await api.get(apiUrl);

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 상세 조회');
  }
};

// 게시판 등록
interface PostBoardParams {
  templetTypeCd: string;
  templetNm: string;
  cateGrpCd1: string;
  cateGrpCd2: string;
  additionInfoDesc: string;
  fileUseYn: 'Y' | 'N' | 'none';
  replyUseYn: 'Y' | 'N' | 'none';
  useYn: 'Y' | 'N' | 'none';
  isGetErrorMsg?: boolean;
}

export const postBoard = async ({
  templetTypeCd,
  templetNm,
  cateGrpCd1,
  cateGrpCd2,
  additionInfoDesc,
  fileUseYn,
  replyUseYn,
  useYn,
  isGetErrorMsg = false,
}: PostBoardParams) => {
  try {
    const apiUrl = `${API_URL}/add`;
    const response = await api.post(apiUrl, {
      templetTypeCd,
      templetNm,
      cateGrpCd1,
      cateGrpCd2,
      additionInfoDesc,
      fileUseYn,
      replyUseYn,
      useYn,
    });

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 등록');
  }
};

// 게시판 수정
interface PutBoardParams {
  id: string;
  templetNm: string;
  cateGrpCd1: string;
  cateGrpCd2: string;
  additionInfoDesc: string;
  fileUseYn: 'Y' | 'N' | 'none';
  replyUseYn: 'Y' | 'N' | 'none';
  useYn: 'Y' | 'N' | 'none';
  isGetErrorMsg?: boolean;
}

export const putBoard = async ({
  id,
  templetNm,
  cateGrpCd1,
  cateGrpCd2,
  additionInfoDesc,
  fileUseYn,
  replyUseYn,
  useYn,
  isGetErrorMsg = false,
}: PutBoardParams) => {
  try {
    const apiUrl = `${API_URL}/${id}`;
    const response = await api.post(apiUrl, {
      templetNm,
      cateGrpCd1,
      cateGrpCd2,
      additionInfoDesc,
      fileUseYn,
      replyUseYn,
      useYn,
    });

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 수정');
  }
};

// 게시판 삭제
interface DeleteBoardParams {
  ids: string[];
  isGetErrorMsg?: boolean;
}

export const deleteBoard = async ({ ids, isGetErrorMsg = false }: DeleteBoardParams) => {
  try {
    const apiUrl = `${API_URL}/delete`;
    const response = await api.post(apiUrl, { ids });

    return response.data.code;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 삭제');
  }
};

// 게시판 템플릿 카테고리 목록 조회
interface GetBoardCategoryListParams {
  cateSeq: number;
  isGetErrorMsg?: boolean;
}

export const getBoardCategoryList = async ({ cateSeq, isGetErrorMsg = false }: GetBoardCategoryListParams) => {
  try {

    // const apiUrl = `/api-admin/templet-categories` + (cateSeq != null) ?  `?cateSeq=${cateSeq}` : '';
    const apiUrl = `/api-admin/templet-categories`
    console.log("## getBoardCategoryList START");
    console.log("## apiUrl : " + apiUrl);
    const response = await api.get(apiUrl, {params: {cateSeq: cateSeq}});

    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 템플릿 카테고리 목록 조회');
  }
};
