export const getUserAgentInfo = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    let browser = "Unknown";
    let browserVersion = "Unknown";
    let osName = "Unknown";
    // let osVersion = "Unknown";
    let deviceType = getDeviceType(userAgent)
    // let deviceType = /Mobi|Android|iPhone|iPad/i.test(userAgent) ? "Mobile" : "PC";

    // 브라우저
    if (userAgent.includes("edg/")) { // 🎯 Edge 먼저 체크 (Chrome과 혼동 방지)
        browser = "Edge";
        browserVersion = userAgent.match(/edg\/([\d.]+)/)?.[1] || "Unknown";
    } 
    else if (userAgent.includes("opr/") || userAgent.includes("opera/")) { // 🎯 Opera 감지
        browser = "Opera";
        browserVersion = userAgent.match(/(?:opr|opera)\/([\d.]+)/)?.[1] || "Unknown";
    } 
    else if (userAgent.includes("chrome/")) { // 🎯 Chrome 감지
        browser = "Chrome";
        browserVersion = userAgent.match(/chrome\/([\d.]+)/)?.[1] || "Unknown";
    } 
    else if (userAgent.includes("safari/") && !userAgent.includes("chrome")) { // 🎯 Safari 감지
        browser = "Safari";
        browserVersion = userAgent.match(/version\/([\d.]+)/)?.[1] || "Unknown";
    } 
    else if (userAgent.includes("firefox/")) { // 🎯 Firefox 감지
        browser = "Firefox";
        browserVersion = userAgent.match(/firefox\/([\d.]+)/)?.[1] || "Unknown";
    } 
    else if (userAgent.includes("trident/") || userAgent.includes("msie")) { // 🎯 IE 감지
        browser = "IE";
        browserVersion = userAgent.match(/(?:msie |rv:)([\d.]+)/)?.[1] || "Unknown";
    }

    // OS
    if (/windows/i.test(userAgent)) {
        osName = "Windows";
        // osVersion = userAgent.match(/Windows NT ([\d.]+)/)?.[1] || "Unknown";
    } else if (/mac os/i.test(userAgent)) {
        osName = "macOS";
        // osVersion = userAgent.match(/Mac OS X ([\d_]+)/)?.[1]?.replace(/_/g, ".") || "Unknown";
    } else if (/android/i.test(userAgent)) {
        osName = "Android";
        // osVersion = userAgent.match(/Android ([\d.]+)/)?.[1] || "Unknown";
    } else if (/iphone|ipad|ipod/i.test(userAgent)) {
        osName = "iOS";
        // osVersion = userAgent.match(/OS ([\d_]+)/)?.[1]?.replace(/_/g, ".") || "Unknown";
    }


    return { browser, browserVersion, deviceType, osName };
}

const getDeviceType = (userAgent) => {
    if (/mobile/i.test(userAgent)) {
      return "Mobile";  // 모바일
    } else if (/tablet/i.test(userAgent)) {
      return "Tablet";  // 태블릿
    } else {
      return "PC";  // PC
    }
};  