import { useLayoutStore } from '@store/useLayoutStore';
import joinClassName from '@utils/joinClassName';
import GNB from '@components/layout/Navigation/GNB';

const Header = ({ className }: { className?: string }) => {
  const { lnbState } = useLayoutStore();
  return (
    <header className={joinClassName('c_header', className, lnbState && 'expand')}>
      <GNB />
    </header>
  );
};

export default Header;
