import { useState } from 'react';

import Title from '@components/common/Title/Title';
import CategoryList from '@page/Admin/Menu/Components/CategoryList';
import MenuDetail from '@page/Admin/Menu/Components/MenuDetail';

const AdminMenuPage = () => {
  const [clickedMenuId, setClickedMenuId] = useState(0);

  return (
    <div className="manage_admin_menu">
      <div className="manage_admin_contents">
        <div className="manage_admin_contents_left">
          <CategoryList handleClickedMenuId={setClickedMenuId} clickedMenuId={clickedMenuId} />
        </div>
        <div className="manage_admin_contents_right">
          <MenuDetail categoryId={clickedMenuId} setCategoryId={setClickedMenuId} />
        </div>
      </div>
    </div>
  );
};

export default AdminMenuPage;
