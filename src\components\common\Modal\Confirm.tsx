import React, { Fragment, useEffect, useRef, useState } from 'react';

import joinClassName from '@utils/joinClassName';
import { useConfirmStore } from '@store/useConfirmStore';
import ModalPortal from '@components/common/Modal/ModalPortal';
import IconButton from '@components/common/Button/IconButton';
import Button from '@components/common/Button/Button';
import { useLoadingStore } from '@store/useLoadingStore';

interface AlertProps {
  isOpenConfirm: boolean;
  title?: string;
  children: string | React.ReactNode;
  leftButtonText?: string;
  rightButtonText?: string;
  rightButtonType?: 'button' | 'submit' | 'reset';
  onLeftButton: (props?: any) => void;
  onRightButton: (props?: any) => void;
  className?: string;
  onClickedDim?: (props?: any) => void; // dim 클릭했을 때 실행 함수
  transparentDim?: boolean; // dim이 투명한 경우
  isDelete?: boolean; // 삭제 버튼 여부
}

const Confirm = ({
  isOpenConfirm = false, // confirm의 open/close 여부를 관리하는 상태값(*)
  title, // 제목
  children, // 내부 text(*)
  leftButtonText = '취소', // left button text
  rightButtonText = '확인', // right button text
  rightButtonType = 'button', // right button type
  onLeftButton, // left button 실행 함수 (*)
  onRightButton, // right button 실행 함수 (*)
  onClickedDim, // dim 클릭 시 실행 함수 (기본동작: 이벤트 없음)
  transparentDim, // dim 투명 설정(true: 투명 dim / false(기본): 불투명 black dim)
  className, //. 추가 클래스 요소
  isDelete, // 삭제 버튼 여부
}: AlertProps) => {
  const confirmClass = joinClassName('c_modal', 'confirm', className);
  const { initConfirmState } = useConfirmStore();
  const { isLoading } = useLoadingStore();

  const confirmRef = useRef<HTMLDivElement>(null);
  const [position, setPosition] = useState({
    top: null,
    left: null,
  });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  useEffect(() => {
    if (confirmRef.current) {
      if (!position.top || !position.left || isOpenConfirm) {
        const modalRef = confirmRef.current;
        const { innerWidth, innerHeight } = window;
        const center = {
          x: innerWidth * 0.5 - modalRef.offsetWidth * 0.5,
          y: innerHeight * 0.5 - modalRef.offsetHeight * 0.5,
        };
        setPosition({ top: `${center.y}px`, left: `${center.x}px` });
      }
    }
  }, [isOpenConfirm]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target instanceof Element && e.target.closest('.c_modal_header')) {
      setIsDragging(true);

      const rect = confirmRef.current.getBoundingClientRect();
      const offsetX = e.clientX - rect.left;
      const offsetY = e.clientY - rect.top;

      setDragOffset({ x: offsetX, y: offsetY });

      e.preventDefault();
    }
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      const left = e.clientX - dragOffset.x;
      const top = e.clientY - dragOffset.y;

      setPosition({
        top: `${top}px`,
        left: `${left}px`,
      });

      e.preventDefault();
    }
  };

  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isDragging) {
      setIsDragging(false);
      e.preventDefault();
    }
  };

  useEffect(() => {
    const handleGlobalMouseUp = () => {
      if (isDragging) {
        setIsDragging(false);
      }
    };

    window.addEventListener('mouseup', handleGlobalMouseUp);

    return () => {
      window.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [isDragging]);

  const parseChildren = () => {
    if (typeof children === 'string') {
      return children.split(/\n|<br\s*\/?>|<br>/).map((line, idx) => (
        <Fragment key={idx}>
          {line}
          {idx < children.split(/\n|<br\s*\/?>|<br>/).length - 1 && <br />}
        </Fragment>
      ));
    }
    return children;
  };

  return (
    <>
      {isOpenConfirm && (
        <ModalPortal onClickedDim={onClickedDim} transparentDim={transparentDim}>
          <div
            className={confirmClass}
            style={position}
            ref={confirmRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
          >
            <div className="c_modal_header">
              <h2 className="c_modal_title">{title}</h2>
              <IconButton
                iconOnly
                text="닫기"
                icon="close"
                color="grayscale"
                fill="unfilled"
                size="smallest"
                design="circle"
                onClick={initConfirmState}
              />
            </div>
            <div className="c_modal_body">{parseChildren()}</div>
            <div className="c_modal_footer">
              <Button color="grayscale" text={leftButtonText} onClick={onLeftButton} disabled={isLoading} />
              <Button
                type={rightButtonType}
                color={!isDelete ? 'primary' : 'red'}
                text={rightButtonText}
                onClick={onRightButton}
                disabled={isLoading}
              />
            </div>
          </div>
        </ModalPortal>
      )}
    </>
  );
};

export default Confirm;
