import IconButton from '@components/common/Button/IconButton';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Button from '@components/common/Button/Button';
import api from '@api/api';
import { IconType } from '@components/common/Button/types';
import joinClassName from '@utils/joinClassName';
import { getMyInfo } from '@api/admin/adminUsersApi';
import { useMyInfoStore } from '@page/MyInfo/store/useMyInfoStore';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useSessionTimeoutStore } from '@store/useSessionTimeoutStore';
import { useLayoutStore } from '@store/useLayoutStore';
import { useLoginStore } from '@store/useLoginStore';

const GNB = () => {
  const { name, setMyInfo } = useMyInfoStore();
  const { lnbState, setLnbState } = useLayoutStore();
  const { setAlertState } = useAlertStore();
  const { setConfirmState } = useConfirmStore();
  const { setIsLogin } = useLoginStore();

  const { sessionTimeout } = useSessionTimeoutStore();
  const [seconds, setSeconds] = useState(sessionTimeout);
  const profileBtnRef = useRef<HTMLDivElement>(null);
  const profileModalRef = useRef<HTMLDivElement>(null);
  const [isProfileActive, setIsProfileActive] = useState(false);
  const [optionPosition, setOptionPosition] = useState({ top: 0, left: 0 });
  // const [searchVal, setSearchVal] = useState('');

  // 내 정보 불러오기
  const getMyInfoData = async () => {
    const response = await getMyInfo();
    if (response) {
      setMyInfo({
        id: response.id,
        name: response.name,
        adminId: response.adminId,
        activeYn: response.activeYn,
        roleIds: response.roles.map((roles) => roles.id),
      });
    }
  };

  useEffect(() => {
    getMyInfoData();
  }, []);

  // 세션 만료 시간
  useEffect(() => {
    setSeconds(sessionTimeout);
  }, [sessionTimeout]);

  useEffect(() => {
    if (seconds <= 0) return; // 0초 이하일 경우 타이머 종료

    const interval = setInterval(() => {
      setSeconds((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval); // 언마운트 시 인터벌 정리
  }, [seconds]);

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}분 ${remainingSeconds}초`;
  };

  const handleToggleLnbState = () => {
    setLnbState(!lnbState);
  };

  const handleClickLogout = () => {
    setConfirmState({
      isOpen: true,
      confirmType: null,
      title: '로그아웃',
      content: '로그아웃 하시겠습니까?',
      onCancel: () => {
        setConfirmState((prev) => ({ ...prev, isOpen: false }));
      },
      onConfirm: () => {
        setConfirmState((prev) => ({ ...prev, isOpen: false }));
        handleLogout();
      },
    });
  };

  const handleLogout = async () => {
    // const response = await api.post(
    //   '/auth/logoutSession,
    //   {},
    //   {
    //     withCredentials: true,
    //   }
    // ); // SESSION 쿠키 자동 포함

    const response = await api.post('/auth/logout');

    if (response.status === 200) {
      localStorage.removeItem('accessToken');
      setIsLogin(false);
      window.location.href = '/login';
    } else {
      setAlertState({
        isOpen: true,
        title: '로그아웃 실패',
        content: '로그아웃에 실패하였습니다',
        onConfirm: () => {
          setAlertState({
            isOpen: false,
            content: '',
            onConfirm: () => {},
          });
        },
      });
    }
  };

  const handleProfileClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setIsProfileActive(!isProfileActive);
  };

  const updateProfilePosition = useCallback(() => {
    let profileRect;
    profileRect = profileBtnRef.current?.getBoundingClientRect();
    setOptionPosition({ top: profileRect.bottom + 5, left: profileRect.left - 170 });
  }, []);

  useEffect(() => {
    updateProfilePosition();
    window.addEventListener('resize', updateProfilePosition);
    window.addEventListener('scroll', updateProfilePosition);

    return () => {
      window.removeEventListener('resize', updateProfilePosition);
      window.removeEventListener('scroll', updateProfilePosition);
    };
  }, [updateProfilePosition]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isProfileActive &&
        profileModalRef.current &&
        !profileModalRef.current.contains(event.target as Node) &&
        profileBtnRef.current &&
        !profileBtnRef.current.contains(event.target as Node)
      ) {
        setIsProfileActive(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isProfileActive]);

  return (
    <>
      <div className="c_gnb">
        <div className="left">
          <div className={`mbtn ${lnbState ? 'active' : ''}`} onClick={handleToggleLnbState}>
            <i className="bar_1"></i>
            <i className="bar_2"></i>
            <i className="bar_3"></i>
          </div>
        </div>
        <div className="right">
          <div className="timeout">
            <p>접속 유지 시간</p>
            <p>{formatTime(seconds)}</p>
          </div>
          <div className="my_info_name">{name} 님</div>
          <div className="button_wrapper" ref={profileBtnRef}>
            <IconButton
              text="profile"
              icon={'profile' as IconType}
              iconOnly
              design="circle"
              color="grayscale"
              fill="unfilled"
              clickLog={{ buttonSection: '프로필' }}
              onClick={handleProfileClick}
            />
          </div>
        </div>
      </div>
      <div
        className={joinClassName('c_profile_modal', !isProfileActive ? 'hidden' : '')}
        style={{
          top: optionPosition.top,
          left: optionPosition.left,
        }}
        ref={profileModalRef}
      >
        <h3 className="c_profile_modal_title">프로필</h3>
        <ul className="c_profile_modal_content">
          <li className="c_profile_modal_content_item">
            <Link to={'/myInfo'}>
              <p>프로필 수정</p>
            </Link>
          </li>
          <li className="c_profile_modal_content_item">
            <Button text="로그아웃" onClick={handleClickLogout} clickLog={{ buttonSection: '프로필' }} />
          </li>
        </ul>
      </div>
    </>
  );
};

export default GNB;
