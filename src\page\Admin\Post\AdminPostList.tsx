import { useCallback, useEffect, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';

import { ColDef, ColGroupDef } from '@ag-grid-community/core';
import { AgGridReact } from '@ag-grid-community/react';

import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import SelectBox from '@components/common/SelectBox/SelectBox';
import { AdminPostFormValue, AdminPostSearchOptions } from '@page/Admin/Post/type';
import { deleteMultiPost, getAllPost, getPostBoardList } from '@api/admin/postApi';
import { calcNoSort } from '@utils/calcNoSort';
import { useNavigate } from 'react-router-dom';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import Title from '@components/common/Title/Title';

const AdminPostList = () => {
  const pageSize: AdminPostSearchOptions['pageSize'] = [
    { label: '10', value: 10 },
    { label: '20', value: 20 },
    { label: '30', value: 30 },
    { label: '40', value: 40 },
    { label: '50', value: 50 },
  ];

  const [boardList, setBoardList] = useState<AdminPostSearchOptions['boardList']>([{ label: '전체', value: 0 }]);

  // ------------------------------------------------------------------
  const [selectedPageSize, setSelectedPageSize] = useState<{ label: string; value: number }>(pageSize[0]);
  const [page, setPage] = useState<number>(0);
  const [totalDataCnt, setTotalDataCnt] = useState<number>(0);
  const gridRef = useRef<AgGridReact>(null);
  const [column] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: 'seq', headerName: 'No.', width: 130 },
    { field: 'templetNm', headerName: '게시판명' },
    { field: 'templetTypeNm', headerName: '게시판 유형' },
    { field: 'title', headerName: '제목' },
    { field: 'delYn', headerName: '삭제유무' },
    { field: 'dateInfo.createUser', headerName: '등록자' },
    { field: 'dateInfo.createDate', headerName: '등록시간' },
    { field: 'dateInfo.updateUser', headerName: '수정자' },
    { field: 'dateInfo.updateDate', headerName: '수정시간' },
  ]);
  const [gridData, setGridData] = useState([]);
  const [selectedGridData, setSelectedGridData] = useState([]);
  const navigate = useNavigate();
  const { setAlertState, initAlertState } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();

  const formMethods = useForm<AdminPostFormValue>({
    defaultValues: {
      board: boardList[0],
      postTitle: '',
    },
  });

  const { getValues } = formMethods;

  const getBoardList = async () => {
    const response = await getPostBoardList({});

    setBoardList([
      { label: '전체', value: 0 },
      ...response?.data?.map((board) => {
        return { label: board.templetNm, value: board.id };
      }),
    ]);
  };

  const getAllPostListData = async ({ templetId, title, page, size }) => {
    const response = await getAllPost({ templetId, title, page, size });

    setTotalDataCnt(response.pageInfo.totalCount);
    setGridData(
      response.data.map((data, index) => {
        return {
          seq: calcNoSort({
            totalCount: response.pageInfo.totalCount,
            currentPage: page,
            pageSize: selectedPageSize.value,
            index,
          }),
          ...data,
        };
      })
    );
  };

  // 조회
  useEffect(() => {
    getBoardList();
  }, []);

  useEffect(() => {
    setPage(0); // page size 변화 시 1페이지로 이동
    getAllPostListData({
      templetId: getValues('board').value,
      title: getValues('postTitle'),
      page: 0,
      size: selectedPageSize.value,
    });
  }, [selectedPageSize]);

  // 검색
  const handleSearchSubmit = (data: AdminPostFormValue) => {
    const { board, postTitle } = data;

    getAllPostListData({
      templetId: board.value,
      title: postTitle,
      page: 0,
      size: selectedPageSize.value,
    });
  };

  // 페이지 변경
  const handleChangePage = (e) => {
    getAllPostListData({
      templetId: getValues('board').value,
      title: getValues('postTitle'),
      page: e - 1,
      size: selectedPageSize.value,
    });

    setPage(e - 1);
  };

  // row 선택
  const handleRowSelected = useCallback(() => {
    const selectedNodes = gridRef.current?.api.getSelectedNodes();
    const selectedData = selectedNodes?.map((node) => node.data) || [];

    setSelectedGridData(selectedData);
  }, []);

  // 상세 페이지 이동
  const handlePostDetail = (event) => {
    const postId = event.data.id;

    navigate(`/admin/post/detail/${postId}`);
  };

  // 삭제
  const handleDeletePost = async () => {
    initConfirmState();

    const response = await deleteMultiPost({ ids: selectedGridData.map((data) => data.id) });

    if (response === 'DELETE') {
      setAlertState({
        isOpen: true,
        content: '게시글이 삭제되었습니다',
        onConfirm: () => {
          setPage(0);
          getAllPostListData({
            templetId: getValues('board').value,
            title: getValues('postTitle'),
            page: 0,
            size: selectedPageSize.value,
          });
          initAlertState();
        },
      });
    }
  };

  const handleClickDeleteBtn = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: handleDeletePost,
    });
  };

  return (
    <div className="admin_post">
      <Form onSubmit={handleSearchSubmit} methods={formMethods}>
        <ControlBox>
          <FormSelectBox name="board" label="게시판" options={boardList} />
          <FormInput name="postTitle" label="게시글 제목" />
          <Button text="조회" type="submit" />
        </ControlBox>
      </Form>
      <div className="admin_post_contents">
        <div className="admin_post_contents_control">
          <div className="admin_post_contents_control_cnt">
            <p>총 {totalDataCnt}건</p>
            <SelectBox
              options={pageSize}
              defaultValue={selectedPageSize}
              selectedValue={selectedPageSize}
              setSelectedValue={setSelectedPageSize}
            />
          </div>
          <div className="admin_post_contents_control_btn">
            <Button text="삭제" color="red" disabled={selectedGridData.length === 0} onClick={handleClickDeleteBtn} />
            <Button text="추가" onClick={() => navigate('/admin/post/add')} />
          </div>
        </div>
        <Grid
          ref={gridRef}
          columns={column}
          rowData={gridData}
          autoSizeStrategy={'onGridSizeChanged'}
          defaultColDef={{
            onCellClicked: handlePostDetail,
          }}
          gridOptions={{
            onRowSelected: handleRowSelected,
            rowSelection: {
              checkboxes: true,
              headerCheckbox: true,
              mode: 'multiRow',
            },
          }}
        />
        <Pagination
          pageCount={10}
          totalCount={totalDataCnt}
          currentPage={page + 1}
          itemCountPerPage={selectedPageSize.value}
          onPageChange={(e) => handleChangePage(e)}
        />
      </div>
    </div>
  );
};

export default AdminPostList;
