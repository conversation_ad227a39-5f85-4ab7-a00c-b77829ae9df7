import { useForm } from 'react-hook-form';
import ControlBox from '@components/common/ControlBox/ControlBox';
import FormInput from '@components/common/Form/FormInput';
import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import { useEffect, useState } from 'react';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import { deleteFiles, getFileList } from '@api/admin/fileAPI';
import { addCalcNoSort } from '@utils/calcNoSort';
import SelectBox from '@components/common/SelectBox/SelectBox';
import { pageSizeOptions } from '@store/constant';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';

const ManageFilePage = () => {
  const [selectedRow, setSelectedRow] = useState<string[]>([]);
  const [fileListData, setFileListData] = useState<any[]>([]);
  const [paginationData, setPaginationData] = useState<any>({
    currentPage: 0,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
  });

  const { activeAlert } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();

  // 검색 폼 관리
  const methods = useForm({
    defaultValues: {
      tableName: { label: '전체', value: '' }, // 테이블 타입
      fileName: '', // 파일명
    },
  });

  const { getValues } = methods;

  // 테이블 헤더 정의
  const colDef = [
    { headerName: 'NO', field: 'sort' },
    { headerName: '테이블 ID', field: 'targetId' },
    { headerName: '테이블명', field: 'tableName' },
    { headerName: '원본파일명', field: 'originName' },
    { headerName: '파일명', field: 'name' },
    { headerName: 'ID', field: 'id' },
    { headerName: '전체경로', field: 'fullPath' },
    { headerName: '경로', field: 'path' },
    { headerName: '확장자', field: 'extension' },
    { headerName: '사이즈 (단위:Byte)', field: 'size' },
    { headerName: '등록자', field: 'dateInfo.createUser' },
    { headerName: '등록일시', field: 'dateInfo.createDate' },
    { headerName: '수정자', field: 'dateInfo.updateUser' },
    { headerName: '수정일시', field: 'dateInfo.updateDate' },
  ];

  const getFileListData = async () => {
    const formData = {
      page: paginationData.currentPage,
      size: paginationData.pageSize,
      tableName: getValues('tableName').value,
      fileName: getValues('fileName'),
    };
    const { data, pageInfo } = await getFileList({ formData });
    const parsedData = addCalcNoSort({ data, pageInfo });
    setFileListData(parsedData);
    setPaginationData(pageInfo);
  };

  // 페이지 사이즈 변경
  const handlePageSizeChange = (page) => {
    setPaginationData({ ...paginationData, pageSize: parseInt(page.value) });
  };

  // 페이지 변경
  const handlePageChange = (page: number) => {
    setPaginationData({ ...paginationData, currentPage: page - 1 });
  };

  const handleSubmit = () => {
    getFileListData();
  };

  const handleDelete = async () => {
    const respMessage = await deleteFiles({ ids: [...selectedRow] });
    if (respMessage) {
      getFileListData();
      activeAlert(respMessage);
    }
  };

  const onRowSelected = (event: any) => {
    setSelectedRow((prev) => {
      if (prev.includes(event.data.id)) {
        return prev.filter((id) => id !== event.data.id);
      }
      return [...prev, event.data.id];
    });
  };

  useEffect(() => {
    getFileListData();
  }, [paginationData.currentPage, paginationData.pageSize]);

  return (
    <div className="manage_admin_file">
      <Form onSubmit={handleSubmit} methods={methods}>
        <ControlBox>
          {/* <FormSelectBox name="tableName" options={tableNames} placeholder="테이블 종류" /> */}
          <label htmlFor="fileName">파일 이름</label>
          <FormInput name="fileName" placeholder="파일명" />
          <Button type="submit" text="조회" />
        </ControlBox>

        <TableContainerHeader
          leftChildren={
            <>
              <span>총 {paginationData?.totalCount}건</span>
              <SelectBox
                selectedValue={{ label: paginationData.pageSize.toString(), value: paginationData.pageSize.toString() }}
                defaultValue={{ label: paginationData.pageSize.toString(), value: paginationData.pageSize.toString() }}
                setSelectedValue={handlePageSizeChange}
                options={pageSizeOptions}
              />
            </>
          }
          rightChildren={
            <Button
              text="삭제"
              color="red"
              onClick={() => {
                if (selectedRow.length === 0) {
                  activeAlert('파일이 선택되지 않았습니다. \n 삭제할 파일을 선택해주세요.');
                  return;
                }

                setConfirmState({
                  isOpen: true,
                  title: '알림',
                  content: (
                    <>
                      " <span className="text-red-500 font-bold">{selectedRow.length}</span> " 건의 파일을 삭제
                      하시겠습니까?
                      <br />
                      삭제 후 복구 불가능 합니다.
                    </>
                  ),
                  confirmType: 'delete',
                  onCancel: initConfirmState,
                  onConfirm: handleDelete,
                });
              }}
            />
          }
        />
        <Grid
          columns={colDef}
          rowData={fileListData}
          autoSizeStrategy={{ type: 'fitCellContents' }}
          gridOptions={{
            onRowSelected: onRowSelected,
            rowSelection: { checkboxes: true, headerCheckbox: true, mode: 'multiRow' },
          }}
        />
        {paginationData && (
          <Pagination
            totalCount={paginationData.totalCount}
            itemCountPerPage={paginationData.pageSize}
            currentPage={paginationData.currentPage + 1}
            onPageChange={handlePageChange}
          />
        )}
      </Form>
    </div>
  );
};

export default ManageFilePage;
