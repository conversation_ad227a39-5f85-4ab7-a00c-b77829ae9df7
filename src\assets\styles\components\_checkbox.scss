@use '@styles/utils/mixin' as m;

.c_checkbox_list {
  @include m.inline_flex(center);
  gap: 0.5rem;
  
  &_item {
    @include m.inline_flex(center);
  }
}

.c_checkbox {
  position: relative;
  @include m.inline_flex(center, center);
  line-height: 1.25rem;
  cursor: pointer;

  input {
    left: 0;
    opacity: 0;
    position: absolute;
    top: 0;
    height: 1.25rem;
    width: 1.25rem;
    z-index: -1;
  }
  span {
    position: relative;
    display: inline-block;
    padding-left: 1.75rem;

    &.sr-only {
      min-height: 1.25rem;
      padding-left: 1.25rem;
    }

    &::before {
      @include m.content_without_url(1.25rem, 1.25rem);
      display: inline-block;
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      border-radius: 0.25rem;
      min-width: 1.25rem;
      transition: all 0.3s;
    }
  }

  input + span {
    &::before {
      border: 1px solid var(--g_06);
      background-color: var(--g_01);
    }
  }

  input:checked + span {
    &::before {
      border: none;
      background-color: transparent;
    }
    @include m.before_url('@assets/images/icon/icon_checkbox.svg');
  }

  input:disabled + span {
    opacity: 0.5;
  }

  input:checked:disabled + span {
    @include m.before_url('@assets/images/icon/icon_checkbox_disabled.svg');
  }
}


@include m.bp_large {
  .c_checkbox {
    line-height: 1rem;
    
    input {
      height: 1rem;
      width: 1rem;
    }
    
    span {
      font-size: 0.875rem;
      padding-left: 1.5rem;

      &.sr-only {
        min-height: 1.125rem;
        padding-left: 1.125rem;
      }
  
      &::before {
        @include m.content_without_url(1.125rem, 1.125rem);
        min-width: 1.125rem;
      }
    }
  }
}


@include m.bp_medium {
  .c_checkbox {
    line-height: 0.875rem;
    
    input {
      height: 0.875rem;
      width: 0.875rem;
    }
    
    span {
      font-size: 0.75rem;
      padding-left: 1.375rem;

      &.sr-only {
        min-height: 1rem;
        padding-left: 1rem;
      }
      
      &::before {
        @include m.content_without_url(1rem, 1rem);
        min-width: 1rem;
      }
    }
  }
}
