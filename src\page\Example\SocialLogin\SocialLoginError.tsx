import { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

const SocialLoginError = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [errorInfo, setErrorInfo] = useState({
        code: 'unknown_error',
        message: '알 수 없는 오류가 발생했습니다.'
    });

    useEffect(() => {
        // URL 파라미터에서 오류 정보 추출
        const urlParams = new URLSearchParams(location.search);
        const errorCode = urlParams.get('error') || 'unknown_error';
        const errorMessage = urlParams.get('message') || '알 수 없는 오류가 발생했습니다.';

        setErrorInfo({
            code: errorCode,
            message: decodeURIComponent(errorMessage)
        });

        console.error('소셜 로그인 오류:', { errorCode, errorMessage });
    }, [location]);

    const getErrorTitle = (code) => {
        switch (code) {
            case 'access_denied':
                return '로그인 취소';
            case 'invalid_request':
                return '잘못된 요청';
            case 'server_error':
                return '서버 오류';
            case 'authentication_failed':
                return '인증 실패';
            case 'authentication_processing_error':
                return '처리 오류';
            default:
                return '로그인 오류';
        }
    };

    const getErrorDescription = (code) => {
        switch (code) {
            case 'access_denied':
                return '소셜 로그인을 취소하셨습니다. 다시 시도해주세요.';
            case 'invalid_request':
                return '요청이 올바르지 않습니다. 다시 시도해주세요.';
            case 'server_error':
                return '소셜 로그인 서버에 일시적인 문제가 발생했습니다.';
            case 'authentication_failed':
                return '소셜 로그인 인증에 실패했습니다.';
            case 'authentication_processing_error':
                return '로그인 정보 처리 중 오류가 발생했습니다.';
            default:
                return '예상치 못한 오류가 발생했습니다.';
        }
    };

    const handleRetry = () => {
        navigate('/social/login', { replace: true });
    };

    return (
        <div className="auth-error-container">
            <div className="auth-error-content">
                <div className="error-icon">⚠️</div>

                <h1>{getErrorTitle(errorInfo.code)}</h1>

                <div className="error-details">
                    <p className="error-description">
                        {getErrorDescription(errorInfo.code)}
                    </p>

                    <p className="error-message">
                        {errorInfo.message}
                    </p>

                    <div className="error-code">
                        오류 코드: {errorInfo.code}
                    </div>
                </div>

                <div className="error-actions">
                    <button
                        onClick={handleRetry}
                        className="btn btn-primary"
                    >
                        다시 로그인하기
                    </button>
                </div>
            </div>
        </div>
    );
};

export default SocialLoginError;