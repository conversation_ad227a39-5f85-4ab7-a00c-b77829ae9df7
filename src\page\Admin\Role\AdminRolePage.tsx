import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Title from '@components/common/Title/Title';
import AuthDetail from '@page/Admin/Role/Components/AuthDetail';
import AuthList from '@page/Admin/Role/Components/AuthList';
import MenuAuthList from '@page/Admin/Role/Components/MenuAuthList';
import ServiceAuthList from '@page/Admin/Role/Components/ServiceAuthList';
import { AuthData, DetailFormValues, ParsedPageAuthData, SearchFormValues } from '@page/Admin/Role/type';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useConfirmStore } from '@store/useConfirmStore';
import SubTitle from '@components/common/Title/SubTitle';
import { useAuthStore } from '@page/Admin/Role/store/useAuthStore';
import { getAuthList, getAuthInfoData, addAuth, updateAuth, deleteAuth, getPageAuthList } from '@api/admin/roleApi';
import { getAdminMenus } from '@api/admin/menuManageApi';
import { getServiceList } from '@api/admin/serviceAPI';
import useRole from './hooks/useRole';
import { useAlertStore } from '@store/useAlertStore';
import { TabsContainer } from '@components/common/Tabs/TabsContainer';
import { TabPanel } from '@components/common/Tabs/TabPanel';
import { TabList } from '@components/common/Tabs';
import PageAuthList from '@page/Admin/Role/Components/PageAuthList';

const TAB_LIST = [
  { id: 'menu', label: '메뉴 권한' },
  { id: 'service', label: '서비스 권한' },
  { id: 'page', label: '페이지 권한' },
];

const AdminRolePage = () => {
  const {
    detailState,
    setDetailState,
    menuListData,
    setMenuListData,
    serviceListData,
    setServiceListData,
    pageAuthListData,
    setPageAuthListData,
    authData,
    setAuthData,
    originalAuthListData,
    setAuthListData,
    setOriginalAuthListData,
  } = useAuthStore();

  const { sortMenuData, parseServiceData, parsePageAuthListData } = useRole();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  const [menuAuthList, setMenuAuthList] = useState<number[]>([]);
  const [serviceGroupIds, setServiceGroupIds] = useState([]);
  const [serviceIds, setServiceIds] = useState([]);

  // 검색 폼 관리
  const searchMethods = useForm<SearchFormValues>({
    defaultValues: {
      name: '', // 권한명 검색어 초기값
    },
  });

  // 상세 폼 관리
  const detailMethods = useForm<DetailFormValues>({
    defaultValues: {
      name: '', // 권한명 초기 값
    },
  });

  const { reset } = detailMethods;

  // 권한 목록 조회
  const getAuthListData = async () => {
    const authList = await getAuthList({});

    setAuthListData(authList);
    setOriginalAuthListData(authList);
  };

  // 메뉴, 서비스, 페이지 권한 목록 조회
  const getTabsData = async () => {
    const menusListResponse = await getAdminMenus({}); // 메뉴 목록 조회
    const servicesListResponse = await getServiceList({}); // 서비스 목록 조회
    const pageAuthListResponse = await getPageAuthList({}); // 페이지 권한 목록 조회

    setMenuListData(sortMenuData(menusListResponse)); // 메뉴 목록 정렬 후 세팅
    setServiceListData(parseServiceData(servicesListResponse)); // 서비스 목록 파싱 후 세팅
    setPageAuthListData(parsePageAuthListData(pageAuthListResponse)); // 페이지 권한 목록 파싱 후 세팅
  };

  const getAuthDetailInfoData = async (id: number) => {
    const authDetailInfo = await getAuthInfoData({ id });
    reset({
      name: authDetailInfo.name,
    });
    setAuthData(authDetailInfo);
    setDetailState('edit');

    setMenuAuthList(authDetailInfo.menuIds);
    setServiceGroupIds(authDetailInfo.serviceGroupIds);
    setServiceIds(authDetailInfo.serviceIds);
    setPageAuthListData(
      pageAuthListData.map((pageAuth) => {
        const pageRole = authDetailInfo.pageRoles.find((pageRole) => pageRole.pageId === pageAuth.pageId);
        return {
          ...pageAuth,
          readRoleYn: pageRole?.readRoleYn === 'Y',
          regRoleYn: pageRole?.regRoleYn === 'Y',
          modRoleYn: pageRole?.modRoleYn === 'Y',
          delRoleYn: pageRole?.delRoleYn === 'Y',
        };
      })
    );
  };

  // 권한 목록 조회
  useEffect(() => {
    setDetailState(null);
    setAuthData(null);
    getAuthListData(); // 권한 목록 조회
    getTabsData(); // 메뉴 및 서비스 조회
  }, []);

  // 권한 그룹 목록 검색
  const handleSearchAuthGroup = () => {
    const searchKeyword = searchMethods?.getValues('name');
    if (searchKeyword === '') {
      setAuthListData(originalAuthListData);
      return;
    }
    const searchResult = originalAuthListData.filter((auth: AuthData) =>
      auth.name.toLowerCase().includes(searchKeyword.toLowerCase())
    );
    setAuthListData(searchResult);
  };

  // 권한 그룹 추가
  const handleOnClickAddAuth = () => {
    setDetailState('add');
    reset({
      name: '',
    });

    setMenuAuthList([]);
    setServiceGroupIds([]);
    setServiceIds([]);
    setPageAuthListData(
      pageAuthListData.map((pageAuth) => ({
        ...pageAuth,
        readRoleYn: false,
        regRoleYn: false,
        modRoleYn: false,
        delRoleYn: false,
      }))
    );
  };

  const handleSubmit = (data: DetailFormValues) => {
    const { name } = data;

    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '권한 정보를 저장하시겠습니까?',
      onConfirm: async () => {
        const parsePageRoles: ParsedPageAuthData[] = pageAuthListData
          ?.filter((auth) => auth.readRoleYn || auth.regRoleYn || auth.modRoleYn || auth.delRoleYn)
          .map((auth) => ({
            pageId: auth.pageId as number,
            readRoleYn: auth.readRoleYn ? 'Y' : 'N',
            regRoleYn: auth.regRoleYn ? 'Y' : 'N',
            modRoleYn: auth.modRoleYn ? 'Y' : 'N',
            delRoleYn: auth.delRoleYn ? 'Y' : 'N',
          }));

        if (detailState === 'add') {
          const response = await addAuth({
            name,
            menuIds: menuAuthList,
            serviceGroupIds,
            serviceIds,
            pageRoles: parsePageRoles,
          });
          if (response) {
            initConfirmState();
            await getAuthListData();
            activeAlert(response);
          }
        } else {
          const response = await updateAuth({
            id: authData.id,
            name,
            menuIds: menuAuthList,
            serviceGroupIds,
            serviceIds,
            pageRoles: parsePageRoles,
          });

          if (response) {
            initConfirmState();
            await getAuthListData();
            activeAlert(response);
          }
        }
      },
      onCancel: initConfirmState,
    });
  };

  const handleOnClickDeleteAuth = (e: React.MouseEvent, id: number) => {
    e.stopPropagation(); // 이벤트 버블링 방지
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onConfirm: async () => {
        const responseMessage = await deleteAuth({ id });
        if (responseMessage) {
          initConfirmState();
          setDetailState(null); // 상세 상태 초기화
          setAuthData(null); // 권한 데이터 초기화
          await getAuthListData(); // 권한 목록 조회
          activeAlert(responseMessage);
        }
      },
      onCancel: initConfirmState,
    });
  };

  return (
    <div className="manage_admin_authority">
      {/* 검색 폼 */}
      <Form onSubmit={handleSearchAuthGroup} methods={searchMethods}>
        <ControlBox>
          <FormInput name="name" label="권한명" placeholder="권한명을 입력하세요" />
          <Button type="submit" text="조회" clickLog={{ buttonSection: '검색창' }} />
        </ControlBox>
      </Form>
      <Form className="content horizontal" onSubmit={handleSubmit} methods={detailMethods}>
        <div className="left_content vertical">
          <TableContainerHeader
            leftChildren={<SubTitle>권한리스트</SubTitle>}
            rightChildren={
              <Button text="추가" onClick={handleOnClickAddAuth} clickLog={{ buttonSection: '권한리스트' }} />
            }
          />
          {/* 권한 리스트 */}
          <AuthList getAuthDetailInfoData={getAuthDetailInfoData} handleOnClickDeleteAuth={handleOnClickDeleteAuth} />
        </div>
        <div className="right_content vertical">
          <TableContainerHeader
            leftChildren={<SubTitle>권한정보</SubTitle>}
            rightChildren={
              detailState && (
                <Button
                  type="submit"
                  text={`권한 및 정보 ${detailState === 'add' ? '추가' : '수정'}`}
                  clickLog={{ buttonSection: '권한 정보' }}
                />
              )
            }
          />
          {/* 권한 상세 */}
          <AuthDetail />
          {detailState && (
            <TabsContainer defaultTab="menu" containerId="admin_role_page">
              <TabList data={TAB_LIST} />
              <TabPanel id="menu">
                <MenuAuthList menuListData={menuListData} checkedList={menuAuthList} setCheckedList={setMenuAuthList} />
              </TabPanel>
              <TabPanel id="service">
                <ServiceAuthList
                  serviceListData={serviceListData}
                  serviceGroupIds={serviceGroupIds}
                  setServiceGroupIds={setServiceGroupIds}
                  serviceIds={serviceIds}
                  setServiceIds={setServiceIds}
                />
              </TabPanel>
              <TabPanel id="page">
                <PageAuthList />
              </TabPanel>
            </TabsContainer>
          )}
        </div>
      </Form>
    </div>
  );
};

export default AdminRolePage;
