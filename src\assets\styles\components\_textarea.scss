@use '@styles/utils/mixin' as m;

.c_textarea {
  // 기본스타일 초기화
  all: unset;
  resize: none;
  word-break: break-all;
  box-sizing: border-box;

  // 커스텀
  display: block;
  width: 100%;
  height: auto;
  padding: 0.4375rem 0.75rem;
  border-radius: 4px;
  text-align: left;

  background-color: var(--modal_bg);
  color: var(--font_default);
  border: 1px solid var(--g_06);
  outline: none;

  &.default {
    &:focus {
      outline: 3px solid var(--g_09);
    }
  }

  &::placeholder {
    color: var(--g_05);
  }

  &:read-only {
    background-color: var(--g_04);
  }

  &:disabled {
    background-color: var(--g_04);
    color: var(--font_disabled);
  }
}

.c_textarea_wrapper {
  display: block;
  width: 100%;
  border-radius: 4px;

  &.error,
  &.warning,
  &.confirm {
    position: relative;

    &::before {
      @include m.content_without_url(1rem, 1rem);
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
    }

    .c_textarea {
      display: block;
      width: 100%;
      border: 0;
      padding-right: 2.5rem;
      overflow: hidden;
    }
  }

  &.error {
    @include m.before_url('@assets/images/icon/icon_error.svg');
    border: 1px solid var(--error);
    textarea {
      &:focus {
        outline: 3px solid var(--error);
      }
    }
  }
  &.warning {
    @include m.before_url('@assets/images/icon/icon_warning.svg');
    border: 1px solid var(--warning);
    textarea {
      &:focus {
        outline: 3px solid var(--warning);
      }
    }
  }
  &.confirm {
    @include m.before_url('@assets/images/icon/icon_confirm.svg');
    border: 1px solid var(--confirm);
    textarea {
      &:focus {
        outline: 3px solid var(--confirm);
      }
    }
  }
}
