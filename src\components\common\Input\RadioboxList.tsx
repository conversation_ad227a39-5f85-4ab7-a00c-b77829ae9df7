import { forwardRef, InputHTMLAttributes, useState, useEffect } from 'react';
import Radiobox from './Radiobox';
import ErrorMsg from '@components/common/Error/ErrorMsg';
import joinClassName from '@utils/joinClassName';

/**
 * 라디오 옵션 인터페이스
 */
interface RadioOption {
  label: string; // 라디오 버튼 레이블
  value: string | number; // 라디오 버튼 값
  disabled?: boolean; // 비활성화 여부
}

/**
 * 라디오박스 리스트 컴포넌트 Props 인터페이스
 */
interface RadioboxListProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'value'> {
  options: RadioOption[]; // 라디오 옵션 배열
  value?: string | number; // 선택된 라디오 값
  hasLabel?: boolean; // 레이블 표시 여부
  className?: string; // 컨테이너 클래스명
  listClassName?: string; // 라디오 그룹 클래스명
  error?: string; // 에러 메시지
  direction?: 'horizontal' | 'vertical'; // 라디오 버튼 배치 방향
}

/**
 * 라디오박스 리스트 컴포넌트
 * 여러 개의 라디오 버튼을 그룹으로 표시하는 컴포넌트
 */
const RadioboxList = forwardRef<HTMLDivElement, RadioboxListProps>(
  (
    {
      options, // 라디오 옵션 배열
      value, // 선택된 값
      name, // 라디오 그룹 이름
      hasLabel = false, // 레이블 표시 여부 (기본값: false)
      className, // 추가 클래스명
      listClassName, // 라디오 그룹 클래스명
      error, // 에러 메시지
      direction = 'horizontal', // 배치 방향 (기본값: 가로)
      onChange, // 변경 이벤트 핸들러
      ...attributes // 기타 HTML 속성들
    },
    ref // 부모 컴포넌트에서 접근할 수 있는 ref
  ) => {
    // 내부 선택 상태 관리
    const [selectedValue, setSelectedValue] = useState<string | number | undefined>(value);

    // 외부에서 value가 변경될 경우 내부 상태 업데이트
    useEffect(() => {
      setSelectedValue(value);
    }, [value]);

    // 라디오 버튼 변경 이벤트 핸들러
    const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = e.target.value;
      setSelectedValue(newValue);
      
      // 외부 onChange 핸들러가 있으면 호출
      if (onChange) {
        onChange(e);
      }
    };

    // 컨테이너 클래스 생성
    const containerClass = joinClassName(
      'c_radio_list',
      direction === 'vertical' ? 'vertical' : 'horizontal',
      className
    );

    // 라디오 그룹 클래스 생성
    const radioGroupClass = joinClassName('c_radio_group', listClassName);

    return (
      <div className={containerClass} ref={ref}>
        <div 
          className={radioGroupClass}
          role="radiogroup" // 접근성을 위한 role 속성
          aria-label={name} // 접근성을 위한 라벨
        >
          {/* 옵션 배열을 순회하며 라디오 버튼 생성 */}
          {options.map((option) => (
            <Radiobox
              key={option.value} // React 리스트 렌더링을 위한 고유 키
              name={name} // 라디오 그룹 이름
              value={option.value} // 라디오 값
              label={option.label} // 라디오 레이블
              hasLabel={hasLabel} // 레이블 표시 여부
              checked={selectedValue === option.value} // 내부 상태를 기반으로 체크 상태 결정
              disabled={option.disabled} // 비활성화 여부
              onChange={handleRadioChange} // 내부 이벤트 핸들러 사용
              {...attributes} // 기타 HTML 속성 전달
            />
          ))}
        </div>
        {/* 에러 메시지가 있을 경우 표시 */}
        {error && <ErrorMsg text={error} />}
      </div>
    );
  }
);

export default RadioboxList;
