import joinClassName from '@utils/joinClassName';
import { IconButtonProps } from './types';
import useClickLog from '@hooks/useClickLog';
import { LogParams } from '@components/common/Log/Components/types';

/**아이콘 버튼 */
const IconButton = ({
  text, // 버튼 text(*)
  onClick, // 버튼 클릭 시 실행 함수(*)
  icon, // 아이콘 (*)
  iconOnly = false, // text 없이 아이콘만 있는 경우
  type = 'button', // 버튼의 type (button/submit/reset)
  isActive = false, // 버튼 활성 여부
  clickLog, // 버튼 로그 params

  /**아이콘 버튼 디자인 요소 */
  design = 'default',
  size = 'medium',
  fill = 'filled',
  color = 'primary',
  iconPosition = 'left',
  className, // 추가 클래스 요소
  children,
  ...attributes
}: IconButtonProps) => {
  const commonButtonClass = 'c_button_icon' + (iconOnly ? ' icon_only' : '_text');
  const commonButtonTextClass = 'c_button_text' + (iconOnly ? ' sr-only' : '');
  const sizeClass = iconOnly ? size : ''; // Design 이 Circle일 경우만 사용
  const { handleClickLog } = useClickLog();

  const buttonClass = joinClassName(
    commonButtonClass,
    design,
    fill,
    sizeClass,
    color,
    `i_${icon}`,
    iconPosition,
    className,
    isActive ? 'active' : ''
  );

  return (
    <button
      type={type}
      onClick={(event) => {
        onClick(event);

        if (clickLog) {
          handleClickLog({
            eventType: (type === 'button' ? 'click' : type) as LogParams['logType'],
            button: text,
            buttonSection: clickLog.buttonSection,
          });
        }
      }}
      className={buttonClass}
      {...attributes}
    >
      <span className={commonButtonTextClass}>{text}</span>
      {children}
    </button>
  );
};

export default IconButton;
