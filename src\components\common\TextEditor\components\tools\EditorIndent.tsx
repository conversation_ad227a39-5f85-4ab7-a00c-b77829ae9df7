import IconButton from '@components/common/Button/IconButton';
import { useCurrentEditor } from '@tiptap/react';
import { IconType } from '@components/common/Button/types';

const EditorIndent = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const handleIndent = () => {
    // @ts-ignore
    editor.chain().focus().indent().run();
  };

  const handleOutdent = () => {
    // @ts-ignore  
    editor.chain().focus().outdent().run();
  };

  return (
    <div className="item_box">
      <IconButton
        icon={'indent_decrease' as IconType}
        iconOnly
        size='smallest'
        color={editor.isActive('outdent') ? 'primary' : 'grayscale'}
        fill="unfilled"
        text="내어쓰기"
        isActive={editor.isActive('outdent')}
        onClick={handleOutdent}
        disabled={editor.isActive('codeBlock') || editor.isActive('blockquote')}
        />
      <IconButton
        icon={'indent_increase' as IconType}
        iconOnly
        size='smallest'
        color={editor.isActive('indent') ? 'primary' : 'grayscale'}
        fill="unfilled"
        text="들여쓰기"
        isActive={editor.isActive('indent')}
        onClick={handleIndent}
        disabled={editor.isActive('codeBlock') || editor.isActive('blockquote')}
        />
    </div>
  );
};

export default EditorIndent;
