import React, { forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { AgGridReact } from '@ag-grid-community/react'; // React에서 AG Grid를 사용하기 위한 컴포넌트
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model'; // 데이터를 클라이언트 메모리에 모두 로드 후, 정렬/필터링/페이징을 클라이언트에서 처리
import { InfiniteRowModelModule } from '@ag-grid-community/infinite-row-model'; // 서버에서 데이터를 비동기적으로 페이지 단위 로딩 (무한 스크롤)
import GridTooltip from './GridTooltip';
import Pagination from '@components/common/Pagination';
import SelectBox from '@components/common/SelectBox/SelectBox';
import { pageSizeOptions } from '@constants/options';
import Button from '@components/common/Button/Button';
import type { PaginationInfo } from '@type/pagination';
import { useState } from 'react';

import {
  ColDef,
  ColGroupDef,
  GridOptions, // AG Grid의 전체 옵션을 정의하는 객체 타입
  GridSizeChangedEvent, // 그리드 크기 변경 시 발생하는 이벤트 타입
  ModuleRegistry, // AG Grid에서 사용할 모듈들을 등록할 때 사용하는 유틸리티
  SizeColumnsToContentStrategy, // 각 셀의 콘텐츠 크기에 맞춰 열 너비 자동 조정 전략
  SizeColumnsToFitGridStrategy, // 전체 그리드 너비에 맞춰 열 너비 균등 분배 전략
  SizeColumnsToFitProvidedWidthStrategy, // 지정된 너비에 맞춰 열 너비 조정 전략
  GridReadyEvent,
} from '@ag-grid-community/core'; // AG Grid의 타입, 클래스, 전략 등 핵심 기능을 포함한 패키지

import '@ag-grid-community/styles/ag-grid.css'; // AG Grid의 공통 스타일
import '@ag-grid-community/styles/ag-theme-quartz.css'; // AG Grid의 Quartz 테마 스타일

import GridLoading from '@components/common/Grid/GridLoading';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';

//사용할 모듈 등록
ModuleRegistry.registerModules([ClientSideRowModelModule, InfiniteRowModelModule]);

interface DataGridProps {
  columnDefs: ColDef[];
  rowData: any[];
  rowEditMode: boolean;
  addButtonCallback?: () => void;
  serverPagination?: PaginationInfo;
  onPageChange?: (currentPage: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  [key: string]: any;
}

export const baseColDef: ColDef = {
  suppressMovable: true,
  sortable: false, // 정렬 가능
  filter: false, // 필터 가능
  resizable: true, // 컬럼 리사이즈 가능
  editable: false, // 기본은 편집 불가능
  // floatingFilter: true, // 작은 필터 UI 보여주기
  minWidth: 100, // 최소 너비
  flex: 1, // 그리드 영역 채우기
  tooltipField: 'tooltip', // tooltipField라는 필드를 tooltip으로 사용
  cellClass: 'text-s', // 기본 셀 클래스
  wrapText: false, // 기본 줄바꿈 비활성화 (줄바꿈이 필요한 컬럼만 따로 지정)
  autoHeight: false, // 높이 자동 조절은 비활성화 (rowHeight 설정 권장)
  headerClass: 'font-bold text-sm',
  tooltipComponent: GridTooltip, // 커스텀 툴팁 사용
};

const DataGrid = forwardRef<any, DataGridProps>(
  (
    {
      columnDefs,
      rowData,
      rowEditMode,
      serverPagination, // 기본값 false 설정 가능
      addButtonCallback,
      deleteButtonCallback,
      onPageChange,
      onPageSizeChange,
      defaultColDef,
      ...props
    },
    ref
  ) => {
    const gridRef = useRef(null);
    const containerClass = utils.joinClassName('c_grid_container', 'c_grid_container_reset');
    const normalizedColumnDefs = columnDefs.map((col) => ({
      ...col,
      tooltipField: col.tooltipField ?? col.field, // tooltipField 없으면 자동 설정.
      headerTooltip: col.headerTooltip ?? col.headerName, // headerTooltip 없으면 자동 설정.
      onCellValueChanged: (params) => {
        // 셀 편집후 컨텐츠 파란색으로 변경
        const { oldValue, newValue, data, api, node, column } = params;

        // 수정 여부 저장할 map이 없으면 생성
        if (!data._modifiedMap) {
          data._modifiedMap = {};
        }

        // 현재 컬럼 필드 기준으로 수정 여부 저장
        const field = column.getColId(); // 또는 column.getColDef().field;
        data._modifiedMap[field] = oldValue !== newValue;

        //해당 셀만 리렌더링
        api.refreshCells({
          rowNodes: [node],
          columns: [column],
          force: true,
        });
      },
      cellClassRules: {
        'text-blue-500': (params) => {
          const field = params.colDef.field;
          return params.data._modifiedMap?.[field] === true;
        },
        'bg-sky-50': (params) => {
          const colDef = params.colDef;
          const isEditable = typeof colDef.editable === 'function' ? colDef.editable(params) : colDef.editable;
          return isEditable === true;
        },
      },
    }));

    const mergeDefaultColDef: ColDef = {
      ...baseColDef, // 내부 기본 설정
      ...defaultColDef, // 외부에서 전달된 것 우선 적용
    };

    const onChangePageSize = (option) => {
      const newPageSize = option.value;
      onPageSizeChange(newPageSize);
      onPageChange(1);
    };

    useImperativeHandle(ref, () => ({
      getSelectedNodes: () => {
        return gridRef.current?.api?.getSelectedNodes();
      },
    }));

    const [selectedRow, setSelectedRow] = useState([]);
    const onGridReady = (params: GridReadyEvent) => {
      gridRef.current = params.api;
      params.api.addEventListener('selectionChanged', onSelectionChanged);
    };

    const onSelectionChanged = () => {
      const selectedNodes = gridRef.current?.getSelectedNodes();
      setSelectedRow(selectedNodes || []);
    };

    return (
      <div>
        <TableContainerHeader
          leftChildren={
            <div className="flex items-center gap-2">
              {serverPagination ? (
                <p>총 {serverPagination?.totalCount}건</p>
              ) : (
                <p>총 {rowData?.length}건</p>
              )}
              {serverPagination && (
                <SelectBox
                  defaultValue={pageSizeOptions[0]}
                  options={pageSizeOptions}
                  selectedValue={{ label: `${serverPagination.pageSize}`, value: serverPagination.pageSize }} // 선택된 페이지 크기
                  setSelectedValue={onChangePageSize}
                />
              )}
            </div>
          }
          rightChildren={
            <div className="flex gap-1">
              {rowEditMode && (
                <>
                  <Button text="삭제" color="red" />
                  <Button text="추가" />
                </>
              )}
              {deleteButtonCallback && (
                <Button text="삭제" color="red" onClick={deleteButtonCallback} disabled={_.isEmpty(selectedRow)} />
              )}
              {addButtonCallback && <Button text="추가" onClick={addButtonCallback} />}
            </div>
          }
        />

        <div className={containerClass}>
          <AgGridReact
            ref={gridRef}
            onGridReady={onGridReady}
            overlayNoRowsTemplate={'조회된 데이터가 없습니다.'}
            className="ag-theme-quartz"
            columnDefs={normalizedColumnDefs}
            rowData={rowData}
            {...props}
            tooltipShowMode={'whenTruncated'} // ellipsis 형태의 셀만 툴팁 show
            tooltipShowDelay={0}
            defaultColDef={mergeDefaultColDef}
            enableCellTextSelection={true} // 셀 택스트 복사 가능
            domLayout="autoHeight"
          />
          {serverPagination && (
            <div className="mt-3">
              <Pagination
                totalCount={serverPagination.totalCount}
                itemCountPerPage={serverPagination.pageSize}
                currentPage={serverPagination.currentPage}
                onPageChange={onPageChange}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
);

export default DataGrid;
