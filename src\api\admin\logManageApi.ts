import api from '@api/api';
import dayjs from 'dayjs';
import { defaultHandleError } from '@utils/apiErrorHandler';
import { useDownloadingStore } from '@store/useLoadingStore';

// [관리자] 로그 관리

/**
 * 로그인 로그 조회
 */

interface LoginLogsParams {
  page: number;
  size: number;
  startDate: Date;
  endDate: Date;
  loginId?: string;
  isGetErrorMsg?: boolean;
}

export const getLoginLogs = async ({
  page,
  size,
  startDate,
  endDate,
  loginId,
  isGetErrorMsg = false,
}: LoginLogsParams) => {
  try {
    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/login-logs?page=${page}&size=${size}&startDate=${start}&endDate=${end}${loginId && loginId.trim() !== '' ? `&loginId=${loginId}` : ''}`;
    const response = await api.get(apiUrl);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '로그인 로그');
  }
};

/**
 * 로그인 로그 조회 CSV 다운로드
 */

interface LoginLogsCSVDownloadParams {
  startDate: Date;
  endDate: Date;
  loginId: string;
  isGetErrorMsg?: boolean;
}

export const getLoginLogsCsv = async ({
  startDate,
  endDate,
  loginId,
  isGetErrorMsg = false,
}: LoginLogsCSVDownloadParams) => {
  useDownloadingStore.getState().setIsDownloading(true);

  try {
    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/login-logs/download`;
    const response = await api.get(apiUrl, {
      responseType: 'blob',
      data: {
        loginId,
        startDate: start,
        endDate: end,
      },
    });

    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `로그인_로그_${start}~${end}.csv`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(link.href);
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '로그인 로그 CSV 다운로드');
  } finally {
    useDownloadingStore.getState().setIsDownloading(false);
  }
};

/**
 * API 호출 로그 조회
 */

interface ApiLogsParams {
  page: number;
  size: number;
  startDate: Date;
  endDate: Date;
  loginId?: string;
  globalTraceId?: string;
  isGetErrorMsg?: boolean;
}

export const getApiLogs = async ({
  page,
  size,
  startDate,
  endDate,
  loginId,
  globalTraceId,
  isGetErrorMsg = false,
}: ApiLogsParams) => {
  try {
    const isValid = (value) => value && value.trim() !== '';

    const params = [
      isValid(loginId) && `loginId=${loginId.trim()}`,
      isValid(globalTraceId) && `globalTraceId=${globalTraceId.trim()}`,
    ]
      .filter(Boolean)
      .join('&');

    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/api-logs?page=${page}&size=${size}&startDate=${start}&endDate=${end}${`&${params}`}`;
    const response = await api.get(apiUrl);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, 'API 호출 로그');
  }
};

/**
 * API 로그 조회 CSV 다운로드
 */

interface ApiLogsCSVDownloadParams {
  startDate: Date;
  endDate: Date;
  loginId: string;
  globalTraceId: string;
  isGetErrorMsg?: boolean;
}

export const getApiLogsCsv = async ({
  startDate,
  endDate,
  loginId,
  globalTraceId,
  isGetErrorMsg = false,
}: ApiLogsCSVDownloadParams) => {
  useDownloadingStore.getState().setIsDownloading(true);

  try {
    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/api-logs/download`;
    const response = await api.get(apiUrl, {
      responseType: 'blob',
      data: {
        loginId,
        globalTraceId,
        startDate: start,
        endDate: end,
      },
    });

    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `API_로그_${start}~${end}.csv`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(link.href);
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, 'API 로그 CSV 다운로드');
  } finally {
    useDownloadingStore.getState().setIsDownloading(false);
  }
};

/**
 * 프론트 로그 조회
 */

interface FrontLogsParams {
  page: number;
  size: number;
  startDate: Date;
  endDate: Date;
  loginId?: string;
  globalTraceId?: string;
  menuName?: string;
  isGetErrorMsg?: boolean;
}

export const getFrontLogs = async ({
  page,
  size,
  startDate,
  endDate,
  loginId,
  globalTraceId,
  menuName,
  isGetErrorMsg = false,
}: FrontLogsParams) => {
  try {
    const isValid = (value) => value && value.trim() !== '';

    const params = [
      isValid(loginId) && `loginId=${loginId.trim()}`,
      isValid(globalTraceId) && `globalTraceId=${globalTraceId.trim()}`,
      isValid(menuName) && `menuName=${menuName.trim()}`,
    ]
      .filter(Boolean)
      .join('&');

    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/front-logs?page=${page}&size=${size}&startDate=${start}&endDate=${end}${`&${params}`}`;
    const response = await api.get(apiUrl);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '프론트 로그 조회');
  }
};

/**
 * 프론트 로그 조회 CSV 다운로드
 */

interface FrontLogsCSVDownloadParams {
  startDate: Date;
  endDate: Date;
  loginId: string;
  globalTraceId: string;
  menuName: string;
  isGetErrorMsg?: boolean;
}

export const getFrontLogsCsv = async ({
  startDate,
  endDate,
  loginId,
  globalTraceId,
  menuName,
  isGetErrorMsg = false,
}: FrontLogsCSVDownloadParams) => {
  useDownloadingStore.getState().setIsDownloading(true);
  try {
    const start = dayjs(startDate).format('YYYY-MM-DD');
    const end = dayjs(endDate).format('YYYY-MM-DD');

    const apiUrl = `/api-admin/front-logs/download`;
    const response = await api.get(apiUrl, {
      responseType: 'blob',
      data: {
        loginId,
        globalTraceId,
        menuName,
        startDate: start,
        endDate: end,
      },
    });

    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `프론트_로그_${start}~${end}.csv`;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    URL.revokeObjectURL(link.href);
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '프론트 로그 CSV 다운로드');
  } finally {
    useDownloadingStore.getState().setIsDownloading(false);
  }
};
