export const getExtractedStyleSheetIconOptions = () => {
  const iconOptions = [];

  for (let sheet of document.styleSheets) {
    try {
      for (let rule of sheet.cssRules) {
        if ('selectorText' in rule && typeof rule.selectorText === 'string' && rule.selectorText.includes('.i_')) {
          const className = rule.selectorText.match(/\.i_[^.:,]+/)?.[0]?.replace('.i_', '');

          // 중복된 값이 있는지 확인 후 없을 경우에만 추가
          const isDuplicate = iconOptions.some((style) => style.value === className);

          if (className === 'close_white' || className === 'circle_close') {
            continue;
          }

          if (!isDuplicate && className) {
            iconOptions.push({ label: className, value: className });
          }
        }
      }
    } catch (e) {
      console.warn('CSS rules 접근 불가:', e);
    }
  }

  return iconOptions;
};