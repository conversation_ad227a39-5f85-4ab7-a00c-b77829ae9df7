import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormTextarea from '@components/common/Form/FormTextarea';
import DefaultModal from '@components/common/Modal/DefaultModal';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { BodyRowProps } from '@page/Admin/Account/type';
import { useUserServiceGroupAPI } from '@page/User/Service/hooks/useUserServiceGroupAPI';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { ServiceGroupData } from '@page/User/Service/type';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

const BodyRow = ({ title = '', required, children }: BodyRowProps) => {
  return (
    <tr>
      <th>
        <span className={required ? 'required' : ''}>{title}</span>
      </th>
      <td colSpan={3}>{children}</td>
    </tr>
  );
};

const ServiceGroupModal = () => {
  // hook
  const methods = useForm({
    defaultValues: {
      name: '',
      groupDesc: '',
    },
  });
  const { reset } = methods;
  const { serviceGroupDetail, modalType, isOpenServiceGroupModal, setIsOpenServiceGroupModal } = useServiceStore();
  const { addServiceGroup, updateServiceGroup } = useUserServiceGroupAPI();

  const handleOnSubmit = (data: ServiceGroupData) => {
    handleServiceGroup(data);
  };

  // API functions
  const handleServiceGroup = async (data: ServiceGroupData) => {
    if (modalType === 'add') {
      addServiceGroup(data);
    } else {
      if (serviceGroupDetail) {
        updateServiceGroup(serviceGroupDetail.id, data);
      }
    }
  };

  useEffect(() => {
    if (modalType === 'edit') {
      serviceGroupDetail &&
        reset({
          name: serviceGroupDetail.name,
          groupDesc: serviceGroupDetail.groupDesc,
        });
    } else {
      reset({
        name: '',
        groupDesc: '',
      });
    }
  }, [modalType, serviceGroupDetail]);

  return (
    <DefaultModal
      className="service_group_confirm_modal"
      title={`서비스 그룹 ${modalType === 'add' ? '등록' : '수정'}`}
      isOpenModal={isOpenServiceGroupModal}
      setIsOpenModal={setIsOpenServiceGroupModal}
      footer={false}
    >
      <Form methods={methods} onSubmit={handleOnSubmit}>
        <TableContainer>
          <colgroup>
            <col width={'160px'} />
            <col width={'calc(100% - 160px)'} />
          </colgroup>
          <TableBody>
            <BodyRow title="서비스 그룹명" required>
              <FormInput
                name="name"
                placeholder="서비스 그룹명 입력"
                rules={{
                  required: '필수값입니다.',
                }}
              />
            </BodyRow>
            <BodyRow title="서비스 그룹 설명">
              <FormTextarea name="groupDesc" placeholder="서비스 그룹 설명 입력" className="group_desc" />
            </BodyRow>
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button
            text="취소"
            color="grayscale"
            onClick={() => {
              setIsOpenServiceGroupModal(false);
            }}
          />
          <Button type="submit" text="확인" onClick={() => { }} />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default ServiceGroupModal;
