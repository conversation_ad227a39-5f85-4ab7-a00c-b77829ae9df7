@use '@styles/utils/mixin' as m;

[data-is-open-lnb='true'] {
  footer {
    min-width: 1280px;
    width: calc(100% - 250px);
    background-color: var(--footer_bg);
    color: var(--g_09);

    @include m.bp_large() {
      width: calc(100% - 225px);
    }

    @include m.bp_medium() {
      width: calc(100% - 200px);
    }
  }
}

[data-is-open-lnb='false'] {
  footer {
    min-width: calc(1205px + 75px);
    width: calc(100% - 75px);

    @include m.bp_large() {
      width: calc(100% - 65px);
    }

    @include m.bp_medium() {
      width: calc(100% - 65px);
    }
  }
}

footer {
  padding: 0.675rem;
  font-size: 0.875rem;
  font-weight: 400;
  text-align: right;
  margin-left: auto;
}

@include m.bp_large() {
  footer {
    padding: 0.5rem;
    font-size: 0.75rem;
  }
}

@include m.bp_medium() {
  footer {
    padding: 0.5rem;
    font-size: 0.625rem;
  }
}
