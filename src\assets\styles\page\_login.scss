@use '@styles/utils/mixin' as m;

.login {
  height: 100vh;
  @include m.bg_set(center, no-repeat, cover);
  color: white;
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.6)), url('@assets/images/login_bg.jpg');

  .login_contents_wrapper {
    @include m.position_center(fixed);
    max-width: 468px;
    width: 100%;
    padding: 2rem;
    border-radius: 20px;
    background-color: rgba(#fff, 0.1);
    backdrop-filter: blur(8px);
    box-shadow: var(--shadow_l);

    // border
    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 20px;
      padding: 1px;
      z-index: -1;
      background: conic-gradient(
        from 304deg,
        rgba(#fff, 1) 0deg,
        rgba(#000, 0.1) 90deg,
        rgba(#fff, 1) 110deg,
        rgba(#000, 0.1) 284deg,
        rgba(#fff, 1) 294deg,
        rgba(#000, 0.1) 330deg,
        rgba(#fff, 1) 360deg
      );
      mask:
        linear-gradient(to right, #fff 0%, #fff 100%) content-box,
        linear-gradient(to right, #fff 0%, #fff 100%);
      -webkit-mask-composite: destination-out;
      mask-composite: exclude;
    }

    .login_contents_title {
      font-size: 2rem;
      font-weight: 600;
    }

    .login_contents {
      margin-top: 2rem;

      .login_contents_inputs {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        margin-bottom: 2rem;

        .login_input {
          @include m.flex(start, space-between);

          label {
            @include m.flex(center, center);
            height: 100%;
            font-size: 1rem;
            font-weight: 600;
          }

          input {
            min-width: 300px;
          }

          .c_error_msg {
            font-weight: 700;
            padding: 0.25rem;
            border-radius: 0.5rem;
          }
        }
      }

      .login_remember_id {
        margin-bottom: 2rem;
      }

      .button_wrapper {
        @include m.flex(center, space-between);
        gap: 1rem;

        .login_button {
          flex: 1;
          height: 3rem;
        }
      }
    }
  }

  .c_footer {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1rem;
    font-size: 0.875rem;
    background-color: transparent;
  }
}

.c_qr_login_modal {
  .c_modal_body {
    .c_qr_login_modal_content {
      .desc {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .qr_code_wrapper {
        border: 1px solid var(--g_04);
        padding: 2.5rem;
        .qr_code_img {
          @include m.flex(center, center);
          margin-bottom: 0.625rem;
          &.expired {
            filter: blur(1.8px);
          }
        }
      }
    }
  }

  .c_modal_footer {
    button {
      width: 100%;
    }
  }
}
