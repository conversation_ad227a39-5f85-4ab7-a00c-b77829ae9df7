import { LogParams } from '@components/common/Log/Components/types';
import { createTraceId } from '@components/common/Log/utils/createTraceId';
import { create } from 'zustand';

interface LogStore {
  logParams: LogParams;
  setLogParams: (logParams: LogParams | ((prev: LogParams) => LogParams)) => void;
  getLogParams: () => LogParams;
}

export const useLogStore = create<LogStore>((set, get) => ({
  logParams: {
    globalTraceId: createTraceId(),
    menuName: '',
    logType: null,
    buttonSection: '',
    button: '',
    fullUrl: '',
    url: '',
    referrer: '',
    data: {},
  },
  setLogParams: (state) =>
    set((prev) => ({
      logParams: typeof state === 'function' ? state(prev.logParams) : state,
    })),
  getLogParams: () => get().logParams,
}));
