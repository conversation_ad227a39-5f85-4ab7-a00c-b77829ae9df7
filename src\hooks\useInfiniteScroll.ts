import { Dispatch, SetStateAction, useEffect, useRef } from 'react';

/**
 * @param loading 현재 데이터 로딩 여부
 * @param boolean 추가 데이터 가져올 수 있는지 여부(마지막 페이지인가)
 * @param setPage 페이지 번호 증가
 */

interface Props {
  loading?: boolean;
  hasMore?: boolean;
  setPage: Dispatch<SetStateAction<number>>;
}

const useInfiniteScroll = ({ loading = false, hasMore = true, setPage }: Props) => {
  const observer = useRef<IntersectionObserver | null>(null); // observer 객체 저장
  const inView = useRef<HTMLDivElement | null>(null); // 관찰 요소

  useEffect(() => {
    if (loading) return;
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting && hasMore) {
        setPage((prevPage) => prevPage + 1);
      }
    });

    if (inView.current) observer.current.observe(inView.current);

    return () => {
      if (observer.current) observer.current.disconnect();
    };
  }, [loading, hasMore, setPage]);

  return { inView };
};

export default useInfiniteScroll;
