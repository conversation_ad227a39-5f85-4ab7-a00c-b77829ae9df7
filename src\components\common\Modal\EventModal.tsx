import { useEffect, useRef, useState } from 'react';
import ModalPortal from './ModalPortal';
import joinClassName from '@utils/joinClassName';
import Button from '@components/common/Button/Button';
import Checkbox from '@components/common/Input/Checkbox';
import dayjs from 'dayjs';
import { ResponseFile } from '@components/common/Filebox/Filebox';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay, Navigation } from 'swiper/modules';


export interface EventModalProps {
  data: {
    id: number;
    title: string;
    subTitle: string;
    exposureStart: string;
    exposureEnd: string;
    linkUrl: string;
    notOpenDay: number;
    fileInfo: ResponseFile[];
  };
}

const EventModal = ({ data }: EventModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const eventModalClass = joinClassName('c_modal', 'event');

  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  const [isChecked, setIsChecked] = useState(false);
  const [expiredCnt, setExpiredCnt] = useState<number>(data.notOpenDay === 1 ? 1 : 7);
  const [position, setPosition] = useState({ top: '50%', left: '50%' });

  const calcPosition = (windowX = 0.5, windowY = 0.5) => {
    if (modalRef.current) {
      const { innerWidth, innerHeight } = window;
      const center = {
        x: innerWidth * windowX - modalRef.current.offsetWidth * 0.5,
        y: innerHeight * windowY - modalRef.current.offsetHeight * 0.5,
      };
      setPosition({ top: `${center.y}px`, left: `${center.x}px` });
    }
  };

  const handleClose = () => {
    // 체크가 안된 경우 그냥 닫기
    if (!isChecked) return setIsOpenModal(false);

    const now = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
    const expired = dayjs(now).add(expiredCnt, 'day').format('YYYY-MM-DD HH:mm:ss');

    localStorage.setItem(`no_repeat_event_${data.id}`, expired);
    setIsOpenModal(false);
  };

  const handleCheckboxChange = () => {
    setIsChecked(!isChecked);
  };

  const handleImageClick = () => {
    if (data.linkUrl) {
      handleClose();
      window.open(data.linkUrl, '_blank');
    }
  };

  const removeExpiredStorage = () => {
    // 현재 시간
    const now = dayjs(new Date());

    // localStorage에서 'no_repeat_event'를 포함하는 모든 항목 가져오기
    const expiredStorageList = Object.keys(localStorage)
      .filter((key) => key.includes('no_repeat_event'))
      .reduce((obj, key) => {
        obj[key] = localStorage.getItem(key);
        return obj;
      }, {});

    // expiredStorageList의 각 항목을 확인하여 만료된 항목 제거
    Object.entries(expiredStorageList).forEach(([key, value]) => {
      if (value) {
        const expiredDate = dayjs(value as string);
        // 현재 시간보다 이전인 경우(만료된 경우) 해당 항목 제거
        if (expiredDate.isBefore(now)) {
          localStorage.removeItem(key);
        }
      }
    });
  };

  const validCurrentStorage = () => {
    const currentStorage = localStorage.getItem(`no_repeat_event_${data.id}`);

    if (currentStorage) {
      const expiredDate = dayjs(currentStorage);
      const now = dayjs(new Date());
      if (expiredDate.isBefore(now)) {
        localStorage.removeItem(`no_repeat_event_${data.id}`);
        setIsOpenModal(true);
      }
    } else {
      setIsOpenModal(true);
    }
  };

  useEffect(() => {
    if (modalRef.current) {
      if (!position.top || !position.left || isOpenModal) {
        calcPosition();
      }
    }
  }, [isOpenModal, window.innerWidth, window.innerHeight]);

  useEffect(() => {
    // 현재 id와 동일한 토큰 유효성 검사
    validCurrentStorage();

    // 만료된 토큰들을 전부 삭제
    removeExpiredStorage();

  }, []);

  return (
    <>
      {isOpenModal && (
        <ModalPortal>
          <div
            ref={modalRef}
            className={eventModalClass}
            // style={position}
          >
            <div className="c_modal_body">
              <Swiper
                slidesPerView={1}
                spaceBetween={30}
                pagination={{
                  clickable: true,
                }}
                centeredSlides={true}
                autoplay={{
                  delay: 5000,
                  disableOnInteraction: false,
                }}
                navigation={true}
                modules={[Pagination, Autoplay, Navigation]}
                className="mySwiper"
              >
                <div className="desc">
                  <h3>{data.title}</h3>
                  <p>{data.subTitle}</p>
                </div>
                {data.fileInfo.map((file) => (
                  <SwiperSlide key={file.id}>
                    <figure className="slide_img" onClick={handleImageClick}>
                      <img src={`/api/files/${file.id}`} alt={file.name} />
                    </figure>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
            <div className="c_modal_footer">
              <Checkbox
                checked={isChecked}
                onChange={handleCheckboxChange}
                label={`오늘 부터 ${expiredCnt} 일간 다시 보지 않기`}
                value={expiredCnt}
                hasLabel
              />
              <Button text="닫기" onClick={handleClose} />
            </div>
          </div>
        </ModalPortal>
      )}
    </>
  );
};

export default EventModal;
