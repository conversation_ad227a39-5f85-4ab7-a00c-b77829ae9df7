import React, { useCallback, useEffect, useState } from 'react';
import ColorPallette from './ColorPallette';
import IconButton from '@components/common/Button/IconButton';
import { useCurrentEditor } from '@tiptap/react';

export type PaletteType = null | 'text_color' | 'highlight';

const FontColor = ({ toolbarRef }: { toolbarRef: React.RefObject<HTMLDivElement> }) => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const [isOpenPallette, setIsOpenPallette] = useState(false);
  const [optionPosition, setOptionPosition] = useState({ top: 0, left: 0 });
  const [palletteType, setPalletteType] = useState<PaletteType>(null);

  const initPalletteProps = () => {
    setPalletteType(null);
    setIsOpenPallette(false);
  };

  const updateColorPallettePosition = useCallback(() => {
    if (!palletteType) return;
    let toolbarRect;
    if (palletteType === 'text_color') {
      toolbarRect = toolbarRef.current?.querySelector('.i_text_color')?.getBoundingClientRect();
    } else {
      toolbarRect = toolbarRef.current?.querySelector('.i_highlight')?.getBoundingClientRect();
    }
    setOptionPosition({ top: toolbarRect.bottom + 5, left: toolbarRect.left });
  }, [palletteType]);

  const togglePallette = (type: PaletteType) => {
    if (!isOpenPallette) {
      if (palletteType === type) {
        initPalletteProps();
      } else {
        updateColorPallettePosition();
        setPalletteType(type);
        setIsOpenPallette(true);
      }
    } else {
      if (palletteType !== type) {
        updateColorPallettePosition();
        setPalletteType(type);
      } else {
        initPalletteProps();
      }
    }
  };

  useEffect(() => {
    updateColorPallettePosition();
    window.addEventListener('resize', updateColorPallettePosition);
    window.addEventListener('scroll', updateColorPallettePosition);

    return () => {
      window.removeEventListener('resize', updateColorPallettePosition);
      window.removeEventListener('scroll', updateColorPallettePosition);
    };
  }, [updateColorPallettePosition]);

  // 팔레트 외부 클릭 시 닫히도록 설정
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isOpenPallette && toolbarRef.current && !toolbarRef.current.contains(event.target as Node)) {
        console.log('팔레트 외부 클릭');
        initPalletteProps();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [setIsOpenPallette, isOpenPallette]);
  return (
    <>
      <div className="item_box color">
        <IconButton
          text="text_color"
          icon="text_color"
          iconOnly
          fill="unfilled"
          size='smallest'
          color={editor.getAttributes('textStyle').color ? 'primary' : 'grayscale'}
          isActive={editor.getAttributes('textStyle').color ? true : false}
          onClick={() => {
            togglePallette('text_color');
          }}
        />
        <IconButton
          text="highlight"
          icon="highlight"
          iconOnly
          fill="unfilled"
          size='smallest'
          color={editor.isActive('highlight') ? 'primary' : 'grayscale'}
          isActive={editor.isActive('highlight')}
          onClick={() => {
            togglePallette('highlight');
          }}
        />
      </div>
      {isOpenPallette && (
        <ColorPallette
          setIsOpenPallette={setIsOpenPallette}
          optionPosition={optionPosition}
          palletteType={palletteType}
        />
      )}
    </>
  );
};

export default FontColor;
