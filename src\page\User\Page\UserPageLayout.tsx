import Loading from '@components/common/Loading';
import { Outlet } from 'react-router-dom';
import { useLoadingStore } from '@store/useLoadingStore';
import Title from '@components/common/Title/Title';

const UserPageLayout = () => {
  const { isLoading } = useLoadingStore();
  return (
    <div className="manage_admin_account">
      <Title />
      <Outlet />
      <Loading isLoading={isLoading} />
    </div>
  );
};

export default UserPageLayout;
