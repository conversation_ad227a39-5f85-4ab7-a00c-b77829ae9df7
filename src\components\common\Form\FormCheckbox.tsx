import Checkbox from '@components/common/Input/Checkbox';
import { Controller, useFormContext } from 'react-hook-form';

// 체크박스 폼 컴포넌트의 Props 인터페이스
interface FormCheckboxProps {
  name: string; // 폼 필드 이름
  rules?: any; // 유효성 검사 규칙
  label: string; // 체크박스 라벨
  hasLabel?: boolean; // 라벨 표시 여부
  // value: string | number; // 체크박스 값
  // checked?: boolean; // 체크 여부
  // onChange?: (value: number | string) => void; // 값 변경 핸들러
}

// 체크박스 폼 컴포넌트
const FormCheckbox: React.FC<FormCheckboxProps> = ({
  name,
  rules,
  label,
  // value,
  // onChange,
  // checked,
  hasLabel = true,
  ...attributes
}) => {
  // react-hook-form의 control만 사용
  const { control, setValue } = useFormContext();

  // useEffect(() => {
  //   setValue(name, value);
  // }, [value]);

  return (
    <Controller
      name={name}
      control={control}
      // defaultValue={value}
      rules={rules}
      render={({ field, fieldState }) => (
        <div className="c_checkbox_wrapper">
          <Checkbox
            {...field}
            {...attributes}
            label={label}
            hasLabel={hasLabel}
            value={field.value}
            checked={field.value === 'Y'}
            // checked={checked}
            // value={value}
            onChange={(e) => {
              // field 값 업데이트
              field.onChange(e.target.value === 'Y' ? 'N' : 'Y');
              // onChange가 있을 경우에만 호출
              // onChange?.(e.target.value);
            }}
            error={fieldState.error?.message}
          />
        </div>
      )}
    />
  );
};

export default FormCheckbox;
