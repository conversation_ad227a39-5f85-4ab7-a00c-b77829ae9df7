export const searchByKeywordAndKey = (data: any[], keyword = '', searchKey?: string): any[] => {
  if (!Array.isArray(data)) return [];

  return data.filter((item) => {
    // 특정 키값이 주어진 경우
    if (searchKey) {
      const value = item[searchKey];

      // 값이 문자열인 경우 직접 비교
      if (typeof value === 'string') {
        return value.toLowerCase().includes(keyword.toLowerCase());
      }

      // 값이 배열인 경우 재귀 호출
      if (Array.isArray(value)) {
        return searchByKeywordAndKey(value, keyword, searchKey).length > 0;
      }

      // 값이 객체인 경우 재귀 호출
      if (typeof value === 'object' && value !== null) {
        return searchByKeywordAndKey([value], keyword, searchKey).length > 0;
      }

      return null;
    }

    // 키값이 없는 경우 모든 값을 순회
    return Object.values(item).some((value) => {
      // 값이 문자열인 경우 직접 비교
      if (typeof value === 'string') {
        return value.toLowerCase().includes(keyword.toLowerCase());
      }

      // 값이 배열인 경우 재귀 호출
      if (Array.isArray(value)) {
        return searchByKeywordAndKey(value, keyword).length > 0;
      }

      // 값이 객체인 경우 재귀 호출
      if (typeof value === 'object' && value !== null) {
        return searchByKeywordAndKey([value], keyword).length > 0;
      }

      return null;
    });
  });
};
