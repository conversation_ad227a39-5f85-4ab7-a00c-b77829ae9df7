import joinClassName from '@utils/joinClassName';
import { ChoiceChipProps } from './types';

const ChoiceChip = ({ label, size = 'medium', active, icon, className, onClick }: ChoiceChipProps) => {
  const activeClass = active ? 'active' : '';
  const chipClass = joinClassName('c_chip', 'c_chip_choice', size, activeClass, icon, className);

  return (
    <div className={chipClass} onClick={onClick}>
      <span>{label}</span>
    </div>
  );
};

export default ChoiceChip;
