import { DateInfo } from '@page/User/Account/type';

export interface ServiceData {
  id: number;
  serviceGroupId: number;
  serviceGroupName: string;
  name: string;
  serviceDesc: string;
  url: string;
  method: string;
  useYn: string;
  dateInfo: DateInfo;
}

export interface ServiceGroupData {
  id: number | null;
  name: string;
  groupDesc: string;
  dateInfo: DateInfo;
}

export type ListPageConfirmType = 'edit' | 'delete' | null;

// Detail 페이지 타입
export type pageType = 'add' | 'edit';
