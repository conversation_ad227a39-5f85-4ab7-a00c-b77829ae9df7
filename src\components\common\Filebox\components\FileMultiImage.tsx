import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableCell from '@components/common/Table/TableCell';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import TableRow from '@components/common/Table/TableRow';
import joinClassName from '@utils/joinClassName';
import { useAlertStore } from '@store/useAlertStore';
import Alert from '@components/common/Modal/Alert';
import { useFilePreviewer } from '@components/common/Filebox/store/useFilePreviewer';
import FileImagePreview from './FileImagePreview';
import IconButton from '@components/common/Button/IconButton';
import { IconType } from '../../Button/types';
import { ResponseFile } from '../Filebox';

interface FileMultiProps {
  files: FileList;
  imageList: ResponseFile[];
  setImageList: (imageList: ResponseFile[]) => void;
  onDelete: (e: React.MouseEvent<HTMLButtonElement>, file: File) => void;
  handleAddClick: () => void;
  activeClass: string;
  readOnly?: boolean;
}

const FileMulti = ({
  files,
  imageList,
  setImageList,
  onDelete,
  handleAddClick,
  activeClass,
  readOnly,
}: FileMultiProps) => {
  const { setPreviewImageInfo, setPreviewModalState, previewModalState } = useFilePreviewer();

  const handlePreview = (e: React.MouseEvent<HTMLImageElement>, file: File) => {
    e.preventDefault();
    setPreviewImageInfo({
      name: file.name,
      url: URL.createObjectURL(file),
      size: (file.size / 1024).toFixed(2),
      type: file.name.split('.').pop(),
    });
    setPreviewModalState(true);
  };

  const handleDeleteImageList = (id: string) => {
    const filteredList = imageList.filter((img) => img.id !== id);
    setImageList(filteredList);
  };

  return (
    <>
      <div className={joinClassName('c_filebox_image_list', activeClass)}>
        {imageList &&
          imageList.map((image, index) => {
            return (
              <figure className="c_filebox_image_list_item" key={image.id + index}>
                <img
                  src={`/api/files/${image.id}`}
                  alt={image.name}
                  onClick={(e) => {
                    setPreviewImageInfo({
                      name: image.name,
                      url: `/api/files/${image.id}`,
                      size: (image.size / 1024).toFixed(2),
                      type: image.name.split('.').pop(),
                    });
                    setPreviewModalState(true);
                  }}
                />
                {!readOnly && (
                  <IconButton
                    text="삭제"
                    icon={'minus' as IconType}
                    iconOnly
                    design="circle"
                    color="grayscale"
                    fill="unfilled"
                    size="smallest"
                    onClick={() => handleDeleteImageList(image.id)}
                  />
                )}
              </figure>
            );
          })}
        {files &&
          Array.from(files).map((file, index) => {
            const previewImage = URL.createObjectURL(file);
            return (
              <figure className="c_filebox_image_list_item" key={file.name + index}>
                <img src={previewImage} alt={file.name} onClick={(e) => handlePreview(e, file)} />
                <IconButton
                  text="삭제"
                  icon={'minus' as IconType}
                  iconOnly
                  design="circle"
                  color="grayscale"
                  fill="unfilled"
                  size="smallest"
                  onClick={(e) => onDelete(e, file)}
                />
              </figure>
            );
          })}
        {!readOnly && (
          <div className="c_filebox_image_list_add_box" onClick={handleAddClick}>
            <IconButton
              icon={'camera' as IconType}
              iconOnly
              color="grayscale"
              fill="unfilled"
              size="largest"
              text="파일 추가"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAddClick();
              }}
            />
            <p className="desc">
              * 권장사이즈 440 x 550 (px)
              <br />
              최대사이즈 2000 x 2000 (px)
            </p>
            <b className="add_cover_text">
              {activeClass === 'active' ? '파일을 드랍해주세요.' : '파일을 드랍하거나 추가해 주세요.'}
            </b>
          </div>
        )}
      </div>
      {previewModalState && <FileImagePreview />}
    </>
  );
};

export default FileMulti;
