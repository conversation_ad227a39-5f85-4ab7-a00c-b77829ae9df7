import type { ComponentProps, JSXElementConstructor } from 'react';

/**
 * 스토리북 Meta 설정을 위한 문서용 타입입니다.
 * 실제 사용은 Meta<T>를 따르며, 이 타입은 설명 및 가이드 참고용입니다.
 */
export interface CustomStoryMeta<T extends keyof JSX.IntrinsicElements | JSXElementConstructor<any>> {
  /**
   * 스토리북 브라우저 사이드바에 표시될 컴포넌트 분류 경로입니다.
   * 예시: "Components/Button"
   */
  title: string;

  /**
   * 실제 렌더링할 컴포넌트입니다.
   * 예시: Button
   */
  component: T;

  /**
   * 해당 컴포넌트 스토리북 설정에 영향을 주는 전역 파라미터입니다.
   */
  parameters?: {
    /**
     * 스토리 레이아웃 설정
     * 예시: 'centered' | 'fullscreen' | 'padded'
     */
    layout?: 'centered' | 'fullscreen' | 'padded';

    /**
     * Docs 탭 관련 설정입니다. (문서 자동 생성 관련)
     */
    docs?: {
      /**
       * 컴포넌트 설명을 문서에 표시합니다.
       * 예시: { component: '이 컴포넌트는 버튼입니다.' }
       */
      description?: {
        component?: string;
      };

      /**
       * Docs 탭의 소스 코드 표기 방식
       * - 'code': 실제 코드 그대로
       * - 'dynamic': 실행 기반 코드
       */
      source?: {
        type?: 'code' | 'dynamic';
      };
    };

    /**
     * 배경색 설정 (preview 화면에서)
     */
    backgrounds?: {
      /**
       * 기본 배경 테마
       * 예시: 'light'
       */
      default?: string;

      /**
       * 선택 가능한 배경 테마 목록
       * 예시: [{ name: 'dark', value: '#000' }]
       */
      values?: { name: string; value: string }[];
    };

    /**
     * 반응형 테스트용 뷰포트 설정
     * - responsive: 기본 반응형
     * - iphone12, ipad, pixel, etc. (INITIAL_VIEWPORTS에서 지원하는 키 값 사용 가능)
     * 예시: { defaultViewport: 'mobile1' }
     *
     */
    viewport?: {
      defaultViewport?: string;
      [key: string]: any;
    };

    /**
     * 그 외 사용자 정의 파라미터
     */
    [key: string]: any;
  };

  /**
   * 문서 자동 생성이나 Storybook 내에서 필터링에 사용할 수 있는 태그입니다.
   * 예: ['autodocs']를 설정하면 Docs 탭이 자동 활성화됩니다.
   */
  tags?: string[];

  /**
   * 스토리에 공통 적용할 데코레이터 목록입니다.
   * 예시: [(Story) => <div style={{ padding: 20 }}><Story /></div>]
   */
  decorators?: ((Story: any) => JSX.Element)[];

  /**
   * 스토리북에서 props 제어 및 설명을 위한 설정입니다.
   */
  argTypes?: {
    [propName: string]: {
      /**
       * 해당 prop에 대한 설명 (Docs 탭에 표시됨)
       * 예시: '버튼 크기 설정'
       */
      description?: string;

      /**
       * props를 제어할 수 있는 컨트롤러 유형
       * 예시: { type: 'select' }
       */
      control?: {
        type:
          | 'text'
          | 'number'
          | 'boolean'
          | 'select'
          | 'radio'
          | 'inline-radio'
          | 'color'
          | 'object'
          | 'array'
          | 'file';
      };

      /**
       * select 또는 radio일 때 옵션 목록
       * 예시: ['sm', 'md', 'lg']
       */
      options?: any[];

      /**
       * 기타 고급 설정
       */
      [key: string]: any;
    };
  };

  /**
   * Story = {} 방식 (args만 사용하는 방식)
   * 스토리에서 기본으로 사용할 props 값
   * 예시: { size: 'md', disabled: false }
   */
  args?: Partial<ComponentProps<T>>;
}
