import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Input from '@components/common/Input/Input';
import Loading from '@components/common/Loading';
import Alert from '@components/common/Modal/Alert';
import Confirm from '@components/common/Modal/Confirm';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Title from '@components/common/Title/Title';
import ServiceGroupDetail from '@page/User/Service/Components/ServiceGroupDetail';
import ServiceGroupList from '@page/User/Service/Components/ServiceGroupList';
import ServiceGroupModal from '@page/User/Service/Components/ServiceGroupModal';
import ServiceList from '@page/User/Service/Components/ServiceList';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { searchByKeywordAndKey } from '@utils/searchByKeywordAndKey';
import { useEffect, useState } from 'react';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useLoadingStore } from '@store/useLoadingStore';

const UserServicePage = () => {
  // global state
  const { isLoading } = useLoadingStore();
  const { alertState } = useAlertStore();
  const { confirmState } = useConfirmStore();
  const {
    serviceGroupList,
    setServiceGroupList,
    setIsOpenServiceGroupModal,
    setModalType,
    originalServiceGroupList,
    serviceGroupDetail,
    setServiceGroupDetail,
  } = useServiceStore();

  // value
  const [serviceGroupName, setServiceGroupName] = useState(''); // 서비스 그룹 이름

  const handleServiceGroupName = (e: React.ChangeEvent<HTMLInputElement>) => {
    setServiceGroupName(e.target.value);
  };

  const handleRegisterServiceGroup = () => {
    setModalType('add');
    setIsOpenServiceGroupModal(true);
  };

  const handleModifyServiceGroup = () => {
    setModalType('edit');
    setIsOpenServiceGroupModal(true);
  };

  const handleSearchGroupByKeyword = () => {
    if (serviceGroupName === '') {
      setServiceGroupList(originalServiceGroupList);
      return;
    }
    const searchResult = searchByKeywordAndKey(serviceGroupList, serviceGroupName, 'name');
    setServiceGroupList(searchResult);
  };

  const handleKeydownSearchGroupByKeyword = (e: React.KeyboardEvent<HTMLButtonElement | HTMLInputElement>) => {
    e.key === 'Enter' && handleSearchGroupByKeyword();
  };

  useEffect(() => {
    setModalType(null);
    setServiceGroupDetail(null);
  }, []);

  return (
    <>
      <div className="manage_admin_service">
        <h1 className="c_title">
          <p className="c_title_text">사용자 서비스 관리</p>
        </h1>

        <ControlBox>
          <Input
            placeholder="서비스 그룹명 입력"
            value={serviceGroupName}
            onChange={handleServiceGroupName}
            onKeyDown={(e) => handleKeydownSearchGroupByKeyword(e)}
          />
          <Button
            text="조회"
            onClick={handleSearchGroupByKeyword}
            onKeyDown={(e) => handleKeydownSearchGroupByKeyword(e)}
          />
        </ControlBox>
        <div className="content horizontal">
          <div className="left_content vertical">
            <TableContainerHeader
              leftChildren={
                <h1 className="c_title">
                  <p className="c_title_text">서비스 그룹 목록</p>
                </h1>
              }
              rightChildren={<Button text="추가" onClick={handleRegisterServiceGroup} />}
            />
            <ServiceGroupList />
          </div>
          <div className="right_content vertical">
            {serviceGroupDetail && (
              <>
                <h1 className="c_title">
                  <p className="c_title_text">서비스 그룹 상세</p>
                </h1>

                <ServiceGroupDetail handleModifyServiceGroup={handleModifyServiceGroup} />
              </>
            )}
            <h1 className="c_title">
              <p className="c_title_text">서비스 목록</p>
            </h1>
            <ServiceList />
          </div>
        </div>
        <Loading isLoading={isLoading} />
      </div>
      <ServiceGroupModal />
      <Alert isOpenAlert={alertState.isOpen} children={alertState.content} onConfirm={alertState.onConfirm} />
      <Confirm
        isOpenConfirm={confirmState.isOpen}
        title="알림"
        children={confirmState.content}
        onLeftButton={confirmState.onCancel}
        onRightButton={confirmState.onConfirm}
        rightButtonText={confirmState.confirmType === 'delete' ? '삭제' : '확인'}
        isDelete={confirmState.confirmType === 'delete'}
      />
    </>
  );
};

export default UserServicePage;
