import DefaultModal from '@components/common/Modal/DefaultModal';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import Button from '@components/common/Button/Button';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import TextEditor from '@components/common/TextEditor/TextEditor';
import { useVOCStore } from '@page/VOC/store/useVOCStore';
import { useAlertStore } from '@store/useAlertStore';
import { addVOC } from '@api/admin/vocAPI';

interface VOCAddModalProps {
  getVOCListData: () => void;
}

const VOCAddModal = ({ getVOCListData }: VOCAddModalProps) => {
  const { isOpenVOCAddModal, setIsOpenVOCAddModal, categoryOptions } = useVOCStore();
  const { activeAlert } = useAlertStore();

  const methods = useForm({
    defaultValues: {
      category: '',
      title: '',
      content: '',
    },
  });

  const [VOCComment, setVOCComment] = useState<string>('');

  const handleSubmit = async (data: any) => {
    const parseData = {
      ...data,
      category: data.category.value || '',
      content: VOCComment,
    };

    const resultMessage = await addVOC({ data: parseData });
    if (resultMessage) {
      setIsOpenVOCAddModal(false);
      getVOCListData();
      activeAlert(resultMessage);
    }
  };

  return (
    <DefaultModal
      isOpenModal={isOpenVOCAddModal}
      className="voc_add_modal"
      title="VOC 등록"
      footer={false}
      setIsOpenModal={setIsOpenVOCAddModal}
    >
      <Form methods={methods} onSubmit={handleSubmit}>
        <TableContainer>
          <TableBody>
            <tr>
              <th>카테고리</th>
              <td>
                <FormSelectBox
                  name="category"
                  options={categoryOptions?.filter((option) => option.value !== '')}
                  placeholder="카테고리"
                />
              </td>
            </tr>
            <tr>
              <th>VOC 제목</th>
              <td>
                <FormInput name="title" placeholder="VOC 키워드 입력" />
              </td>
            </tr>
            <tr>
              <th colSpan={2}>VOC 내용</th>
            </tr>
            <tr>
              <td colSpan={2}>
                <TextEditor
                  name="content"
                  className="voc_text_editor_content"
                  onChange={setVOCComment}
                  content={VOCComment}
                />
              </td>
            </tr>
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button color="grayscale" text="취소" onClick={() => setIsOpenVOCAddModal(false)} />
          <Button type="submit" text="등록" />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default VOCAddModal;
