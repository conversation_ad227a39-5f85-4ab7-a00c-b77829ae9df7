import { Node, mergeAttributes } from "@tiptap/core";

export const Figure = Node.create({
  name: "figure",
  group: "block",
  content: "image caption?",
  defining: true,
  addAttributes() {
    return {
      style: {
        default: '',
      },
      class: {
        default: '',
      },
      id: {
        default: '',
      },
    };
  },
  parseHTML() {
    return [
      {
        tag: "figure",
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ["figure", mergeAttributes(HTMLAttributes), 0];
  },
});

export const Caption = Node.create({
  name: "caption",
  group: "block",
  content: "text*",
  addAttributes() {
    return {
      style: {
        default: '',
      },
      class: {
        default: '',
      },
      id: {
        default: '',
      },
      textContent: {
        default: '',
        parseHTML: (element) => {
          return element.textContent || '';
        },
      },
    };
  },
  parseHTML() {
    return [
      {
        tag: "figcaption",
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return ["figcaption", mergeAttributes(HTMLAttributes), 0];
  },
});
