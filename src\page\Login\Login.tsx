import Button from '@components/common/Button/Button';
import Checkbox from '@components/common/Input/Checkbox';
import Alert from '@components/common/Modal/Alert';
import Footer from '@components/layout/Footer';
import { useEffect, useState } from 'react';
import { useAlertStore } from '@store/useAlertStore';
import QRLogin from './QRLogin';
import useLoginAPI from '@api/common/useLoginAPI';
import Form from '@components/common/Form/Form';
import { useForm } from 'react-hook-form';
import FormInput from '@components/common/Form/FormInput';
import { useNavigate } from 'react-router-dom';
import { useEventStore } from '@store/useEventStore';
import { useLoginStore } from '@store/useLoginStore';

const Login = () => {
  const { handlePostLogin } = useLoginAPI();
  const navigate = useNavigate();
  const { setIsLogin } = useLoginStore();
  
  const methods = useForm({ defaultValues: { username: '', password: '' } });
  const { handleSubmit, setValue } = methods;
  
  const { alertState, initAlertState } = useAlertStore();
  const { setEventPopupData } = useEventStore();
  const [isRememberId, setIsRememberId] = useState(false);
  const [isOpenQRLoginModal, setIsOpenQRLoginModal] = useState(false);

  /**  로그인 버튼 클릭 */
  const handleLoginButton = (formData) => {
    if (alertState.isOpen) return; // alert 띄워져 있을 때 이벤트 전송 막기
    handlePostLogin(formData, isRememberId, setIsLogin);
  };

  // const handleOpenQRLoginModal = () => {
  //   setIsOpenQRLoginModal(true);
  // };

  useEffect(() => {
    const rememberedUsername = localStorage.getItem('loginUsername');

    if (rememberedUsername) {
      setValue('username', rememberedUsername);
      setIsRememberId(true);
    }
  }, []);

  useEffect(() => {
    const accessToken = localStorage.getItem('accessToken');
    if (accessToken) {
      // accessToken 이 유효한지 유효하지 않은지 검사필요
      // 유효하다면 홈으로 이동
      // 유효하지 않으면 login 페이지 유지
      window.location.href = '/myInfo';
    } else {
      setEventPopupData(null);
    }
  }, []);

  return (
    <div className="login">
      <div className="login_contents_wrapper">
        <h2 className="login_contents_title">BRIT NODE PLATFORM</h2>

        <div className="login_contents">
          <Form methods={methods} onSubmit={handleSubmit(handleLoginButton)}>
            <div className="login_contents_inputs">
              <div className="c_label_input login_input">
                <label htmlFor="id">ID</label>
                <FormInput
                  type="text"
                  id="id"
                  name="username"
                  placeholder="아이디를 입력해 주세요"
                  rules={{ required: 'ID를 입력해주세요' }}
                />
              </div>
              <div className="c_label_input login_input">
                <label htmlFor="pw">PASSWORD</label>
                <FormInput
                  type="password"
                  id="pw"
                  name="password"
                  placeholder="비밀번호를 입력해 주세요"
                  rules={{ required: 'Password를 입력해주세요' }}
                />
              </div>
            </div>
            <Checkbox
              label="아이디 저장하기"
              value="rememberId"
              checked={isRememberId}
              onChange={() => setIsRememberId((prev) => !prev)}
              hasLabel
              className="login_remember_id"
            />
            <div className="button_wrapper">
              {/* <Button className="login_button" text="QR 로그인" onClick={handleOpenQRLoginModal} /> */}
              <Button className="login_button" text="로그인" onClick={() => {}} type="submit" />
            </div>
          </Form>
        </div>
      </div>
      <Alert
        title="로그인 실패"
        children={alertState.content}
        isOpenAlert={alertState.isOpen}
        onConfirm={initAlertState}
      />
      <Footer />
      {isOpenQRLoginModal && (
        <QRLogin isOpenQRLoginModal={isOpenQRLoginModal} setIsOpenQRLoginModal={setIsOpenQRLoginModal} />
      )}
    </div>
  );
};

export default Login;
