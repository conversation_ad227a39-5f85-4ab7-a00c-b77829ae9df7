import Loading from '@components/common/Loading';
import Title from '@components/common/Title/Title';
import LNB from '@components/layout/Navigation/LNB';
import { useLoadingStore } from '@store/useLoadingStore';

const Main = ({ children }) => {
  const { isLoading } = useLoadingStore();

  return (
    <main className="c_main">
      <LNB />
      <div className="c_main_content">
        <Title />
        {children}
        <Loading isLoading={isLoading} />
      </div>
    </main>
  );
};

export default Main;
