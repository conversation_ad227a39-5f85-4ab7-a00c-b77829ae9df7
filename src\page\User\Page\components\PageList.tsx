import { useCodeStore } from '../store/useCodeStore';
import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import TableHeader from '@components/common/Table/TableHeader';
import { GroupCodeDataProps } from '../type';
import SubTitle from '@components/common/Title/SubTitle';
import { useConfirmStore } from '@store/useConfirmStore';
import { useAlertStore } from '@store/useAlertStore';
import { getPageList } from '@api/user/pageApi';
import { UseFormReturn } from 'react-hook-form';
import ListTable from '@components/common/Table/ListTable';
import { useEffect, useLayoutEffect, useState } from 'react';
import useEventBus from '@hooks/useEventBus';
import { Mode, Page } from '../type';

const PageList = () => {
  const [pageList, setPageList] = useState<Page[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      const data = await getPageList();
      setPageList(data);
    };

    fetchData();
  }, []);

  // 코드 스토어에서 필요한 상태와 액션을 가져옴
  const {
    groupCode, // 현재 선택된 그룹 코드 정보
    setGroupCode, // 그룹 코드 정보를 변경하는 함수
    groupCodeListData, // 그룹 코드 목록 데이터
    setGroupCodeListData, // 그룹 코드 목록 데이터를 설정하는 함수
    setGroupCodeDetailData, // 그룹 코드 상세 데이터를 설정하는 함수
  } = useCodeStore();

  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  /**
   * 그룹 코드 선택 시 실행되는 핸들러
   * @param code 선택된 그룹 코드 데이터
   */
  const handleGroupCodeSelect = (code: GroupCodeDataProps) => {
    setGroupCode({
      code: code.code,
      name: code.name,
      state: 'edit',
    });
  };

  /**
   * 삭제 모달을 여는 핸들러
   * @param e 이벤트 객체
   * @param code 삭제할 그룹 코드
   */
  const handleOpenDeleteModal = (e: React.MouseEvent, code: string) => {
    e.stopPropagation();
    setConfirmState({
      isOpen: true,
      title: '알림',
      confirmType: 'delete',
      content: (
        <>
          <hr />
          <br />
          코드 <br />
          <b className="text-red-500">{code}</b>
          <br /> <br /> <hr /> <br />
          그룹 코드를 삭제하시겠습니까?
        </>
      ),
      // onConfirm: async () => {
      //   const message = await deleteGroupCode({ code });
      //   const responseData = await getGroupCodeList({});
      //   setGroupCodeListData(responseData);
      //   setGroupCode?.({
      //     code: '',
      //     name: '',
      //     state: null,
      //   });
      //   setGroupCodeDetailData({
      //     name: '',
      //     code: '',
      //     desc: '',
      //   });
      //   activeAlert(message);
      //   initConfirmState();
      // },
      onCancel: initConfirmState,
    });
  };

  /**
   * 그룹 코드 추가 버튼 클릭 시 실행되는 핸들러
   */
  const handleAddGroupCode = () => {
    setGroupCode({
      code: '',
      name: '',
      state: 'add',
    });
  };

  const { eventBus } = useEventBus();
  const [mode, setMode] = useState<Mode>(undefined);
  const [selectedPageId, setSelectedPageId] = useState<Page['id']>(undefined);

  useEffect(() => {
    if (mode) {
      eventBus.emit('mode:change', mode);
    }
  }, [mode]);

  const onClickPage = (id) => {
    setMode('update');
    setSelectedPageId(id);
    eventBus.emit('pageId:change', id);
  };

  return (
    <>
      <TableContainerHeader
        leftChildren={<SubTitle>페이지 목록</SubTitle>}
        rightChildren={
          <Button text="추가" onClick={() => setMode('create')} clickLog={{ buttonSection: '페이지 목록' }} />
        }
      />
      <ListTable
        title="페이지명"
        list={pageList?.map((item) => (
          <div
            key={item.id}
            className={item.id === selectedPageId ? 'active' : ''}
            onClick={() => onClickPage(item.id)}
          >
            <span>{item.pageName}</span>
            <IconButton
              text="삭제"
              // onClick={(e: React.MouseEvent) => handleOpenDeleteModal(e, item.code)}
              design="circle"
              size="smallest"
              fill="unfilled"
              color="grayscale"
              icon="close"
              iconOnly
              clickLog={{ buttonSection: '페이지 목록' }}
            />
          </div>
        ))}
      />
    </>
  );
};

export default PageList;
