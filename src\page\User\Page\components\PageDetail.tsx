import { useEffect, useLayoutEffect, useState } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Button from '@components/common/Button/Button';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import FormInput from '@components/common/Form/FormInput';
import Form from '@components/common/Form/Form';
import FormTextarea from '@components/common/Form/FormTextarea';
import SubTitle from '@components/common/Title/SubTitle';
import { useConfirmStore } from '@store/useConfirmStore';
import { useCodeStore } from '@page/System/Code/store/useCodeStore';
import { addGroupCode, editGroupCode, getGroupCodeList } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import useEventBus from '@hooks/useEventBus';
import { Mode, Page } from '../type';
import { getPageDetail } from '@api/user/pageApi';
import SubPagesList from './SubPagesList';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import { useYnOptions, pageTypeOptions } from '@constants/options';

const PageDetail = () => {
  const { eventBus } = useEventBus();

  const [mode, setMode] = useState<Mode>(undefined);
  eventBus.on('mode:change', (newMode) => {
    setMode(newMode);
  });

  const formMethods = useForm({
    defaultValues: {
      pageName: '',
      pageTypeCode: {
        label: pageTypeOptions[0].label,
        value: pageTypeOptions[0].value,
      },
      pageFileName: '',
      pageDesc: '',
    },
  });

  const [pageData, setPageData] = useState<Page>(null);
  eventBus.on('pageId:change', async (pageId) => {
    const res = await getPageDetail(pageId);
    setPageData(res);
    formMethods.reset({
      pageName: pageData.pageName,
      pageTypeCode: {
        label: _.find(pageTypeOptions, { value: pageData.pageTypeCode }).label,
        value: pageData.pageTypeCode,
      },
      pageFileName: pageData.pageFileName,
      pageDesc: pageData.pageDesc,
    });
  });

  const {
    groupCode, // 현재 선택된 그룹 코드 정보
    groupCodeDetailData, // 그룹 코드의 상세 정보 데이터
    setGroupCode, // 그룹 코드 정보를 변경하는 함수
    setGroupCodeListData, // 그룹 코드 목록 데이터를 설정하는 함수
  } = useCodeStore();

  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  const [isOpenDetail, setIsOpenDetail] = useState<boolean>(false);

  // 그룹 코드 추가 함수
  const handleAddGroupCode = async (formData) => {
    const { data, message } = await addGroupCode({ formData });
    activeAlert(message);
    setGroupCode?.({
      code: data.code,
      name: data.name,
      state: 'edit',
    });
    const responseData = await getGroupCodeList({});
    setGroupCodeListData(responseData);
    initConfirmState();
  };

  // 그룹 코드 수정 함수
  const handleEditGroupCode = async (formData) => {
    const message = await editGroupCode({ code: groupCode.code, formData });
    if (message) {
      initConfirmState();
      activeAlert(message);
    }
  };

  /**
   * 그룹 코드 추가 모달을 여는 핸들러
   * @param formData 추가할 그룹 코드 데이터
   */
  const handleOpenAddModal = (formData) => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      confirmType: 'add',
      content: '그룹 코드를 추가하시겠습니까?',
      onConfirm: () => handleAddGroupCode(formData),
      onCancel: initConfirmState,
    });
  };

  /**
   * 그룹 코드 수정 모달을 여는 핸들러
   * @param formData 수정할 그룹 코드 데이터
   */
  const handleOpenEditModal = (formData) => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      confirmType: 'edit',
      content: '그룹 코드를 수정하시겠습니까?',
      onConfirm: () => {
        handleEditGroupCode(formData);
        setIsOpenDetail(false);
      },
      onCancel: initConfirmState,
    });
  };

  /**
   * 폼 제출 시 실행되는 핸들러
   * @param formData 폼 데이터
   */
  const handleSubmit = (formData) => {
    if (groupCode.state === 'add') {
      handleOpenAddModal(formData);
    } else {
      // 상세가 안열려 있을때는, 상세만 열어주는 역할
      if (!isOpenDetail) {
        setIsOpenDetail(true);
        return;
      }

      const parsedFormData = {
        name: formData['name'],
        desc: formData['desc'],
      };

      // 상세가 열려 있을때는, 수정 모달을 열어줌
      handleOpenEditModal(parsedFormData);
    }
  };

  // 그룹 코드 상세 데이터가 변경될 때마다 폼 데이터 업데이트
  // useEffect(() => {
  //   if (groupCodeDetailData) {
  //     reset({
  //       name: groupCodeDetailData.name || '',
  //       code: groupCodeDetailData.code || '',
  //       desc: groupCodeDetailData.desc || '',
  //     });
  //     setIsOpenDetail(false);
  //   }
  // }, [groupCodeDetailData]);

  const renderContent = () => {
    if (!mode) {
      return (
        <div className="no_result">
          페이지가 선택되지 않았습니다. <br />
          페이지를 선택하거나 추가해주세요.
        </div>
      );
    } else {
      return (
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={[
                {
                  title: '페이지명',
                  required: true,
                  contents:
                    mode === 'create' ? (
                      <FormInput
                        name="pageName"
                        wrapperClassName="full"
                        placeholder="페이지명 입력"
                        rules={{ required: '페이지명을 입력해주세요.' }}
                      />
                    ) : (
                      <>{pageData?.pageName}</>
                    ),
                },
                {
                  title: '페이지 유형 코드',
                  required: true,
                  contents: <FormSelectBox name="pageTypeCode" options={pageTypeOptions} />,
                },
              ]}
            />
            <TableBodyRow
              rowData={{
                title: '페이지 파일명',
                required: true,
                isFullWidth: true,
                contents: (
                  <FormInput
                    wrapperClassName="full"
                    name="pageFileName"
                    placeholder="파일명 입력"
                    rules={{ required: '파일명을 입력해주세요.' }}
                  />
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '페이지 설명',
                isFullWidth: true,
                contents: <FormTextarea name="pageDesc" placeholder="페이지 설명 입력" />,
              }}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '게시판 템플릿',
                  required: true,
                  contents: (
                    <FormSelectBox
                      name="delAbleYn"
                      options={[
                        { label: '가능', value: 'Y' },
                        { label: '불가능', value: 'N' },
                      ]}
                    />
                  ),
                },
              ]}
            />
            {mode === 'update' && (
              <>
                <TableBodyRow
                  rowData={[
                    {
                      title: '등록자',
                      contents: pageData?.dateInfo?.createUser,
                    },
                    {
                      title: '등록일자',
                      contents: pageData?.dateInfo?.createDate,
                    },
                  ]}
                />
                <TableBodyRow
                  rowData={[
                    {
                      title: '수정자',
                      contents: pageData?.dateInfo?.updateUser,
                    },
                    {
                      title: '수정일자',
                      contents: pageData?.dateInfo?.updateDate,
                    },
                  ]}
                />
              </>
            )}
          </TableBody>
        </TableContainer>
      );
    }
  };

  return (
    <>
      <Form className="vertical" methods={formMethods} onSubmit={handleSubmit}>
        <TableContainerHeader
          leftChildren={<SubTitle>페이지 상세</SubTitle>}
          rightChildren={
            groupCode.state && (
              <div className="group_code_detail_btn_wrapper">
                {isOpenDetail && (
                  <Button
                    className="cancel_btn"
                    color="grayscale"
                    text="취소"
                    onClick={() => {
                      setIsOpenDetail(false);
                    }}
                  />
                )}
                <Button
                  type="submit"
                  className="modify_btn"
                  text={`페이지 ${mode === 'create' ? '추가' : '수정'}`}
                  onClick={() => {}}
                />
              </div>
            )
          }
        />
        <div className="admin_code_detail_info_wrapper">{renderContent()}</div>
      </Form>
      {mode === 'update' ? <SubPagesList pageId={pageData?.id} /> : null}
      <SubPagesList pageId={pageData?.id} />
    </>
  );
};

export default PageDetail;
