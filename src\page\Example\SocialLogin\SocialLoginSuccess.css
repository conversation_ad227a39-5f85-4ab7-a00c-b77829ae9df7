/* 로그인 성공 페이지 컨테이너 */
.login-success-container {
    /* min-height: 100vh; */
    display: flex;
    align-items: center;
    justify-content: center;
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    padding: 2rem;
}

.login-success-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem;
    max-width: 500px;
    width: 100%;
    text-align: center;
}

/* 성공 헤더 */
.success-header {
    margin-bottom: 2.5rem;
}

.success-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2.5rem;
    color: white;
    font-weight: bold;
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.3);
}

.success-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    letter-spacing: -0.02em;
}

.success-header p {
    margin: 0;
    color: #666;
    font-size: 1.1rem;
    line-height: 1.5;
}

/* 사용자 정보 섹션 */
.user-info-section {
    margin-bottom: 2.5rem;
    text-align: left;
}

.user-info-section h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a1a1a;
    text-align: center;
}

.user-info-card {
    background: #f8f9fa;
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid #e9ecef;
}

/* 사용자 아바타 */
.user-avatar {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.user-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.avatar-placeholder {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: bold;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 사용자 정보 상세 */
.user-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.info-value {
    font-weight: 500;
    color: #212529;
    font-size: 0.9rem;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

.provider-badge {
    background: #e3f2fd;
    color: #1976d2;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* 소셜 계정 섹션 */
.social-accounts-section {
    margin-top: 1rem;
}

.social-account-item {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.social-account-item:last-child {
    margin-bottom: 0;
}

.social-account-info {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.social-provider-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.375rem 0.875rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    flex-shrink: 0;
    min-width: 60px;
    text-align: center;
}

.social-account-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.social-account-name {
    font-weight: 600;
    color: #212529;
    font-size: 0.9rem;
}

.social-account-id {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: 'Courier New', monospace;
}

.social-account-email {
    font-size: 0.8rem;
    color: #495057;
    font-style: italic;
}

/* 소셜 제공자별 배지 색상 */
.social-provider-badge:contains('google') {
    background: linear-gradient(135deg, #4285f4, #34a853);
}

.social-provider-badge:contains('naver') {
    background: linear-gradient(135deg, #03c75a, #02b351);
}

.social-provider-badge:contains('kakao') {
    background: linear-gradient(135deg, #fee500, #fdd835);
    color: #3c1e1e;
}

/* 액션 버튼 */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.primary-button, .secondary-button {
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    min-width: 120px;
}

.primary-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.secondary-button {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.secondary-button:hover {
    background: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 로딩 상태 */
.auth-callback-loading {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
    color: white;
}

/* .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} */

.auth-callback-loading p {
    font-size: 1.1rem;
    margin: 0;
}

/* 에러 상태 */
.auth-callback-error {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    /* background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); */
    color: white;
    text-align: center;
    padding: 2rem;
}

.auth-callback-error h2 {
    margin: 0 0 1rem 0;
    font-size: 2rem;
    font-weight: 700;
}

.auth-callback-error p {
    margin: 0 0 2rem 0;
    font-size: 1.1rem;
    max-width: 400px;
    line-height: 1.5;
}

.error-button {
    background: white;
    color: #ee5a24;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.error-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .login-success-container {
        padding: 1rem;
    }
    
    .login-success-card {
        padding: 2rem;
        border-radius: 16px;
    }
    
    .success-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }
    
    .success-header h2 {
        font-size: 1.5rem;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .primary-button, .secondary-button {
        width: 100%;
    }
    
    .user-avatar img, .avatar-placeholder {
        width: 60px;
        height: 60px;
    }
    
    .avatar-placeholder {
        font-size: 1.5rem;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .info-value {
        text-align: left;
        max-width: 100%;
    }

    .social-account-info {
        flex-direction: column;
        gap: 0.75rem;
    }

    .social-provider-badge {
        align-self: flex-start;
        min-width: auto;
    }

    .social-account-item {
        padding: 0.75rem;
    }
}

/* 다크 모드 지원 */
@media (prefers-color-scheme: dark) {
    .login-success-card {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .success-header h2 {
        color: #ffffff;
    }
    
    .user-info-card {
        background: #2d2d2d;
        border-color: #404040;
    }
    
    .info-label {
        color: #cccccc;
    }
    
    .info-value {
        color: #ffffff;
    }
    
    .info-row {
        border-bottom-color: #404040;
    }
    
    .secondary-button {
        background: #2d2d2d;
        color: #ffffff;
        border-color: #404040;
    }
    
    .secondary-button:hover {
        background: #404040;
    }

    .social-account-item {
        background: #2d2d2d;
        border-color: #404040;
    }

    .social-account-name {
        color: #ffffff;
    }

    .social-account-id {
        color: #cccccc;
    }

    .social-account-email {
        color: #cccccc;
    }
}
