# [Editor]

## Function
  1. Bold 
      - 영역 선택시: 해당 영역을 굵게
      - 영연 미선택시: 커서위치부터 쓰는 글씨를 굵게

  2. Italic  
      - 영역 선택시: 해당 영역을 기울게
      - 영연 미선택시: 커서위치부터 쓰는 글씨를 기울게

  3. Underline
      - 영역 선택시: 해당 영역에 밑줄
      - 영연 미선택시: 커서위치부터 쓰는 글씨부터 밑줄

  4. Strike 
      - 영역 선택시: 해당 영역에 취소선
      - 영연 미선택시: 커서위치부터 쓰는 글씨부터 취소선

  5. Remove
      - 영역 선택시: 해당 영역의 모든 글씨 효과를 취소
      - 영연 미선택시: 커서위치부터 쓰는 글씨의 모든 효과를 취소

  6. Font Color
      - 영역 선택시: 해당 영역의 글씨색을 변경
      - 영연 미선택시: 커서위치부터 쓰는 글씨색을 변경 

  7. Highlight
      - 영역 선택시: 해당 영역의 하이라이트 효과 적용
      - 영연 미선택시: 커서위치부터 쓰는 글씨부터 하이라이트효과 적용

  8. Font Family
      - 영역 선택시: 해당 영역의 글꼴을 변경
      - 영연 미선택시: 커서위치부터 쓰는 글씨의 글꼴을 변경

  9. Font Size
      - 영역 선택시: 해당 영역의 글씨 크기를 변경
      - 영연 미선택시: 커서위치부터 쓰는 글씨의 크기를 변경

  10. Indent
      - 영역 선택시: 선택한 영역의 들여쓰기
      - 영연 미선택시: 커서가 있는 위치의 p태그에 들여쓰기

  11. Outdent
      - 영역 선택시: 선택한 영역의 내어쓰기
      - 영연 미선택시: 커서가 있는 위치의 p태그에 들여쓰기

  12. BlockQuote
      - 영역 선택시: 해당 영역을 인용구로 변경
      - 영연 미선택시: 커서가 있는 위치를 인용구로 변경

  13. Image Embed
      - 커서가 있는 위치에 이미지를 삽입
      - 이미지 사이즈조절 가능
      - 캡션 삽입 가능
      - File 업로드시 별도의 API로 업로드 후, 주소를 리턴받아 src에 맵핑하는 형태
      - Url embed 가능
      
  14. Video Embed
      - 커서가 있는 위치에 비디오를 삽입
      - 비디오 사이즈조절 가능
      - 캡션 삽입 가능
      - File 업로드시 별도의 API로 업로드 후, 주소를 리턴받아 src에 맵핑하는 형태
      - Url embed 가능
        > youtube.com, youtu.be 글씨를 감지하면 youtube-nocookie.com으로 변환하여 삽입
        > 일반 비디오는 src에 url 맵핑

  15. Table
      - 커서가 있는 위치에 표를 삽입
      - 표의 크기 조절 가능
      - (Column, Row) Header 토글 가능
      - 셀 병합, 분리 가능
      - 방향을 선택(상,하,좌,우)하여 셀 삽입 가능
      - 특정 Column, Row 삭제 가능
      - 테이블 전체 삭제 가능
      
  16. Link
      - 영역 선택시: 해당 영역의 하이퍼링크를 설정 가능
      - 영연 미선택시
        > 특정 텍스트를 입력한경우, 특정 텍스트에 하이퍼링크를 개별로 설정 가능
        > 별도의 텍스트가 입력되지 않은 경우, url을 그대로 text 로 리턴
        > 쓰여지는 텍스트를 감지하여, https:// 혹은 http:// 를 포함할 경우 자동으로 링크로 변환

  17. List Item
      - Bullet, Num, Check 스타일의 리스트 생성
      - 종속성에 따라 자동으로 indent가 각각 적용 됨 ( Check 스타일 예외 )
      - 
      



## ShortKey
