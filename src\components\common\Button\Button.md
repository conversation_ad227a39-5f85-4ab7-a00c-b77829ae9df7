# 버튼에서 필요한 정의

1. 버튼의 색상 (color)

  - primary : 해당테마의 메인이 되는 색상
  - grayscale : 검정 => 흰색, 흰색 => 검정 사이의 색상
  - error | warning : 붉은계열의 색상

2. 버튼의 형태 (design)

  - default : border-radius 값이 들어간 4각형의 기본 형태의 버튼
  - capsule : border-radius 가 높의 절반씩 들어간 캡슐 형태의 버튼
  - circle : 동그라미 버튼
  - rect : border-radius 값이 없는 완전한 4각형 버튼
  - handle : panel, modal에서의 handle 버튼 (반드시 position 값과 함께 사용되어야함)

3. 버튼의 색상이 채워지는 형태 (fill)

  - filled : color에서 지정한 색상이 채워지는 형태
  - unfilled : 기본적으로 투명한 상태 (hover, active, disabled 에는 기본테마컬러가 적용된다)
  - outlined : border 에 색상이 채워지는 형태

4. 버튼의 위치값 (position)

  - icon이 text 와 같이 나올경우 icon 의 위치 값
  - design이 handle일 경우 Panel, Modal 의 위치 값을 받아 핸들의 위치를 적용

5. 아이콘 (iconType)
  
  - 해당하는 아이콘의 이름