import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import TableBody from '@components/common/Table/TableBody';
import TableRow from '@components/common/Table/TableRow';
import Button from '@components/common/Button/Button';
import { useSettingStore } from '@page/System/Setting/store/useSettingStore';
import { useSettingAPI } from '@page/System/Setting/hooks/useSettingAPI';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import RadioboxList from '@components/common/Input/RadioboxList';
import CheckboxList from '@components/common/Input/CheckboxList';
import SubTitle from '@components/common/Title/SubTitle';
import Form from '@components/common/Form/Form';
import Input from '@components/common/Input/Input';
import { CheckboxOptionProps } from '@components/common/Input/types';

const SettingList = () => {
  const { settingType, settingListData, setSettingTypesData, setSettingListData } = useSettingStore();
  const { getSettings, editSetting } = useSettingAPI();
  const [data, setData] = useState({});

  // 테이블 헤더 정의
  const tableHeader = ['설정명', '값'];

  useEffect(() => {
    getSettingsData()
  }, [settingType])

  useEffect(() => {
    if (settingListData) {
      const initData = {}
      settingListData.forEach(item => {
        initData[item.key] = item.selectedValues
      })

      setData(initData)
    }
  }, [settingListData]);

  const methods = useForm({ mode: 'onChange' });

  const getSettingsData = async () => {
    const data = await getSettings(settingType)
    setSettingListData(data)
  }

  const handleSubmit = () => {
    const settings = []
    
    Object.entries(data).map(([key, value]) => {
      settings.push({
        "key": key,
        "values": [...data[key]]
      })
    })

    const newData = {
      "settings": settings
    }

    utils.showConfirm("", async () => {
      const success = await editSetting(newData)

      if(success) {
        utils.showAlert("저장되었습니다.")
      }
    });
  }

  const changeData = (key, values) => {
    setData((prev) => ({
      ...prev,
      [key]: [...values]
    }))
  }

  return (
    <>
      <Form className="vertical" onSubmit={handleSubmit} methods={methods}>
        <TableContainerHeader
          leftChildren={<SubTitle>설정 정보</SubTitle>}
          rightChildren={
            <Button
                type="submit"
                text={'저장'}
                clickLog={{ buttonSection: '설정 저장' }}
              />
          }
        />
        <TableContainer>
          <colgroup>
            <col width={'20%'} />
            <col width={'80%'} />
          </colgroup>
          <TableHeader>
            <TableRow tag="th" cellData={tableHeader} />
          </TableHeader>
          <TableBody>
            {
              settingListData && settingListData.length > 0 && settingListData?.map((setting, index) => {
                  if(setting.inputType === 'TOGGLE') {
                    return (
                      <TableBodyRow
                        key={index}
                        rowData={[
                          {
                            title: setting.desc,
                            contents: (
                              <Button
                                text={setting.desc}
                                design="switch"
                                isActive={data[setting.key]?.[0] === "true"}
                                onClick={() => {
                                  changeData(setting.key, data[setting.key]?.[0] === "true" ? ["false"] : ["true"])
                                }}
                              />
                            ),
                          },
                        ]}
                      />
                    )
                  }
                  if(setting.inputType === 'RADIO') {
                    return (
                      <TableBodyRow
                        key={index}
                        rowData={[
                          {
                            title: setting.desc,
                            contents: (
                              <RadioboxList
                                options={setting.availableValues.map((value) => ({
                                  label: value,
                                  value: value,
                                }))}
                                hasLabel
                                value={data[setting.key]?.[0]}
                                direction="horizontal"
                                onChange={(e) => {
                                  changeData(setting.key, [e.target.value])
                                }}
                              />
                            ),
                          },
                        ]}
                      />
                    )
                  }

                  if(setting.inputType === 'SELECT') {
                    return (
                      <TableBodyRow
                        key={index}
                        rowData={[
                          {
                            title: setting.desc,
                            contents: (
                              <CheckboxList
                                options={setting.availableValues.map((value) => ({
                                  label: value,
                                  value: value,
                                }))}
                                checkedValues={data[setting.key]?.map((value) => ({
                                  label: value,
                                  value: value,
                                })) || []}
                                setCheckedValues={(values) => {
                                  const valueList = Array.isArray(values) ? values.map((v: CheckboxOptionProps) => v.value) : [];
                                  changeData(setting.key, valueList);
                                }}
                                hasLabel
                              />
                            ),
                          },
                        ]}
                      />
                    )
                  }
                  if(setting.inputType === 'INPUT') {
                    return (
                      <TableBodyRow
                        key={index}
                        rowData={[
                          {
                            title: setting.desc,
                            contents: (
                              <Input
                                placeholder={setting.desc}
                                value={data[setting.key]?.[0] || ''}
                                onChange={(e) => {
                                  changeData(setting.key, [e.target.value])
                                }}
                              />
                            ),
                          },
                        ]}
                      />
                    )
                  }
              })
            }
          </TableBody>
        </TableContainer>
      </Form>
    </>
  );
};

export default SettingList;
