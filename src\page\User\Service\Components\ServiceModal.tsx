import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormCheckbox from '@components/common/Form/FormCheckbox';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import FormTextarea from '@components/common/Form/FormTextarea';
import DefaultModal from '@components/common/Modal/DefaultModal';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { BodyRowProps } from '@page/Admin/Account/type';
import { useUserServiceAPI } from '@page/User/Service/hooks/useUserServiceAPI';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

interface ServiceFormData {
  serviceGroupId: { label: string; value: string | number } | null;
  name: string;
  serviceDesc: string;
  url: string;
  method: string;
  useYn: string;
}

// 테이블 행 컴포넌트
const BodyRow = ({ title = '', required, children }: BodyRowProps) => {
  return (
    <tr>
      <th>
        <span className={required ? 'required' : ''}>{title}</span>
      </th>
      <td colSpan={3}>{children}</td>
    </tr>
  );
};

const ServiceModal = () => {
  const { serviceGroupList, serviceGroupDetail, modalType, serviceDetail, isOpenServiceModal, setIsOpenServiceModal } = useServiceStore();
  const { addServiceData, updateServiceData } = useUserServiceAPI();

  const [serviceGroupOptionList, setServiceGroupOptionList] = useState([]);

  // React Hook Form 설정
  const methods = useForm<ServiceFormData>({
    defaultValues: {
      serviceGroupId: null,
      name: '',
      serviceDesc: '',
      url: '',
      method: '',
      useYn: 'Y',
    },
  });

  const { getValues, reset } = methods;

  // 폼 제출 핸들러
  const handleOnSubmit = (data: ServiceFormData) => {
    const parseData = {
      ...data,
      serviceGroupId: data.serviceGroupId?.value,
    };
    if (modalType === 'add') {
      addServiceData(parseData);
    } else {
      if (serviceDetail) {
        updateServiceData(serviceDetail.id, parseData);
      }
    }
  };

  useEffect(() => {
    setServiceGroupOptionList(serviceGroupList?.map((item) => ({ label: item.name, value: item.id })));
  }, [serviceGroupList]);

  // 수정 모드일 때 기존 데이터 설정
  useEffect(() => {
    if (modalType === 'edit') {
      if (serviceDetail) {
        reset({
          serviceGroupId: { label: serviceDetail.serviceGroupName, value: serviceDetail.serviceGroupId },
          name: serviceDetail.name,
          serviceDesc: serviceDetail.serviceDesc,
          url: serviceDetail.url,
          method: serviceDetail.method,
          useYn: serviceDetail.useYn,
        });
      }
    } else {
      reset({
        // 생성시, 상위 serviceGroupDetail 이 선택된 경우 기본 값 맵핑
        serviceGroupId: serviceGroupDetail ? { label: serviceGroupDetail.name, value: serviceGroupDetail.id } : serviceGroupOptionList?.[0],
        name: '',
        serviceDesc: '',
        url: '',
        method: '',
        useYn: 'Y',
      });
    }
  }, [serviceDetail, modalType, serviceGroupDetail, serviceGroupOptionList]);

  return (
    <DefaultModal
      className="service_confirm_modal"
      title={`서비스 ${modalType === 'add' ? '등록' : '수정'}`}
      isOpenModal={isOpenServiceModal}
      setIsOpenModal={setIsOpenServiceModal}
      footer={false}
    >
      <Form onSubmit={handleOnSubmit} methods={methods}>
        <TableContainer>
          <colgroup>
            <col width={'200px'} />
          </colgroup>
          <TableBody>
            <BodyRow title="서비스 그룹" required>
              {serviceGroupOptionList && (
                <FormSelectBox
                  name="serviceGroupId"
                  placeholder="전체"
                  options={serviceGroupOptionList}
                  rules={{ required: '그룹을 선택해주세요' }}
                />
              )}
            </BodyRow>
            <BodyRow title="서비스 명" required>
              <FormInput name="name" placeholder="서비스 명 입력" rules={{ required: '필수값입니다!' }} />
            </BodyRow>
            <BodyRow title="서비스 설명">
              <FormTextarea name="serviceDesc" placeholder="서비스 설명 입력" className="service_desc" />
            </BodyRow>
            <BodyRow title="서비스 URL" required>
              <FormInput name="url" placeholder="서비스 URL 입력" rules={{ required: '필수값입니다!' }} />
            </BodyRow>
            <BodyRow title="서비스 메소드" required>
              <FormInput name="method" placeholder="서비스 메소드 입력" rules={{ required: '필수값입니다!' }} />
            </BodyRow>
            {getValues('useYn') && (
              <BodyRow title="활성화" required>
                <FormCheckbox
                  name="useYn"
                  label="서비스 활성화"
                  rules={{ required: '필수값입니다!' }}
                />
              </BodyRow>
            )}
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button
            text="취소"
            color="grayscale"
            onClick={() => {
              setIsOpenServiceModal(false);
            }}
          />
          <Button type="submit" text="확인" onClick={() => { }} />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default ServiceModal;
