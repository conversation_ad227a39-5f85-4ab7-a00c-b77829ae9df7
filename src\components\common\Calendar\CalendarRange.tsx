import { useState, useRef, useEffect, HTMLAttributes } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { ko } from 'date-fns/locale';
import { addDays, addMonths } from 'date-fns';
import { createPortal } from 'react-dom';

import joinClassName from '@utils/joinClassName';
import { dateUtils } from '@utils/formatter';

import { IconType } from '@components/common/Button/types';
import IconButton from '@components/common/Button/IconButton';
import Button from '@components/common/Button/Button';
import Input from '@components/common/Input/Input';

export interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

interface CalendarRangeProps {
  selectedDate?: DateRange;
  minDate?: Date | null;
  maxDate?: Date | null;
  minTime?: Date | null;
  maxTime?: Date | null;
  showTime?: boolean;
  onChange?: (range: DateRange) => void;
  readOnly?: boolean;
  buttonDisabled?: boolean;
  desc?: string;
  maxPeriod?: number; // 최대 선택 가능한 기간(일)
}

// HTMLAttributes에서 onChange를 제외하고 나머지 속성만 상속
type CalendarRangeComponentProps = Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> & CalendarRangeProps;

const CalendarRange = ({
  selectedDate: initialDate = { startDate: null, endDate: null },
  minDate = null,
  maxDate = null,
  minTime = null,
  maxTime = null,
  showTime = false,
  onChange = () => {},
  readOnly = false,
  className = '',
  desc = '',
  buttonDisabled = false,
  maxPeriod,
  ...props
}: CalendarRangeComponentProps) => {
  const [dateRange, setDateRange] = useState<DateRange>(initialDate);
  const [isOpen, setIsOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const [calendarPosition, setCalendarPosition] = useState({ top: 0, left: 'auto' });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        calendarRef.current &&
        popupRef.current &&
        !calendarRef.current.contains(event.target as Node) &&
        !popupRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleDateChange = ([start, end]: [Date | null, Date | null]) => {
    // maxPeriod가 설정되어 있고 시작일이 선택된 경우
    if (maxPeriod && start && !end) {
      const maxEndDate = addDays(start, maxPeriod - 1);
      // maxDate가 있는 경우 maxEndDate와 비교하여 더 이른 날짜 선택
      const adjustedMaxDate = maxDate ? (maxEndDate > maxDate ? maxDate : maxEndDate) : maxEndDate;

      const newRange = { startDate: start, endDate: null };
      setDateRange(newRange);
      return;
    }

    // maxPeriod가 설정되어 있고 종료일이 선택된 경우
    if (maxPeriod && start && end) {
      const daysDiff = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      if (daysDiff > maxPeriod) {
        const newEndDate = addDays(start, maxPeriod - 1);
        const newRange = { startDate: start, endDate: newEndDate };
        setDateRange(newRange);
        onChange(newRange);
        !showTime && setIsOpen(false);
        return;
      }
    }

    const newRange = { startDate: start, endDate: end };
    setDateRange(newRange);

    if (start && end) {
      onChange(newRange);
      !showTime && setIsOpen(false);
    }
  };

  const toggleCalendar = () => {
    if (!readOnly) {
      setIsOpen(!isOpen);
    }
  };

  const formatDateRange = () => {
    if (!dateRange.startDate || !dateRange.endDate) return '기간 선택';
    const startD = dateUtils.format(dateRange.startDate, showTime ? 'yyyy.MM.dd HH:mm:ss' : 'yyyy.MM.dd');
    const endD = dateUtils.format(dateRange.endDate, showTime ? 'yyyy.MM.dd HH:mm:ss' : 'yyyy.MM.dd');
    return `${startD} - ${endD}`;
  };

  useEffect(() => {
    const ref = calendarRef.current;
    const popRef = popupRef.current;
    if (ref && popRef) {
      const setPosition = () => {
        const rect = ref.getBoundingClientRect();
        const popupRect = popRef.getBoundingClientRect();
        const isOverHalf = rect.left > window.innerWidth * 0.5;
        // 만약 캘린더의 위치가 화면 기준 50% 이상이면 오른쪽에 배치
        if (isOverHalf) {
          setCalendarPosition({ top: rect.bottom + 4, left: `${rect.left + rect.width - popupRect.width}px` });
        } else {
          setCalendarPosition({ top: rect.bottom + 4, left: `${rect.left}px` });
        }
      };

      setPosition();

      window.addEventListener('resize', setPosition);
      window.addEventListener('scroll', setPosition);

      return () => {
        window.removeEventListener('resize', setPosition);
        window.removeEventListener('scroll', setPosition);
      };
    }
  }, [isOpen]);


  return (
    <div className={joinClassName('c_calendar_wrapper', className)} ref={calendarRef} {...props}>
      <IconButton
        text={formatDateRange()}
        onClick={toggleCalendar}
        icon={'calendar' as IconType}
        fill="outlined"
        color="grayscale"
        className={joinClassName('c_date_button', isOpen && 'open')}
        disabled={readOnly || buttonDisabled}
      />

      {isOpen &&
        createPortal(
          <div
            ref={popupRef}
            className="c_calendar_popup"
            style={{ position: 'fixed', top: calendarPosition.top, left: calendarPosition.left }}
          >
            <DatePicker
              selected={dateRange.startDate}
              onChange={handleDateChange}
              startDate={dateRange.startDate}
              endDate={dateRange.endDate}
              selectsRange
              inline
              locale={ko}
              dateFormat={showTime ? 'yyyy.MM.dd HH:mm:ss' : 'yyyy.MM.dd'}
              monthsShown={2}
              minDate={minDate}
              maxDate={maxDate}
              minTime={minTime}
              maxTime={maxTime}
              renderCustomHeader={({ date: headerDate, decreaseMonth, increaseMonth, customHeaderCount }) => (
                <div className="c_calendar_header">
                  <IconButton
                    text="이전"
                    onClick={decreaseMonth}
                    iconOnly
                    fill="unfilled"
                    color="grayscale"
                    size="smallest"
                    icon="arrow_left"
                    disabled={customHeaderCount === 1}
                  />
                  <span>
                    {dateUtils.format(customHeaderCount === 0 ? headerDate : addMonths(headerDate, 1), 'yyyy년 M월')}
                  </span>
                  <IconButton
                    text="다음"
                    onClick={increaseMonth}
                    iconOnly
                    fill="unfilled"
                    color="grayscale"
                    size="smallest"
                    icon="arrow_right"
                    disabled={customHeaderCount === 0}
                  />
                </div>
              )}
            />
            {showTime && (
              <div className="c_calendar_time_wrapper">
                <Input
                  type="time"
                  value={dateRange.startDate?.toTimeString().slice(0, 5) || new Date().toTimeString().slice(0, 5)}
                  onChange={(e) => {
                    setDateRange({
                      ...dateRange,
                      startDate: new Date(dateRange.startDate?.toDateString() + ' ' + e.target.value),
                    });
                  }}
                />
                <Input
                  type="time"
                  value={dateRange.endDate?.toTimeString().slice(0, 5) || new Date().toTimeString().slice(0, 5)}
                  onChange={(e) => {
                    setDateRange({
                      ...dateRange,
                      endDate: new Date(dateRange.endDate?.toDateString() + ' ' + e.target.value),
                    });
                  }}
                />
              </div>
            )}
            {desc && <p className="c_calendar_desc">{desc}</p>}
            <div className="c_calendar_footer">
              <Button
                text="취소"
                color="grayscale"
                onClick={() => {
                  setDateRange({ startDate: null, endDate: null });
                  onChange({ startDate: null, endDate: null });
                  setIsOpen(false);
                }}
              />
              <Button
                text="확인"
                onClick={() => {
                  if (dateRange.startDate && dateRange.endDate) {
                    onChange(dateRange);
                    setIsOpen(false);
                  }
                }}
                color="primary"
              />
            </div>
          </div>,
          document.body
        )}
    </div>
  );
};

export default CalendarRange;
