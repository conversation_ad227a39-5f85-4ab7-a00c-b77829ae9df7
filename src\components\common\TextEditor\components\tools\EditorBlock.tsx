import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import DefaultModal from '@components/common/Modal/DefaultModal';
import SelectBox from '@components/common/SelectBox/SelectBox';
import { useCurrentEditor } from '@tiptap/react';
import { useState } from 'react';

const EditorBlock = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const [modalState, setModalState] = useState(false);
  const [selectedValue, setSelectedValue] = useState({ value: 'javascript', label: 'JavaScript' });

  const selectedOptions = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'html', label: 'HTML' },
    { value: 'css', label: 'CSS' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'text', label: 'Text' },
    { value: 'markdown', label: 'Markdown' },
  ];

  const openModal = () => {
    setModalState(true);
  };

  const handleBlockQuote = () => {
    editor.chain().focus().toggleBlockquote().run();
  };

  const handleCodeBlock = () => {
    editor.chain().focus().setCodeBlock({ language: selectedValue.value }).run();
    setModalState(false);
  };

  const initCodeBlock = () => {
    editor.chain().focus().toggleCodeBlock().run();
  };

  return (
    <>
      <div className="item_box block">
        <IconButton
          text="블럭"
          icon="blockquote"
          iconOnly
          size='smallest'
          fill={editor.isActive('blockquote') ? 'filled' : 'unfilled'}
          color={editor.isActive('blockquote') ? 'primary' : 'grayscale'}
          disabled={editor.isActive('codeBlock')}
          onClick={handleBlockQuote}
        />
        <IconButton
          text="코드블럭"
          icon="codeblock"
          iconOnly
          size='smallest'
          fill={editor.isActive('codeBlock') ? 'filled' : 'unfilled'}
          color={editor.isActive('codeBlock') ? 'primary' : 'grayscale'}
          disabled={editor.isActive('blockquote')}
          onClick={editor.isActive('codeBlock') ? initCodeBlock : openModal}
        />
      </div>
      <DefaultModal
        title="언어 선택"
        className="codeblock_modal"
        isOpenModal={modalState}
        setIsOpenModal={setModalState}
        onClickedDim={() => setModalState(false)}
        footer={<Button text="확인" onClick={handleCodeBlock} />}
      >
        <SelectBox
          options={selectedOptions}
          defaultValue={selectedValue}
          selectedValue={selectedValue}
          setSelectedValue={setSelectedValue}
        />
      </DefaultModal>
    </>
  );
};

export default EditorBlock;
