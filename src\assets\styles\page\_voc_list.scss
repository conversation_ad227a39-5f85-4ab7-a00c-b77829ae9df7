@use '@styles/utils/mixin' as m;

.voc {
  .voc_list {
    .c_table_container_header {
      .left_cont {
        @include m.flex(center, space-between);
        gap: 1rem;
      }
    }

    .td_title {
      span {
        text-align: left;
      }
    }
  }
}

.voc_status_card {
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: 0.75rem;
  &.OPEN {
    background-color: var(--confirm);
  }
  &.PENDING {
    background-color: var(--warning);
  }
  &.CLOSED {
    background-color: var(--red);
  }
  color: white;
}

.voc_add_modal {
  width: 700px;
  .c_table_wrapper {
    margin-bottom: 1.25rem;

    tbody {
      tr {
        td {
          text-align: left;
        }
      }
    }

    .c_input,
    .c_input_wrapper,
    .c_textarea,
    .c_selectbox {
      width: 100%;
    }
  }
  .button_wrapper {
    @include m.flex(center, right);
    gap: 0.25rem;
  }
}
