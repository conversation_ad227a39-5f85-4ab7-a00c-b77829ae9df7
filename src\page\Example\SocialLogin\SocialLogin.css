/* 소셜 로그인 컨테이너 */
.social-login-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #f0f0f0;
}

/* 제목 영역 */
.social-login-title {
    text-align: center;
    margin-bottom: 2rem;
}

.social-login-title h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    letter-spacing: -0.02em;
}

/* 로그인 폼 */
.login-form {
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    color: #d1d5db;
}

.form-group input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    background: #ffffff;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group input::placeholder {
    color: #9ca3af;
}

/* 로그인 버튼 */
.login-button {
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-button:hover:not(:disabled) {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.login-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
}

.login-button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.button-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 로그인 옵션 */
.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.remember-me input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.remember-me label {
    margin: 0;
    color: #d1d5db;
    cursor: pointer;
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* 에러 메시지 */
.login-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.login-error::before {
    content: '⚠️';
    font-size: 1rem;
}

/* 구분선 */
.social-login-divider {
    position: relative;
    margin: 1.5rem 0;
    text-align: center;
}

.social-login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e5e5e5;
    z-index: 1;
}

.social-login-divider span {
    background: #ffffff;
    padding: 0 1rem;
    color: #666;
    font-size: 0.875rem;
    position: relative;
    z-index: 2;
}

/* 버튼 컨테이너 */
.social-login-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

/* 소셜 로그인 버튼 기본 스타일 */
.social-login-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
    border: none;
    outline: none;
    min-height: 48px;
}

.social-login-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.social-login-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 아이콘 스타일 */
.provider-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    flex-shrink: 0;
}

.provider-icon svg {
    width: 100%;
    height: 100%;
}

/* 텍스트 스타일 */
.provider-text {
    flex: 1;
    text-align: center;
    font-weight: 500;
    letter-spacing: -0.01em;
}

/* 개별 제공자 스타일 */
.social-login-button.google {
    background: #ffffff;
    color: #3c4043;
    border: 1px solid #dadce0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.social-login-button.google:hover {
    background: #f8f9fa;
    border-color: #c6c6c6;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.social-login-button.naver {
    background: #03c75a;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(3, 199, 90, 0.3);
}

.social-login-button.naver:hover {
    background: #02b351;
    box-shadow: 0 4px 16px rgba(3, 199, 90, 0.4);
}

.social-login-button.kakao {
    background: #fee500;
    color: #3c1e1e;
    border: none;
    box-shadow: 0 2px 8px rgba(254, 229, 0, 0.3);
}

.social-login-button.kakao:hover {
    background: #fdd835;
    box-shadow: 0 4px 16px rgba(254, 229, 0, 0.4);
}

.social-login-button.default {
    background: #6c757d;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.social-login-button.default:hover {
    background: #5a6268;
    box-shadow: 0 4px 16px rgba(108, 117, 125, 0.4);
}

/* 로딩 상태 */
.social-login-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.social-login-loading p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

/* 반응형 디자인 */
@media (max-width: 480px) {
    .social-login-container {
        margin: 1rem;
        padding: 1.5rem;
        border-radius: 12px;
    }
    
    .social-login-title h3 {
        font-size: 1.25rem;
    }
    
    .social-login-button {
        padding: 0.75rem 1.25rem;
        font-size: 0.9rem;
        min-height: 44px;
    }
    
    .provider-text {
        font-size: 0.9rem;
    }
}

/* 접근성 개선 */
.social-login-button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.social-login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* 다크 모드 지원 */
@media (prefers-color-scheme: dark) {
    .social-login-container {
        background: #1a1a1a;
        border-color: #333;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }
    
    .social-login-title h3 {
        color: #ffffff;
    }
    
    .social-login-divider::before {
        background: #333;
    }
    
    .social-login-divider span {
        background: #1a1a1a;
        color: #ccc;
    }
    
    .social-login-loading p {
        color: #ccc;
    }
}
