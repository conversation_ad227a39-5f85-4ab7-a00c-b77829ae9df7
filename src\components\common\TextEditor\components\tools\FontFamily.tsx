import SelectBox, { OptionType } from '@components/common/SelectBox/SelectBox';
import { EditorConstant } from '@components/common/TextEditor/utils/constant';
import { useCurrentEditor } from '@tiptap/react';

const FontFamily = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const { FONT_FAMILIES } = EditorConstant;

  const getCurrentFontFamily = () => {
    const attrs = editor.getAttributes('textStyle');
    return FONT_FAMILIES.find((font) => font.value === attrs.fontFamily) || FONT_FAMILIES[0];
  };
  const handleFontFamilyChange = (selectedOption: OptionType) => {
    if (selectedOption.value === FONT_FAMILIES[0].value) {
      editor.chain().focus().unsetFontFamily().run();
    } else {
      editor
        .chain()
        .focus()
        .setFontFamily(selectedOption.value as string)
        .run();
    }
  };

  return (
    <div className="item_box fontfamily">
      <label>글꼴: &nbsp;</label>
      <SelectBox
        className="fontfamily"
        optionListClassName="fontfamily_select_list"
        options={FONT_FAMILIES}
        defaultValue={getCurrentFontFamily()}
        selectedValue={getCurrentFontFamily()}
        setSelectedValue={handleFontFamilyChange}
      />
    </div>
  );
};

export default FontFamily;
