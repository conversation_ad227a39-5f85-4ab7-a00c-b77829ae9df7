{"API_LOG": [{"headerName": "사용자 ID", "field": "loginId"}, {"headerName": "API 요청 시간", "field": "requestDate"}, {"headerName": "요청 처리 시간(ms)", "field": "requestDuration"}, {"headerName": "글로벌 추적 ID", "field": "globalTraceId"}, {"headerName": "호스트", "field": "host"}, {"headerName": "METHOD", "field": "method"}, {"headerName": "FULL URL", "field": "fullUrl"}, {"headerName": "URL 패턴", "field": "uriPattern"}, {"headerName": "패스 파라미터", "field": "pathParams"}, {"headerName": "쿼리스트링", "field": "queryString"}, {"headerName": "단말 정보", "field": "deviceType"}, {"headerName": "OS", "field": "osName"}, {"headerName": "브라우저", "field": "browser"}, {"headerName": "브라우저 버전", "field": "browserVersion"}, {"headerName": "사용자 IP", "field": "ip"}], "FRONT_LOG": [{"headerName": "사용자 ID", "field": "loginId"}, {"headerName": "사용자 동작 시간", "field": "actionDate"}, {"headerName": "글로벌 추적 ID", "field": "globalTraceId"}, {"headerName": "로그 유형", "field": "logType"}, {"headerName": "메뉴명", "field": "menuName"}, {"headerName": "버튼 영역", "field": "buttonSection"}, {"headerName": "버튼명", "field": "button"}, {"headerName": "FULL URL", "field": "fullUrl"}, {"headerName": "URL", "field": "url"}, {"headerName": "호스트", "field": "host"}, {"headerName": "이전페이지", "field": "referrer"}, {"headerName": "쿼리스트링", "field": "queryString"}, {"headerName": "기타 데이터", "field": "data"}, {"headerName": "단말 정보", "field": "deviceType"}, {"headerName": "OS", "field": "osName"}, {"headerName": "브라우저", "field": "browser"}, {"headerName": "브라우저 버전", "field": "browserVersion"}, {"headerName": "사용자 IP", "field": "ip"}], "LOGIN_LOG": [{"headerName": "사용자 ID", "field": "loginId"}, {"headerName": "로그인 일시", "field": "loginDate"}, {"headerName": "로그인 성공 여부", "field": "loginSuccessYn"}, {"headerName": "단말 정보", "field": "deviceType"}, {"headerName": "OS", "field": "osName"}, {"headerName": "브라우저", "field": "browser"}, {"headerName": "브라우저 버전", "field": "browserVersion"}, {"headerName": "사용자 IP", "field": "ip"}]}