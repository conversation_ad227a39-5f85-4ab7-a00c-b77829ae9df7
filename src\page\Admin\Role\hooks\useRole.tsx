import { ServiceData } from '@page/Admin/Service/type';
import { MenuData, PageAuthData, ParsedPageAuthData } from '../type';
import { ParsedServiceData } from '../type';

const useRole = () => {
  // 전체 메뉴 정렬 (sortNo 기준으로 순서 정렬)
  const sortMenuData = (menuList: MenuData[]): MenuData[] => {
    return menuList
      .map((menu) => {
        const updatedChildMenus = menu.childs ? sortMenuData(menu.childs) : [];
        return {
          ...menu,
          childs: updatedChildMenus,
        };
      })
      .sort((a, b) => a.sortNo - b.sortNo);
  };

  // 서비스 그룹별로 데이터 그룹화 (서비스 그룹별로 파싱
  const parseServiceData = (serviceList: ServiceData[]): ParsedServiceData[] => {
    // 서비스 그룹별로 데이터 그룹화
    const groupedServices = serviceList.reduce((acc, service) => {
      const groupId = service.serviceGroupId;
      if (!acc[groupId]) {
        acc[groupId] = {
          serviceGroupId: groupId,
          serviceGroupName: service.serviceGroupName,
          depth: 1,
          childs: [],
        };
      }
      acc[groupId].childs.push({
        id: service.id,
        name: service.name,
        depth: 2,
        serviceGroupId: service.serviceGroupId,
        serviceGroupName: service.serviceGroupName,
        serviceDesc: service.serviceDesc,
      });
      return acc;
    }, {});

    // 그룹화된 데이터를 배열로 변환
    return Object.values(groupedServices);
  };

  // 페이지 권한 목록 파싱
  const parsePageAuthListData = (pageAuthList: PageAuthData[]): ParsedPageAuthData[] => {
    return pageAuthList.map((pageAuth) => ({
      pageId: pageAuth.id,
      pageName: pageAuth.pageName,
      readRoleYn: false,
      regRoleYn: false,
      modRoleYn: false,
      delRoleYn: false,
    }));
  };

  return { sortMenuData, parseServiceData, parsePageAuthListData };
};

export default useRole;
