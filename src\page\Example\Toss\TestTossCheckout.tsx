import { useState, useEffect } from 'react';
import { loadTossPayments, ANONYMOUS } from '@tosspayments/tosspayments-sdk';
import api from '@api/api.ts';
import Button from '@components/common/Button/Button.tsx';

const TestTossCheckout = () => {
  const clientKey = 'test_gck_docs_Ovk5rk1EwkEbP0W43n07xlzm';
  const customerKey = 'YbX2HuSlsC9uVJW6NMRMj';

  const [amount, setAmount] = useState({
    currency: 'KRW',
    value: 1000,
  });
  const [ready, setReady] = useState(false);
  const [widgets, setWidgets] = useState(null);
  const [orderId, setOrderId] = useState(null);
  const [doneOrders, setDoneOrders] = useState([]);
  const [canceledOrders, setCanceledOrders] = useState([]);

  useEffect(() => {
    getDoneOrders();
    getCanceledOrders();
  }, []);

  // 1. 주문 번호 생성 및 결제 위젯 초기화
  useEffect(() => {
    // 주문번호 생성
    async function getOrderId() {
      const apiUrl = '/api/orders/pre-order';
      const response = await api.post(apiUrl);

      if (response.status == 200) {
        console.log('주문서 생성 완료, 주문번호 : ', response.data.data.orderId);
        setOrderId(response.data.data.orderId);
      }
    }

    // 토스페이먼츠 객체 생성
    async function fetchPaymentWidgets() {
      try {
        const tossPayments = await loadTossPayments(clientKey);

        // 회원 결제
        // @docs https://docs.tosspayments.com/sdk/v2/js#tosspaymentswidgets
        // const widgets = tossPayments.widgets({
        //   customerKey,
        // });

        // 비회원 결제
        const widgets = tossPayments.widgets({ customerKey: ANONYMOUS });

        setWidgets(widgets);
      } catch (error) {
        console.error('fetchPaymentWidgets() Error fetching payment widget:', error);
      }
    }

    getOrderId();
    fetchPaymentWidgets();
  }, [clientKey, customerKey]);

  // 2. 결제 위젯 생성. (결제 수단들과 약관이 존재하는 영역)
  useEffect(() => {
    async function renderPaymentWidgets() {
      if (widgets == null) {
        return;
      }

      // TODO: renderPaymentMethods, renderAgreement, requestPayment 보다 반드시 선행되어야 합니다.
      // 주문 금액
      await widgets.setAmount(amount);

      await Promise.all([
        // ------  결제 UI 렌더링 ------
        // @docs https://docs.tosspayments.com/sdk/v2/js#widgetsrenderpaymentmethods
        widgets.renderPaymentMethods({
          selector: '#payment-method',
          variantKey: 'DEFAULT',
        }),
        // ------  이용약관 UI 렌더링 ------
        // @docs https://docs.tosspayments.com/sdk/v2/js#widgetsrenderagreement
        widgets.renderAgreement({
          selector: '#agreement',
          variantKey: 'AGREEMENT',
        }),
      ]);

      setReady(true);
    }

    renderPaymentWidgets();
  }, [widgets]);

  // 3. 결제 진행중으로 상태 변경
  const pay = async () => {
    const apiUrl = `/api/orders/${orderId}/paying`;
    const response = await api.post(apiUrl, {
      amount: amount.value,
    });

    if (response.status == 200) {
      //우리쪽 서버에 정상적으로 orderId, amount를 보내면 결제 진행 요청 팝업 띄우기
      requestPayment();
    }
  };

  // 4. 실 결제 요청
  // 결제가 성공하면 suceessUrl로 보냄
  const requestPayment = async () => {
    try {
      // 결제를 요청하기 전에 orderId, amount를 서버에 저장하세요.
      // 결제 과정에서 악의적으로 결제 금액이 바뀌는 것을 확인하는 용도입니다.
      await widgets.requestPayment({
        orderId: orderId, // 고유 주문 번호
        orderName: '토스 티셔츠 외 2건',
        successUrl: window.location.origin + '/example/toss/success', // 결제 요청이 성공하면 리다이렉트되는 URL
        failUrl: window.location.origin + '/example/toss/failure', // 결제 요청이 실패하면 리다이렉트되는 URL
        customerEmail: '<EMAIL>',
        customerName: '김토스',
        // 가상계좌 안내, 퀵계좌이체 휴대폰 번호 자동 완성에 사용되는 값입니다. 필요하다면 주석을 해제해 주세요.
        // customerMobilePhone: "01012341234",
      });
    } catch (error) {
      // 에러 처리하기
      console.error(error);
    }
  };

  // 가격 수정
  const updateAmount = async (newAmount) => {
    setAmount({
      currency: amount.currency,
      value: newAmount,
    });
    await widgets.setAmount({
      currency: amount.currency,
      value: newAmount,
    });
  };

  // 결제 완료 주문 목록 조회
  const getDoneOrders = async () => {
    const apiUrl = '/api/orders/done-orders';
    const response = await api.get(apiUrl);

    if (response.status == 200) {
      setDoneOrders(response.data.data);
    }
  };

  // 결제 취소 완료 주문 목록 조회
  const getCanceledOrders = async () => {
    const apiUrl = '/api/orders/canceled-orders';
    const response = await api.get(apiUrl);

    if (response.status == 200) {
      setCanceledOrders(response.data.data);
    }
  };

  // 결제 취소
  const payCancel = async (doneOrderId) => {
    if (confirm(doneOrderId + ' : 해당 결제를 취소하시겠습니까?')) {
      const body = {
        pgType: 'TOSS',
        data: {
          cancelReason: doneOrderId + ' 주문 테스트 취소',
        },
      };

      const apiUrl = `/api/orders/${doneOrderId}/cancel`;
      const response = await api.post(apiUrl, body);

      if (response.status == 200) {
        if (response.data.code == 'SUCCESS') {
          alert('주문 취소에 성공하였습니다.');
          getDoneOrders();
          getCanceledOrders();
        } else {
          alert('주문 취소에 실패하였습니다!!');
        }
      }
    }
  };

  return (
    <div>
      <div>
        <h1 className="c_title">
          <p className="c_title_text">토스 결제 테스트</p>
        </h1>
        <h1 className="c_title">
          <p className="c_title_text">현재 선택 금액 : {amount.value}원</p>
        </h1>
        <div>
          {/* 결제 UI */}
          <div id="payment-method" />
          {/* 이용약관 UI */}
          <div id="agreement" />
        </div>
        <div style={{ marginTop: '30px', marginBottom: '30px' }}>
          <Button text="결제하기" onClick={() => pay()} disabled={!ready} />
        </div>
        <div style={{ display: 'flex' }}>
          <div style={{ marginRight: '10px' }}>
            <Button text="금액 올리기" onClick={() => updateAmount(amount.value + 100)} />
          </div>
          <div>
            <Button text="금액 내리기" color="red" onClick={() => updateAmount(amount.value - 100)} />
          </div>
        </div>
      </div>
      <hr style={{ marginTop: '30px', marginBottom: '30px' }} />
      <div>
        <h1 className="c_title">
          <p className="c_title_text">토스 테스트 결제 완료 주문목록</p>
        </h1>
        <div>
          <table>
            <thead>
              <th style={{ width: '100px', padding: '10px', border: '1px solid black' }}>주문취소</th>
              <th style={{ width: '100px', padding: '10px', border: '1px solid black' }}>주문상태</th>
              <th style={{ width: '100px', padding: '10px', border: '1px solid black' }}>가격</th>
              <th style={{ width: '400px', padding: '10px', border: '1px solid black' }}>주문번호</th>
              <th style={{ width: '200px', padding: '10px', border: '1px solid black' }}>생성일</th>
            </thead>
            <tbody>
              {doneOrders &&
                doneOrders.map((data, index) => (
                  <tr key={index}>
                    <td style={{ padding: '10px', border: '1px solid black' }}>
                      <Button text="주문취소" onClick={() => payCancel(data.id)} />
                    </td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.status}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.price}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.id}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.dateInfo.createDate}</td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </div>
      <hr style={{ marginTop: '30px', marginBottom: '30px' }} />
      <div>
        <h1 className="c_title">
          <p className="c_title_text">토스 테스트 결제 취소 완료 주문목록</p>
        </h1>

        <div>
          <table>
            <thead>
              <th style={{ width: '400px', padding: '10px', border: '1px solid black' }}>주문번호</th>
              <th style={{ width: '100px', padding: '10px', border: '1px solid black' }}>주문상태</th>
              <th style={{ width: '100px', padding: '10px', border: '1px solid black' }}>가격</th>
              <th style={{ width: '200px', padding: '10px', border: '1px solid black' }}>생성일</th>
              <th style={{ width: '200px', padding: '10px', border: '1px solid black' }}>취소완료일</th>
            </thead>
            <tbody>
              {canceledOrders &&
                canceledOrders.map((data, index) => (
                  <tr key={index}>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.id}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.status}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.price}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.dateInfo.createDate}</td>
                    <td style={{ padding: '10px', border: '1px solid black' }}>{data.dateInfo.updateDate}</td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TestTossCheckout;
