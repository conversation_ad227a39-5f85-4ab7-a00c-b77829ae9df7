@use '@styles/utils/mixin' as m;
@use '@styles/utils/colors_dark' as dc;
@use '@styles/utils/colors_light' as lc;

.c_filebox {
  .c_filebox_area {
    input[type='file'] {
      display: none;
    }
    .c_filebox_drop_area {
      position: relative;
      min-width: 300px;
      min-height: 150px;
      border-radius: 0.5rem;
      transition: all 0.3s ease-in-out;

      &::after {
        transition: all 0.3s ease-in-out;
        @include m.position_center();
        @include m.content_without_url(calc(100% - 20px), calc(100% - 20px), block);
        border-radius: 0.5rem;
      }

      &_desc {
        @include m.position_center();
        width: 100%;
      }
      &_icon {
        width: 30px;
        height: 30px;
        @include m.bg_set();
        margin: 0 auto;
      }
      &_text {
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
        margin-top: 0.5rem;
      }
    }

    .c_filebox_file_area {
      position: relative;
      @include m.flex(center, center);
      min-width: 300px;
      min-height: 150px;
      transition: all 0.3s ease-in-out;

      .c_filebox_preview {
        @include m.flex(center, center, column);
        padding: 1rem;
        gap: 0.5rem;

        &_image {
          min-width: 50px;
          max-width: 250px;
          position: relative;
          border-radius: 0.5rem;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            border-radius: 0.5rem;
            overflow: hidden;
            cursor: pointer;
          }
          .c_button_icon {
            position: absolute;
            top: 0;
            right: -28px;
            width: 20px;
            height: 20px;
            background-color: var(--g_03);
            border-radius: 50%;
            cursor: pointer;

            &:hover {
              background-color: var(--g_05);
            }
          }
        }

        &_desc {
          display: block;
          max-height: 0;
          opacity: 0;
          transition: all 0.3s ease-in-out;
        }

        &_name {
          position: relative;
          button {
            position: absolute;
            top: 0;
            right: 0;
            margin: 0;
          }
        }
      }
    }

    &.active {
      .c_filebox_drop_area {
        &::after {
          width: calc(100% - 0px);
          height: calc(100% - 0px);
          border: 0 !important;
        }
      }

      .c_filebox_preview {
        &_desc {
          max-height: 100px;
          padding: 1rem 0;
          opacity: 1;
        }
      }
    }
  }

  .c_filebox_list {
    position: relative;
    .add_cover {
      span {
        @include m.flex(center, center, column);
      }

      &_text {
        max-height: 0;
        opacity: 0;
        transition: all 0.3s ease-in-out;
      }
      &_init_text {
        max-height: 100px;
        opacity: 1;
        padding: 1rem 0;
        transition: all 0.3s ease-in-out;
      }
    }

    &.active {
      .add_cover {
        &_text {
          opacity: 1;
          max-height: 100px;
          padding: 1rem 0;
        }
        &_init_text {
          opacity: 0;
          max-height: 0;
          padding: 0;
        }
      }
    }

    tbody {
      tr {
        td {
          span {
            @include m.flex(center, center, row);
            gap: 0.5rem;
          }
          img {
            max-width: 32px;
            max-height: 32px;
            border-radius: 0.25rem;
            cursor: pointer;
          }
        }
      }
    }
  }

  .c_filebox_image_list {
    width: 100%;
    height: 100%;
    @include m.flex(start, start);
    flex-wrap: wrap;
    gap: 1rem;

    &_add_box {
      @include m.flex(center, center, column);
      width: 250px;
      height: 100%;
      min-height: 250px;
      padding: 1rem;
      gap: 1rem;
      border: 1px dashed var(--g_05);
      border-radius: 0.125rem;
      transition: all 0.3s ease-in-out;
      cursor: pointer;

      &:hover {
        background-color: var(--g_02);
      }
    }
    &_item {
      position: relative;
      width: 250px;
      height: 250px;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        object-position: center;
        border-radius: 0.125rem;
      }
      .i_minus {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        background-color: #000;
        &::before {
          background-image: url('@assets/images/icon/icon_minus_white.svg') !important;
        }
      }
    }

    @include m.bp_large {
      .c_filebox_image_list_add_box,
      .c_filebox_image_list_item {
        width: 200px;
        height: 200px;
      }
    }

    @include m.bp_medium {
      .c_filebox_image_list_add_box,
      .c_filebox_image_list_item {
        width: 175px;
        height: 175px;
      }
    }

    @include m.bp_small {
      .c_filebox_image_list_add_box,
      .c_filebox_image_list_item {
        width: 150px;
        height: 150px;
      }
    }
  }
}

.c_filebox_image_preview {
  @include m.position_center(fixed);
  z-index: 100002;
  img {
    max-height: 90vh;
    object-fit: contain;
    object-position: center;
    overflow: hidden;
  }
}

.preview_close {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--g_03);
  z-index: 100002;
}

[theme='dark'] {
  .c_filebox {
    .c_filebox_area {
      &.active {
        .c_filebox_drop_area {
          background-color: dc.$g_04;
          &::after {
            outline: 2px solid dc.$g_06;
          }
        }

        .c_filebox_file_area {
          background-color: dc.$g_04;
          &::after {
            outline: 2px solid dc.$g_06;
          }
        }
      }

      .c_filebox_drop_area {
        border: 2px solid dc.$g_06;

        &::after {
          border: 2px dashed dc.$g_06;
        }

        &_icon {
          background-image: url('@assets/images/icon/icon_download_white.svg');
        }
      }
    }
  }
}

[theme='light'] {
  .c_filebox {
    .c_filebox_area {
      &.active {
        .c_filebox_drop_area {
          background-color: lc.$g_02;
          &::after {
            outline: 2px solid lc.$g_04;
          }
        }
        .c_filebox_preview {
          background-color: lc.$g_02;
        }
      }

      .c_filebox_drop_area {
        border: 2px solid lc.$g_04;

        &::after {
          border: 2px dashed lc.$g_04;
        }

        &_icon {
          background-image: url('@assets/images/icon/icon_download_black.svg');
        }
      }
    }
  }
}
