import Image from '@tiptap/extension-image';
import captionBlack from '@assets/images/icon/icon_editor_add_alt_black.svg';
import alignLeftBlack from '@assets/images/icon/icon_editor_align_left_black.svg';
import alignCenterBlack from '@assets/images/icon/icon_editor_align_center_black.svg';
import alignRightBlack from '@assets/images/icon/icon_editor_align_right_black.svg';
import captionPrimary from '@assets/images/icon/icon_editor_add_alt_primary.svg';
import alignLeftPrimary from '@assets/images/icon/icon_editor_align_left_primary.svg';
import alignCenterPrimary from '@assets/images/icon/icon_editor_align_center_primary.svg';
import alignRightPrimary from '@assets/images/icon/icon_editor_align_right_primary.svg';
import { mergeAttributes } from '@tiptap/react';

export const ImageResize = Image.extend({
  // 이미지의 속성 추가
  addAttributes() {
    return {
      ...this.parent?.(),
      style: {
        default: 'width: auto; height: auto; max-width: 80vw;',
        parseHTML: (element) => {
          const width = element.getAttribute('width');
          // console.log(element, 'element');
          return width ? `width: ${width}px; height: auto; ` : `${element.style.cssText}`;
        },
      },
      caption: {
        default: '',
        parseHTML: (element) => {
          const figcaption = element.querySelector('figcaption');
          return figcaption?.textContent || '';
        },
      },
    };
  },

  renderHTML({ HTMLAttributes }) {
    const figcaption = HTMLAttributes.caption 
      ? ['figcaption', { class: 'image-caption', style: 'color: #9c9c9c; font-size: 12px; margin-top: 4px;' }, HTMLAttributes.caption]
      : ['figcaption', { class: 'image-caption', style: 'color: #9c9c9c; font-size: 12px; margin-top: 4px;' }, ''];

    return [
      'figure',
      { style: HTMLAttributes.style },
      [
        'img',
        mergeAttributes(HTMLAttributes),
      ],
      figcaption,
    ];
  },

  addNodeView() {
    return ({ node, editor, getPos }) => {
      const {
        view,
        options: { editable },
      } = editor;
      const { style, caption } = node.attrs; // 위의 추가된 속성을 불러옴
      const $wrapper = document.createElement('p');
      const $caption = document.createElement('figcaption');
      const $container = document.createElement('figure');
      const $img = document.createElement('img');
      const iconStyle = 'width: 24px; height: 24px; cursor: pointer;';

      const dispatchNodeView = () => {
        if (typeof getPos === 'function') {
          const newAttrs = {
            ...node.attrs,
            style: `${$img.style.cssText}`,
            caption: $caption.textContent || '',
          };
          view.dispatch(view.state.tr.setNodeMarkup(getPos(), null, newAttrs));
        }
      };

      const paintPositionController = () => {
        const $positionController = document.createElement('div');

        const $captionController = document.createElement('img');
        const $leftController = document.createElement('img');
        const $centerController = document.createElement('img');
        const $rightController = document.createElement('img');

        const controllerMouseOver = (e: MouseEvent, hoverSrc: string) => {
          (e.target as HTMLImageElement).src = hoverSrc;
        };

        const controllerMouseOut = (e: MouseEvent, src: string) => {
          (e.target as HTMLImageElement).src = src;
        };

        $positionController.setAttribute(
          'style',
          `position: absolute; top: 0%; left: 50%; min-width: 150px; padding: 10px; z-index: 98; background-color: #fff; border-radius: 4px; outline: 2px solid #6C6C6C; ${!editable ? '' : 'cursor: pointer;'} transform: translate(-50%, -50%); display: flex; justify-content: space-between; align-items: center;`
        );

        $captionController.setAttribute('src', captionBlack);
        $captionController.setAttribute('alt', 'image caption');
        $captionController.setAttribute('style', iconStyle);
        $captionController.addEventListener('mouseover', (e) => controllerMouseOver(e, captionPrimary));
        $captionController.addEventListener('mouseout', (e) => controllerMouseOut(e, captionBlack));

        $leftController.setAttribute('src', alignLeftBlack);
        $leftController.setAttribute('style', iconStyle);
        $leftController.setAttribute('alt', 'align left');
        $leftController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignLeftPrimary));
        $leftController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignLeftBlack));

        $centerController.setAttribute('src', alignCenterBlack);
        $centerController.setAttribute('alt', 'align center');
        $centerController.setAttribute('style', iconStyle);
        $centerController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignCenterPrimary));
        $centerController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignCenterBlack));

        $rightController.setAttribute('src', alignRightBlack);
        $rightController.setAttribute('alt', 'align right');
        $rightController.setAttribute('style', iconStyle);
        $rightController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignRightPrimary));
        $rightController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignRightBlack));

        $captionController.addEventListener('click', () => {
          const newCaption = window.prompt('Enter a caption for your image', $caption.textContent || '');
          if (newCaption === null) return;

          $caption.textContent = newCaption;
          dispatchNodeView();
        });

        $leftController.addEventListener('click', () => {
          $img.setAttribute('style', `${$img.style.cssText} margin: 0 auto 0 0;`);
          dispatchNodeView();
        });
        $centerController.addEventListener('click', () => {
          $img.setAttribute('style', `${$img.style.cssText} margin: 0 auto;`);
          dispatchNodeView();
        });
        $rightController.addEventListener('click', () => {
          $img.setAttribute('style', `${$img.style.cssText} margin: 0 0 0 auto;`);
          dispatchNodeView();
        });

        $positionController.appendChild($captionController);
        $positionController.appendChild($leftController);
        $positionController.appendChild($centerController);
        $positionController.appendChild($rightController);

        $container.appendChild($positionController);
      };

      $wrapper.setAttribute('style', `display: flex; max-width: 80vw;`);
      $wrapper.appendChild($container);

      $container.setAttribute('style', `${style}`);
      $container.appendChild($img);

      $caption.setAttribute('style', 'color: #9c9c9c; font-size: 12px; margin-top: 4px;');
      $caption.textContent = caption || '';
      $container.appendChild($caption);

      Object.entries(node.attrs).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        $img.setAttribute(key, value);
      });

      if (!editable) return { dom: $container };
      const isMobile = document.documentElement.clientWidth < 768;
      const dotPosition = isMobile ? '-8px' : '-4px';
      const dotsPosition = [
        `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,
        `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,
      ];

      let isResizing = false;
      let startX: number, startWidth: number;

      $container.addEventListener('click', (e) => {
        //remove remaining dots and position controller
        const isMobile = document.documentElement.clientWidth < 768;
        isMobile && (document.querySelector('.ProseMirror-focused') as HTMLElement)?.blur();

        if ($container.childElementCount > 3) {
          for (let i = 0; i < 5; i++) {
            $container.removeChild($container.lastChild as Node);
          }
        }

        paintPositionController();

        $container.setAttribute(
          'style',
          `position: relative; outline: 2px dashed #6C6C6C; ${style} ${editable ? 'cursor: pointer;' : ''}`
        );

        Array.from({ length: 4 }, (_, index) => {
          const $dot = document.createElement('div');
          $dot.setAttribute(
            'style',
            `position: absolute; width: ${isMobile ? 16 : 9}px; height: ${isMobile ? 16 : 9}px; outline: 1.5px solid #6C6C6C; border-radius: 50%; ${dotsPosition[index]}`
          );

          $dot.addEventListener('mousedown', (e) => {
            e.preventDefault();
            isResizing = true;
            startX = e.clientX;
            startWidth = $container.offsetWidth;

            const onMouseMove = (e: MouseEvent) => {
              if (!isResizing) return;
              const deltaX = index % 2 === 0 ? -(e.clientX - startX) : e.clientX - startX;

              const newWidth = startWidth + deltaX;

              $container.style.width = newWidth + 'px';

              $img.style.width = newWidth + 'px';
            };

            const onMouseUp = () => {
              if (isResizing) {
                isResizing = false;
              }
              dispatchNodeView();

              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          });

          $dot.addEventListener(
            'touchstart',
            (e) => {
              e.cancelable && e.preventDefault();
              isResizing = true;
              startX = e.touches[0].clientX;
              startWidth = $container.offsetWidth;

              const onTouchMove = (e: TouchEvent) => {
                if (!isResizing) return;
                const deltaX = index % 2 === 0 ? -(e.touches[0].clientX - startX) : e.touches[0].clientX - startX;

                const newWidth = startWidth + deltaX;

                $container.style.width = newWidth + 'px';

                $img.style.width = newWidth + 'px';
              };

              const onTouchEnd = () => {
                if (isResizing) {
                  isResizing = false;
                }
                dispatchNodeView();

                document.removeEventListener('touchmove', onTouchMove);
                document.removeEventListener('touchend', onTouchEnd);
              };

              document.addEventListener('touchmove', onTouchMove);
              document.addEventListener('touchend', onTouchEnd);
            },
            { passive: false }
          );
          $container.appendChild($dot);
        });
      });

      document.addEventListener('click', (e: MouseEvent) => {
        const $target = e.target as HTMLElement;
        const isClickInside = $container.contains($target) || $target.style.cssText === iconStyle;

        if (!isClickInside) {
          const containerStyle = $container.getAttribute('style');
          // 클릭 시 테두리 제거
          const newStyle = containerStyle?.replace('outline: 2px dashed #6C6C6C;', '');
          $container.setAttribute('style', newStyle as string);

          if ($container.childElementCount > 3) {
            for (let i = 0; i < 5; i++) {
              $container.removeChild($container.lastChild as Node);
            }
          }
        }
      });

      return {
        dom: $wrapper,
      };
    };
  },
});
