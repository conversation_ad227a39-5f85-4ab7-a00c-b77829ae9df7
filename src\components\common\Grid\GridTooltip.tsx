import { ITooltipParams } from 'ag-grid-community';
import { useThemeStore } from '@store/useThemeStore';

const { themeState } = useThemeStore.getState();

const tooltipStyle: React.CSSProperties = {
  backgroundColor: themeState === 'light' ? '#333333' : '#ffffff',
  opacity: 0.8,
  color: themeState === 'light' ? '#ffffff' : '#000000',
  padding: '0.375rem 0.625rem',
  borderRadius: '0.375rem ',
  fontSize: '0.8125rem',
  maxWidth: '18.75rem',
  whiteSpace: 'normal', // 텍스트 줄바꿈
  boxShadow: '0rem 0.125rem 0.375rem rgba(0, 0, 0, 0.3)',
};

const isValid = (params) =>
  _.every([
    typeof params.value !== 'boolean', // 값이 불값이여서 셀에 체크박스로 나올때 툴팁 생김 방지
    !params.api //편집중인 셀은 툴팁 없앰
      .getEditingCells()
      .some((cell) => cell.rowIndex === params.rowIndex && cell.column.getColId() === params.column.getColId()),
  ]);

const GridTooltip = (params: ITooltipParams) => {
  if (isValid(params)) return <div style={tooltipStyle}>{params.value}</div>;
};

export default GridTooltip;
