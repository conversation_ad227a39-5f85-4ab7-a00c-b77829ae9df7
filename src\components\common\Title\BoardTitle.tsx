import joinClassName from '@utils/joinClassName';
import { useEffect, useState } from 'react';
import { useMatches } from 'react-router-dom';
import { useMenuStore } from '@store/useMenuStore';

interface TitleProps {
  className?: string;
}

const BoardTitle = ({ className }: TitleProps) => {
  const matches = useMatches();
  const { myMenuList } = useMenuStore();

  const [breadCrumb, setBreadCrumb] = useState<string[]>([]);
  const [title, setTitle] = useState<string>('');

  const findBreadCrumb = (menuList, pathSegment) => {
    for (const menu of menuList) {
      if (menu.url === `${pathSegment}`) {
        return menu.name;
      }

      if (menu.childs && menu.childs.length > 0) {
        const childName = findBreadCrumb(menu.childs, pathSegment);
        if (childName) return childName;
      }
    }
  };

  const setBreadCrumbs = () => {
    let result = [];

    const matchList = matches.slice(1, matches.length);

    matchList.forEach((url, index) => {
      let pathname = url.pathname;
      if (index === matchList.length - 1) {
        pathname = url.params.id ? url.pathname.replace('/' + url.params.id, '') : url.pathname;
      }

      const findDepth = findBreadCrumb(myMenuList, pathname);

      if (findDepth) result.push(findDepth);
    });

    setBreadCrumb(result);
    setTitle(result.join(' '));
  };

  useEffect(() => {
    if (myMenuList.length === 0) return;
    setBreadCrumbs();
  }, [matches]);

  return (
    <h1 className={joinClassName('c_title', className)}>
      <p className="c_title_text">{title}</p>
      <p className="c_bread_crumb">
        <span>HOME</span>
        {breadCrumb.map((crumb, index) => (
          <span key={index}>{crumb}</span>
        ))}
      </p>
    </h1>
  );
};

export default BoardTitle;
