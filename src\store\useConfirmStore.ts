import { create } from 'zustand';

interface ConfirmStore {
  confirmState: {
    isOpen: boolean; // 모달 열림 여부
    confirmType: 'add' | 'edit' | 'delete' | null; // 확인 유형 (추가/수정/삭제)
    title?: string;
    className?: string;
    content: string | React.ReactNode; // 모달 내용
    onCancel: () => void; // 취소 버튼 클릭 시 실행될 함수
    onConfirm: (() => void) | ((data: any) => void); // 확인 버튼 클릭 시 실행될 함수
  };
  setConfirmState: (state: any) => void;
  initConfirmState: () => void;
}

export const useConfirmStore = create<ConfirmStore>((set) => ({
  confirmState: {
    isOpen: false,
    className: '',
    title: '알림',
    content: '',
    confirmType: null,
    onCancel: () => {},
    onConfirm: () => {},
  },
  setConfirmState: (state) =>
    set((prev) => ({ confirmState: typeof state === 'function' ? state(prev.confirmState) : state })),

  initConfirmState: () =>
    set({
      confirmState: {
        isOpen: false,
        className: '',
        title: '알림',
        content: '',
        confirmType: null,
        onCancel: () => {},
        onConfirm: () => {},
      },
    }),
}));
