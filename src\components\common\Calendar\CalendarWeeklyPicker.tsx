import { useState, useRef, useEffect, HTMLAttributes, FormEventHandler } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { ko } from 'date-fns/locale';
import { dateUtils } from '@utils/formatter';
import { createPortal } from 'react-dom';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import joinClassName from '@utils/joinClassName';
import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';

interface DateRange {
  startDate: Date | null;
  endDate: Date | null;
}

interface CalendarWeeklyPickerProps {
  selectedDate?: Date | null;
  maxDate?: Date | null;
  onChange?: (range: DateRange) => void;
  readOnly?: boolean;
  className?: string;
  desc?: string;
  buttonDisabled?: boolean;
  name?: string;
}

// HTMLAttributes에서 onChange를 제외하고 나머지 속성만 상속
type CalendarWeeklyPickerComponentProps = Omit<HTMLAttributes<HTMLDivElement>, 'onChange'> & CalendarWeeklyPickerProps;

const CalendarWeeklyPicker = ({
  selectedDate: initialDate = null,
  maxDate: customMaxDate = null,
  onChange = () => {},
  readOnly = false,
  className = '',
  desc = '',
  buttonDisabled = false,
  name = '',
  ...props
}: CalendarWeeklyPickerComponentProps) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(initialDate || new Date());
  const [dateRange, setDateRange] = useState<DateRange>({ startDate: null, endDate: null });
  const [isOpen, setIsOpen] = useState(false);
  const calendarRef = useRef<HTMLDivElement>(null);
  const popupRef = useRef<HTMLDivElement>(null);
  const [calendarPosition, setCalendarPosition] = useState({
    top: 0,
    left: 'auto',
  });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        calendarRef.current &&
        popupRef.current &&
        !calendarRef.current.contains(event.target as Node) &&
        !popupRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // 최대 선택 가능 날짜 계산 (이번주 일요일)
  const getMaxDate = () => {
    if (customMaxDate) return customMaxDate;
    const current = new Date();
    const monday = current.getDate() - current.getDay() + 1;
    const sunday = monday + 6;
    return new Date(current.setDate(sunday));
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    const weekRange = getWeekRange(date);
    setDateRange(weekRange);
    onChange(weekRange);
    setIsOpen(false);
  };

  const getWeekRange = (date: Date): DateRange => {
    const current = dayjs(date);

    if (current.day() === 0) {
      // 일요일인 경우
      const monday = current.subtract(6, 'day');
      return {
        startDate: monday.toDate(),
        endDate: current.toDate(),
      };
    } else {
      // 일요일이 아닌 경우
      const monday = current.startOf('week').add(1, 'day');
      const sunday = monday.add(6, 'day');
      return {
        startDate: monday.toDate(),
        endDate: sunday.toDate(),
      };
    }
  };

  const toggleCalendar = () => {
    if (!readOnly) {
      setIsOpen(!isOpen);
    }
  };

  const formatDateRange = () => {
    if (!dateRange.startDate || !dateRange.endDate) {
      const initialRange = getWeekRange(selectedDate || new Date());
      return `${dateUtils.format(initialRange.startDate)} - ${dateUtils.format(initialRange.endDate)}`;
    }
    return `${dateUtils.format(dateRange.startDate)} - ${dateUtils.format(dateRange.endDate)}`;
  };

  const calendarPopupStyle = {
    position: 'fixed',
    top: '100%',
    left: '0',
    width: '100%',
    zIndex: '1000',
  };

  useEffect(() => {
    const ref = calendarRef.current;
    const popRef = popupRef.current;
    if (ref && popRef) {
      const setPosition = () => {
        const rect = ref.getBoundingClientRect();
        const popupRect = popRef.getBoundingClientRect();
        const isOverHalf = rect.left > window.innerWidth * 0.5;
        // 만약 캘린더의 위치가 화면 기준 50% 이상이면 오른쪽에 배치
        if (isOverHalf) {
          setCalendarPosition({
            top: rect.bottom + 4,
            left: `${rect.left + rect.width - popupRect.width}px`,
          });
        } else {
          setCalendarPosition({
            top: rect.bottom + 4,
            left: `${rect.left}px`,
          });
        }
      };

      setPosition();

      window.addEventListener('resize', setPosition);
      window.addEventListener('scroll', setPosition);

      return () => {
        window.removeEventListener('resize', setPosition);
        window.removeEventListener('scroll', setPosition);
      };
    }
  }, [isOpen]);

  return (
    <div className={joinClassName('c_calendar_wrapper', className)} ref={calendarRef} {...props}>
      <IconButton
        text={formatDateRange()}
        onClick={toggleCalendar}
        icon={'calendar' as IconType}
        fill="outlined"
        color="grayscale"
        className={joinClassName('c_date_button', isOpen && 'open')}
        disabled={readOnly || buttonDisabled}
      />

      {isOpen &&
        createPortal(
          <div
            ref={popupRef}
            className="c_calendar_popup"
            style={{ position: 'fixed', top: calendarPosition.top, left: calendarPosition.left }}
          >
            <DatePicker
              selected={selectedDate}
              onChange={handleDateChange}
              inline
              locale={ko}
              dateFormat="yyyy.MM.dd"
              maxDate={getMaxDate()}
              renderCustomHeader={({ date: headerDate, decreaseMonth, increaseMonth }) => (
                <div className="c_calendar_header">
                  <IconButton
                    text="이전"
                    onClick={decreaseMonth}
                    iconOnly
                    fill="unfilled"
                    size='smallest'
                    color="grayscale"
                    icon="arrow_left"
                  />
                  <span>{dateUtils.format(headerDate, 'yyyy년 M월')}</span>
                  <IconButton
                    text="다음"
                    onClick={increaseMonth}
                    iconOnly
                    fill="unfilled"
                    color="grayscale"
                    size='smallest'
                    icon="arrow_right"
                  />
                </div>
              )}
            />
            {desc && <p className="c_calendar_desc">{desc}</p>}
            {/* <div className="c_calendar_footer">
              <Button
                text="취소"
                color="grayscale"
                onClick={() => {
                  setSelectedDate(initialDate);
                  onChange({ startDate: null, endDate: null });
                  setIsOpen(false);
                }}
              />
              <Button
                text="확인"
                onClick={() => {
                  if (dateRange.startDate && dateRange.endDate) {
                    onChange(dateRange);
                    setIsOpen(false);
                  }
                }}
                color="primary"
              />
            </div> */}
          </div>,
          document.body
        )}
    </div>
  );
};

CalendarWeeklyPicker.propTypes = {
  selectedDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  onChange: PropTypes.func,
  readOnly: PropTypes.bool,
  className: PropTypes.string,
  desc: PropTypes.string,
  buttonDisabled: PropTypes.bool,
};

export default CalendarWeeklyPicker;
