import { Option } from '@components/common/Form/FormCheckboxList';
import { DetailFormValues } from '@page/User/Account/type';
import { create } from 'zustand';

interface UserAccountDetailStore {
  // 상세 데이터 폼
  detailFormValues: DetailFormValues;

  // 권한 목록
  roleListData: Option[];
  setRoleListData: (data: Option[]) => void;

  // ID 중복 확인 여부
  checkDuplicateId: boolean;
  setCheckDuplicateId: (data: boolean) => void;
}

export const useUserAccountDetailStore = create<UserAccountDetailStore>((set) => ({
  detailFormValues: {
    userId: '',
    name: '',
    email: '',
    password: '',
    activeYn: 'Y',
    roleIds: [],
  },

  roleListData: [],
  setRoleListData: (data) => set((state) => ({ roleListData: data })),

  checkDuplicateId: false,
  setCheckDuplicateId: (data) => set((state) => ({ checkDuplicateId: data })),
}));
