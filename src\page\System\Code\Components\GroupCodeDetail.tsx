import { useEffect, useState } from 'react';
import { useForm, UseFormReturn } from 'react-hook-form';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import Button from '@components/common/Button/Button';
import TableContainer from '@components/common/Table/TableContainer';
import TableBody from '@components/common/Table/TableBody';
import FormInput from '@components/common/Form/FormInput';
import Form from '@components/common/Form/Form';
import FormTextarea from '@components/common/Form/FormTextarea';
import SubTitle from '@components/common/Title/SubTitle';
import { useConfirmStore } from '@store/useConfirmStore';
import { useCodeStore } from '@page/System/Code/store/useCodeStore';
import { addGroupCode, editGroupCode, getGroupCodeList } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';
import TableBodyRow from '@components/common/Table/TableBodyRow';

interface Props {
  searchMethods: UseFormReturn<any>;
}

const GroupCodeDetail = ({ searchMethods }: Props) => {
  const {
    groupCode, // 현재 선택된 그룹 코드 정보
    groupCodeDetailData, // 그룹 코드의 상세 정보 데이터
    setGroupCode, // 그룹 코드 정보를 변경하는 함수
    setGroupCodeListData, // 그룹 코드 목록 데이터를 설정하는 함수
  } = useCodeStore();

  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  // 폼 관련 메서드 초기화
  const methods = useForm({
    defaultValues: {
      name: '', // 그룹명 초기값
      code: '', // 그룹코드 초기값
      desc: '', // 그룹 코드 설명 초기값
    },
  });

  // 폼 리셋 함수
  const { reset } = methods;

  const [isOpenDetail, setIsOpenDetail] = useState<boolean>(false);

  // 그룹 코드 추가 함수
  const handleAddGroupCode = async (formData) => {
    const { data, message } = await addGroupCode({ formData });
    activeAlert(message);
    setGroupCode?.({
      code: data.code,
      name: data.name,
      state: 'edit',
    });
    const responseData = await getGroupCodeList({});
    setGroupCodeListData(responseData);
    initConfirmState();
  };

  // 그룹 코드 수정 함수
  const handleEditGroupCode = async (formData) => {
    const message = await editGroupCode({ code: groupCode.code, formData });
    if (message) {
      initConfirmState();
      activeAlert(message);
    }
  };

  /**
   * 그룹 코드 추가 모달을 여는 핸들러
   * @param formData 추가할 그룹 코드 데이터
   */
  const handleOpenAddModal = (formData) => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      confirmType: 'add',
      content: '그룹 코드를 추가하시겠습니까?',
      onConfirm: () => handleAddGroupCode(formData),
      onCancel: initConfirmState,
    });
  };

  /**
   * 그룹 코드 수정 모달을 여는 핸들러
   * @param formData 수정할 그룹 코드 데이터
   */
  const handleOpenEditModal = (formData) => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      confirmType: 'edit',
      content: '그룹 코드를 수정하시겠습니까?',
      onConfirm: () => {
        handleEditGroupCode(formData);
        setIsOpenDetail(false);
      },
      onCancel: initConfirmState,
    });
  };

  /**
   * 폼 제출 시 실행되는 핸들러
   * @param formData 폼 데이터
   */
  const handleSubmit = (formData) => {
    if (groupCode.state === 'add') {
      handleOpenAddModal(formData);
    } else {
      // 상세가 안열려 있을때는, 상세만 열어주는 역할
      if (!isOpenDetail) {
        setIsOpenDetail(true);
        return;
      }

      const parsedFormData = {
        name: formData['name'],
        desc: formData['desc'],
      };

      // 상세가 열려 있을때는, 수정 모달을 열어줌
      handleOpenEditModal(parsedFormData);
    }
  };

  // 그룹 코드 상세 데이터가 변경될 때마다 폼 데이터 업데이트
  useEffect(() => {
    if (groupCodeDetailData) {
      reset({
        name: groupCodeDetailData.name || '',
        code: groupCodeDetailData.code || '',
        desc: groupCodeDetailData.desc || '',
      });
      setIsOpenDetail(false);
    }
  }, [groupCodeDetailData]);

  return (
    <>
      <Form className="vertical" methods={methods} onSubmit={handleSubmit}>
        <TableContainerHeader
          leftChildren={<SubTitle>그룹 코드 상세</SubTitle>}
          rightChildren={
            groupCode.state && (
              <div className="group_code_detail_btn_wrapper">
                {isOpenDetail && (
                  <Button
                    className="cancel_btn"
                    color="grayscale"
                    text="취소"
                    onClick={() => {
                      setIsOpenDetail(false);
                    }}
                  />
                )}
                <Button
                  type="submit"
                  className="modify_btn"
                  text={`그룹 코드 ${groupCode.state === 'add' ? '추가' : '수정'}`}
                  onClick={() => {}}
                />
              </div>
            )
          }
        />
        <div className="admin_code_detail_info_wrapper">
          {!groupCodeDetailData?.code && groupCode.state !== 'add' ? (
            <div className="no_result">
              그룹 코드가 선택되지 않았습니다. <br />
              조회할 권한을 선택하거나 추가해주세요.
            </div>
          ) : (
            <TableContainer>
              <TableBody>
                <TableBodyRow
                  rowData={[
                    {
                      title: '그룹코드',
                      required: true,
                      contents: (
                        <>
                          {groupCode.state === 'add' ? (
                            <FormInput
                              wrapperClassName="full"
                              name="code"
                              placeholder="그룹코드를 입력해주세요."
                              rules={{ required: '그룹코드를 입력해주세요.' }}
                            />
                          ) : (
                            groupCodeDetailData?.code
                          )}
                        </>
                      ),
                    },
                    {
                      title: '그룹명',
                      required: true,
                      contents: (
                        <>
                          {groupCode.state === 'add' || isOpenDetail ? (
                            <FormInput
                              wrapperClassName="full"
                              name="name"
                              placeholder="그룹명 입력"
                              rules={{ required: '그룹명을 입력해주세요.' }}
                            />
                          ) : (
                            groupCodeDetailData?.name
                          )}
                        </>
                      ),
                    },
                  ]}
                />
                <TableBodyRow
                  rowData={{
                    title: '그룹 코드 설명',
                    isFullWidth: true,
                    contents: (
                      <>
                        {groupCode.state === 'add' || isOpenDetail ? (
                          <FormTextarea name="desc" placeholder="그룹 코드 설명 입력" />
                        ) : (
                          groupCodeDetailData?.desc
                        )}
                      </>
                    ),
                  }}
                />
                {groupCode.state !== 'add' && isOpenDetail && (
                  <>
                    <TableBodyRow
                      rowData={[
                        {
                          title: '등록자',
                          contents: groupCodeDetailData?.dateInfo?.createUser,
                        },
                        {
                          title: '등록일자',
                          contents: groupCodeDetailData?.dateInfo?.createDate,
                        },
                      ]}
                    />
                    <TableBodyRow
                      rowData={[
                        {
                          title: '수정자',
                          contents: groupCodeDetailData?.dateInfo?.updateUser,
                        },
                        {
                          title: '수정일자',
                          contents: groupCodeDetailData?.dateInfo?.updateDate,
                        },
                      ]}
                    />
                  </>
                )}
              </TableBody>
            </TableContainer>
          )}
        </div>
      </Form>
    </>
  );
};

export default GroupCodeDetail;
