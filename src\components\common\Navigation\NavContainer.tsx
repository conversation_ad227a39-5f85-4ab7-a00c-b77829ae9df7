import NavItem, { NavItemProps } from './NavItem';
import joinClassName from '@utils/joinClassName';
import { useEffect, useState } from 'react';
import { useLayoutStore } from '@store/useLayoutStore';
import { Menu } from 'types/adminMenuType';

interface NavContainerProps {
  menuList: Menu[];
  depth?: number;
}

const NavContainer = ({ menuList, depth = 0 }: NavContainerProps) => {
  const navClass = depth === 0 ? 'c_nav' : 'c_child_nav';
  const [isOpenChildMenuId, setIsOpenChildMenuId] = useState<string | number | null>(null);
  const { lnbState } = useLayoutStore();

  useEffect(() => {
    if (!lnbState) {
      setIsOpenChildMenuId(null);
    }
  }, [lnbState]);

  return (
    <ul className={joinClassName(navClass, depth)}>
      {menuList &&
        menuList.map((item, index) => {
          return (
            <NavItem
              tag="li"
              id={item.id}
              key={item.url + item.name + index}
              text={item.name}
              icon={item.icon}
              url={item.url}
              childs={item.childMenus}
              depth={item.depth}
              isOpenChildMenuId={isOpenChildMenuId}
              setIsOpenChildMenuId={setIsOpenChildMenuId}
            />
          );
        })}
    </ul>
  );
};

export default NavContainer;
