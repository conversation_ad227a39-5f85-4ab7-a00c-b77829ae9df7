import Checkbox from '@components/common/Input/Checkbox';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import { ParsedServiceData } from '@page/User/Role/type';
import { Fragment, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';

interface ServiceAuthListProps {
  serviceListData: ParsedServiceData[];
}

const ServiceAuthList = ({ serviceListData }: ServiceAuthListProps) => {
  const { getValues, setValue, watch } = useFormContext();
  const serviceIds = watch('serviceIds');

  // 서비스 체크박스 변경 처리 함수
  const handleServiceCheckboxChange = (serviceId: number) => {
    const serviceCheckedList = getValues('serviceIds');
    if (serviceCheckedList) {
      const updatedServiceCheckedList = serviceCheckedList.includes(serviceId)
        ? serviceCheckedList.filter((id) => id !== serviceId)
        : [...serviceCheckedList, serviceId];

      setValue('serviceIds', updatedServiceCheckedList, { shouldDirty: true });
    }
  };

  // 모든 서비스 ID를 재귀적으로 수집하는 함수
  const getAllServiceIds = (services: ParsedServiceData[]): number[] => {
    return services.reduce((acc: number[], service) => {
      const ids = service.depth !== 1 ? [service.id] : [];  // depth 1 제외
      if (service.childs && service.childs.length > 0) {
        ids.push(...getAllServiceIds(service.childs));
      }
      return [...acc, ...ids];
    }, []);
  };

  // 전체 서비스 체크박스 변경 처리 함수
  const handleAllServiceCheckboxChange = () => {
    const allServiceIds = getAllServiceIds(serviceListData);
    const currentCheckedList = getValues('serviceIds');

    if (currentCheckedList?.length === allServiceIds.length) {
      setValue('serviceIds', [], { shouldDirty: true });
    } else {
      setValue('serviceIds', allServiceIds, { shouldDirty: true });
    }
  };

  // 서비스 목록 태그 재귀적으로 렌더링
  const renderServiceList = (serviceList: ParsedServiceData[], depth: number = 1) => {
    return serviceList.map((service: ParsedServiceData) => (
      <Fragment key={service.id}>
        <tr>
          {service.depth !== 1 && (
            <td>
              <Checkbox
                value={service.id}
                checked={serviceIds?.includes(service.id) ?? false}
                onChange={() => handleServiceCheckboxChange(service.id)}
                label={`${service.name} 체크박스`}
              />
            </td>
          )}
          {service.depth !== 1 ? (
            <td style={{ paddingLeft: `${depth * 5}px` }}>
              {service.name}
            </td>
          ) : (
            <th colSpan={2}>{service.name}</th>
          )}
        </tr>
        {service.childs && service.childs.length > 0 && renderServiceList(service.childs, depth + 1)}
      </Fragment>
    ));
  };

  return (
    <TableContainer>
      <colgroup>
        <col style={{ width: '5%' }} />
        <col style={{ width: '95%' }} />
      </colgroup>
      <TableHeader sticky>
        <tr>
          <th>
            <Checkbox
              value="전체 선택"
              checked={serviceIds?.length > 0 && serviceIds?.length === getAllServiceIds(serviceListData).length}
              onChange={handleAllServiceCheckboxChange}
              hasLabel={false}
              label="전체 선택"
            />
          </th>
          <th>서비스 리스트</th>
        </tr>
      </TableHeader>
      <TableBody>
        {serviceListData ? (
          renderServiceList(serviceListData)
        ) : (
          <tr className="no_result">
            <td colSpan={2}>조회된 데이터가 없습니다.</td>
          </tr>
        )}
      </TableBody>
    </TableContainer>
  );
};

export default ServiceAuthList;
