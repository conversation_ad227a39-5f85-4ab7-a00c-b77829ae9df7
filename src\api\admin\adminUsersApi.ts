import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';

/**
 * 내 정보 조회
 */

export const getMyInfo = async (isGetErrorMsg: boolean = false) => {
  try {
    const apiUrl = `/api-admin/my/my-info`;
    const response = await api.get(apiUrl);

    return response.data.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '내 정보 조회');
  }
};

/**
 * 내 정보 수정(이름)
 */

interface UpdateMyInfoProps {
  name: string;
  activeYn: string;
  roleIds: number[];
  isGetErrorMsg?: boolean;
}

export const updateMyInfo = async ({ name, activeYn, roleIds, isGetErrorMsg = false }: UpdateMyInfoProps) => {
  try {
    const data = {
      name,
      activeYn,
      roleIds,
    };

    const apiUrl = `/api-admin/my/change-info`;
    const response = await api.post(apiUrl, data);

    return response;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '내 정보 수정(이름)');
  }
};

/**
 * 내 정보 수정(비밀번호)
 */

interface UpdateMyInfoPwProps {
  id: number;
  password: string;
  passwordConfirm: string;
  isGetErrorMsg?: boolean;
}

export const updateMyInfoPw = async ({ id, password, passwordConfirm, isGetErrorMsg = false }: UpdateMyInfoPwProps) => {
  try {
    const data = {
      password,
      passwordConfirm,
    };

    const apiUrl = `/api-admin/my/change-password`;
    const response = await api.post(apiUrl, data);

    return response;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '내 정보 수정(비밀번호)');
  }
};
