import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import FormTextarea from '@components/common/Form/FormTextarea';
import DefaultModal from '@components/common/Modal/DefaultModal';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useAlertStore } from '@store/useAlertStore';
import useService from '@page/Admin/Service/hooks/useService';
import { addServiceData, updateServiceData } from '@api/admin/serviceAPI';
import TableBodyRow from '@components/common/Table/TableBodyRow';
interface ServiceFormData {
  serviceGroupId: { label: string; value: string | number } | null;
  name: string;
  serviceDesc: string;
  url: string;
  method: { label: string; value: string };
  useYn: { label: string; value: string };
}

const ServiceModal = () => {
  const { serviceGroupList, serviceGroupDetail, modalType, serviceDetail, isOpenServiceModal, setIsOpenServiceModal } =
    useServiceStore();
  const { activeAlert } = useAlertStore();
  const { handleGetServiceList } = useService();

  const [serviceGroupOptionList, setServiceGroupOptionList] = useState([]);

  // React Hook Form 설정
  const methods = useForm<ServiceFormData>({
    defaultValues: {
      serviceGroupId: { label: '전체', value: '' },
      name: '',
      serviceDesc: '',
      url: '',
      method: { label: 'GET', value: 'GET' },
      useYn: { label: '활성', value: 'Y' },
    },
  });

  const { getValues, reset } = methods;

  // 폼 제출 핸들러
  const handleOnSubmit = async (data: ServiceFormData) => {
    const parseData = {
      ...data,
      serviceGroupId: data.serviceGroupId?.value,
      method: data.method?.value,
      useYn: data.useYn?.value,
    };
    if (modalType === 'add') {
      // 등록 모드일 때
      const responseMsg = await addServiceData({ data: parseData });
      if (responseMsg) {
        setIsOpenServiceModal(false);
        handleGetServiceList({ serviceGroupId: serviceGroupDetail?.id });
        activeAlert(responseMsg);
      }
    } else {
      // 수정 모드일 때
      if (serviceDetail) {
        const responseMsg = await updateServiceData({ id: serviceDetail.id, data: parseData });
        if (responseMsg) {
          setIsOpenServiceModal(false);
          handleGetServiceList({ serviceGroupId: serviceGroupDetail?.id });
          activeAlert(responseMsg);
        }
      }
    }
  };

  useEffect(() => {
    setServiceGroupOptionList(serviceGroupList?.map((item) => ({ label: item.name, value: item.id })));
  }, [serviceGroupList]);

  // 수정 모드일 때 기존 데이터 설정
  useEffect(() => {
    if (modalType === 'edit') {
      if (serviceDetail) {
        reset({
          serviceGroupId: { label: serviceDetail.serviceGroupName, value: serviceDetail.serviceGroupId },
          name: serviceDetail.name,
          serviceDesc: serviceDetail.serviceDesc,
          url: serviceDetail.url,
          method: { label: serviceDetail.method, value: serviceDetail.method },
          useYn: { label: serviceDetail.useYn === 'Y' ? '활성' : '비활성', value: serviceDetail.useYn },
        });
      }
    } else {
      reset({
        // 생성시, 상위 serviceGroupDetail 이 선택된 경우 기본 값 맵핑
        serviceGroupId: serviceGroupDetail
          ? { label: serviceGroupDetail.name, value: serviceGroupDetail.id }
          : serviceGroupOptionList?.[0],
        name: '',
        serviceDesc: '',
        url: '',
        method: { label: 'GET', value: 'GET' },
        useYn: { label: '활성', value: 'Y' },
      });
    }
  }, [serviceDetail, modalType, serviceGroupDetail, serviceGroupOptionList]);

  return (
    <DefaultModal
      className="service_confirm_modal"
      title={`서비스 ${modalType === 'add' ? '등록' : '수정'}`}
      isOpenModal={isOpenServiceModal}
      setIsOpenModal={setIsOpenServiceModal}
      footer={false}
    >
      <Form onSubmit={handleOnSubmit} methods={methods}>
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '서비스 그룹',
                required: true,
                contents: (
                  <>
                    {serviceGroupOptionList && (
                      <FormSelectBox
                        name="serviceGroupId"
                        placeholder="전체"
                        options={serviceGroupOptionList}
                        rules={{ required: '그룹을 선택해주세요' }}
                      />
                    )}
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '서비스 명',
                required: true,
                contents: (
                  <FormInput
                    wrapperClassName="full"
                    name="name"
                    placeholder="서비스 명 입력"
                    rules={{ required: '필수값입니다!' }}
                  />
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '서비스 설명',
                contents: <FormTextarea name="serviceDesc" placeholder="서비스 설명 입력" className="service_desc" />,
              }}
            />
            <TableBodyRow
              rowData={{
                title: '서비스 URL',
                required: true,
                contents: (
                  <FormInput
                    wrapperClassName="full"
                    name="url"
                    placeholder="서비스 URL 입력"
                    rules={{ required: '필수값입니다!' }}
                  />
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '서비스 메소드',
                required: true,
                contents: (
                  <FormSelectBox
                    name="method"
                    options={[
                      { label: 'GET', value: 'GET' },
                      { label: 'POST', value: 'POST' },
                      { label: 'PUT', value: 'PUT' },
                      { label: 'PATCH', value: 'PATCH' },
                      { label: 'DELETE', value: 'DELETE' },
                    ]}
                    rules={{ required: '필수값입니다!' }}
                  />
                ),
              }}
            />
            {getValues('useYn') && (
              <TableBodyRow
                rowData={{
                  title: '활성화',
                  required: true,
                  contents: (
                    <FormSelectBox
                      name="useYn"
                      options={[
                        { label: '활성', value: 'Y' },
                        { label: '비활성', value: 'N' },
                      ]}
                      rules={{ required: '필수값입니다!' }}
                    />
                  ),
                }}
              />
            )}
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button
            text="취소"
            color="grayscale"
            onClick={() => {
              setIsOpenServiceModal(false);
            }}
          />
          <Button type="submit" text="확인" onClick={() => {}} />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default ServiceModal;
