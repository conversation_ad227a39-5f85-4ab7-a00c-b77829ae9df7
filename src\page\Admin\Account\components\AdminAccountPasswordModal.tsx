import { postChangePassword } from '@api/admin/accountDetailAPI';
import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import DefaultModal from '@components/common/Modal/DefaultModal';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { DetailFormValues } from '@page/Admin/Account/type';
import { useAlertStore } from '@store/useAlertStore';
import { useForm } from 'react-hook-form';

const AdminAccountPasswordModal = ({ isOpenModal, setIsOpenModal, id }) => {
  const methods = useForm<DetailFormValues>({
    defaultValues: {
      password: '',
      passwordConfirm: '',
    },
  });

  const { activeAlert } = useAlertStore();

  const handleCloseModal = () => {
    setIsOpenModal(false);
  };

  const handleChangePassword = async (data: DetailFormValues) => {
    const responseMessage = await postChangePassword({ id, data, isGetErrorMsg: true });
    if (responseMessage) {
      handleCloseModal();
      activeAlert(responseMessage);
    }
  };

  return (
    <DefaultModal
      isOpenModal={isOpenModal}
      setIsOpenModal={setIsOpenModal}
      className="admin_account_password_modal"
      title="비밀번호 변경"
      footer={false}
    >
      <Form onSubmit={handleChangePassword} methods={methods}>
        <TableContainer>
          <TableBody>
            <tr>
              <th>
                <span className="required">비밀번호</span>
              </th>
              <td>
                <FormInput
                  name="password"
                  type="password"
                  placeholder="영문, 숫자, 특수문자를 포함하여 8~20자로 입력해주세요"
                  rules={{
                    minLength: {
                      value: 8,
                      message: '최소 8자 이상 입력해주세요.',
                    },
                    validate: (value) => {
                      if (!/^(?=.*[a-zA-Z])(?=.*[!@#$%^*+=-])(?=.*[0-9]).{8,20}$/.test(value)) {
                        return '영문, 숫자, 특수문자를 포함하여 8~20자로 입력해주세요.';
                      }
                    },
                  }}
                />
              </td>
            </tr>
            <tr>
              <th>
                <span className="required">비밀번호 확인</span>
              </th>
              <td>
                <FormInput
                  name="passwordConfirm"
                  type="password"
                  placeholder="위와 동일한 비밀번호를 입력해주세요."
                  rules={{
                    minLength: {
                      value: 8,
                      message: '최소 8자 이상 입력해주세요.',
                    },
                    validate: (value) => {
                      if (!/^(?=.*[a-zA-Z])(?=.*[!@#$%^*+=-])(?=.*[0-9]).{8,20}$/.test(value)) {
                        return '영문, 숫자, 특수문자를 포함하여 8~20자로 입력해주세요.';
                      }
                      if (value !== methods.getValues('password')) {
                        return '비밀번호가 일치하지 않습니다.';
                      }
                    },
                  }}
                />
              </td>
            </tr>
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <Button text="취소" color="grayscale" onClick={handleCloseModal} />
          <Button type="submit" text="변경" color="primary" />
        </div>
      </Form>
    </DefaultModal>
  );
};

export default AdminAccountPasswordModal;
