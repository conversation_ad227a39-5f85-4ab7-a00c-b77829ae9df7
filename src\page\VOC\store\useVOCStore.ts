import { DateInfo } from '@page/Admin/Account/type';
import { create } from 'zustand';

export type VOCListType = {
  category: string;
  id: string;
  status: string;
  title: string;
  viewCnt: number;
  createUserType: string | null;
  updateUserType: string | null;
  dateInfo: DateInfo;
  lastComment: {
    id: string | number;
    vocId: string | number;
    content: string;
    createUserType: string | null;
    updateUserType: string | null;
    dateInfo: DateInfo;
  };
};

export type PageInfoType = {
  totalPages: number;
  totalCount: number;
  currentPage: number;
  pageSize: number;
};

interface VOCStoreProps {
  isLoading: boolean;
  setIsLoading: (bool: boolean) => void;

  VOCList: VOCListType[];
  setVOCList: (val: VOCListType[]) => void;

  VOCPageInfo: PageInfoType;
  setVOCPageInfo: (val: PageInfoType) => void;

  isOpenVOCAddModal: boolean;
  setIsOpenVOCAddModal: (val: boolean) => void;

  categoryOptions: { label: string; value: string }[];
  setCategoryOptions: (val: { label: string; value: string }[]) => void;
  statusOptions: { label: string; value: string }[];
  setStatusOptions: (val: { label: string; value: string }[]) => void;

  VOCDetail: any;
  setVOCDetail: (val: any) => void;
}

export const useVOCStore = create<VOCStoreProps>((set) => ({
  isLoading: false,
  setIsLoading: (bool) => set((state) => ({ isLoading: bool })),

  VOCList: null,
  setVOCList: (val) => set((state) => ({ VOCList: val })),

  VOCPageInfo: null,
  setVOCPageInfo: (val) => set((state) => ({ VOCPageInfo: val })),

  isOpenVOCAddModal: false,
  setIsOpenVOCAddModal: (val) => set((state) => ({ isOpenVOCAddModal: val })),

  categoryOptions: [],
  setCategoryOptions: (val) => set((state) => ({ categoryOptions: val })),
  statusOptions: [],
  setStatusOptions: (val) => set((state) => ({ statusOptions: val })),

  VOCDetail: null,
  setVOCDetail: (val) => set((state) => ({ VOCDetail: val })),
}));
