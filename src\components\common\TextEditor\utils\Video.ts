import { Node, mergeAttributes } from '@tiptap/core';
import captionBlack from '@assets/images/icon/icon_editor_add_alt_black.svg';
import alignLeftBlack from '@assets/images/icon/icon_editor_align_left_black.svg';
import alignCenterBlack from '@assets/images/icon/icon_editor_align_center_black.svg';
import alignRightBlack from '@assets/images/icon/icon_editor_align_right_black.svg';
import captionPrimary from '@assets/images/icon/icon_editor_add_alt_primary.svg';
import alignLeftPrimary from '@assets/images/icon/icon_editor_align_left_primary.svg';
import alignCenterPrimary from '@assets/images/icon/icon_editor_align_center_primary.svg';
import alignRightPrimary from '@assets/images/icon/icon_editor_align_right_primary.svg';

export const Video = Node.create({
  name: 'video',
  group: 'block',
  selectable: true,
  draggable: true,
  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
      },
      width: {
        default: 640,
        parseHTML: (element) => {
          const width = element.getAttribute('width');
          return element.getAttribute('width') || 640;
        },
      },
      height: {
        default: 360,
        parseHTML: (element) => {
          return element.getAttribute('height') || 360;
        },
      },
      muted: {
        default: true,
      },
      playsinline: {
        default: true,
      },
      autoplay: {
        default: true,
      },
      controls: {
        default: true,
      },
      style: {
        default: 'max-width: 80vw;',
        parseHTML: (element) => {
          const width = element.getAttribute('width');
          return width ? `width: ${width}px; height: auto;` : `${element.style.cssText}`;
        },
      },
      caption: {
        default: '',
        parseHTML: element => {
          const figcaption = element.querySelector('figcaption');
          return figcaption?.textContent || '';
        }
      }
    };
  },

  parseHTML() {
    return [
      {
        tag: 'video',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return ['video', mergeAttributes(HTMLAttributes), ['figcaption', {style: 'color: #9c9c9c; font-size: 12px; margin-top: 4px;'}, HTMLAttributes.caption]];
  },

  addNodeView() {
    return ({ node, editor, getPos }) => {
      const {
        view,
        options: { editable },
      } = editor;
      const { style, caption } = node.attrs;
      const $wrapper = document.createElement('p');
      const $caption = document.createElement('figcaption');
      const $container = document.createElement('figure');
      const $video = document.createElement('video');
      const iconStyle = 'width: 24px; height: 24px; cursor: pointer;';

      const dispatchNodeView = () => {
        if (typeof getPos === 'function') {
          const newAttrs = {
            ...node.attrs,
            style: `${$video.style.cssText}`,
            width: $video.width,
            caption: $caption.textContent
          };
          view.dispatch(view.state.tr.setNodeMarkup(getPos(), null, newAttrs));
        }
      };

      const paintPositionController = () => {
        const $positionController = document.createElement('div');

        const $captionController = document.createElement('img');
        const $leftController = document.createElement('img');
        const $centerController = document.createElement('img');
        const $rightController = document.createElement('img');

        const controllerMouseOver = (e: MouseEvent, hoverSrc: string) => {
          (e.target as HTMLImageElement).src = hoverSrc;
        };

        const controllerMouseOut = (e: MouseEvent, src: string) => {
          (e.target as HTMLImageElement).src = src;
        };

        $positionController.setAttribute(
          'style',
          'position: absolute; top: 0%; left: 50%; min-width: 150px; padding: 10px; z-index: 98; background-color: #fff; border-radius: 4px; outline: 2px solid #6C6C6C; cursor: pointer; transform: translate(-50%, -50%); display: flex; justify-content: space-between; align-items: center;'
        );

        $captionController.setAttribute('src', captionBlack);
        $captionController.setAttribute('alt', 'video caption');
        $captionController.setAttribute('style', iconStyle);
        $captionController.addEventListener('mouseover', (e) => controllerMouseOver(e, captionPrimary));
        $captionController.addEventListener('mouseout', (e) => controllerMouseOut(e, captionBlack));
        
        $leftController.setAttribute('src', alignLeftBlack);
        $leftController.setAttribute('style', iconStyle);
        $leftController.setAttribute('alt', 'align left');
        $leftController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignLeftPrimary));
        $leftController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignLeftBlack));

        $centerController.setAttribute('src', alignCenterBlack);
        $centerController.setAttribute('alt', 'align center');
        $centerController.setAttribute('style', iconStyle);
        $centerController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignCenterPrimary));
        $centerController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignCenterBlack));

        $rightController.setAttribute('src', alignRightBlack);
        $rightController.setAttribute('alt', 'align right');
        $rightController.setAttribute('style', iconStyle);
        $rightController.addEventListener('mouseover', (e) => controllerMouseOver(e, alignRightPrimary));
        $rightController.addEventListener('mouseout', (e) => controllerMouseOut(e, alignRightBlack));

        $captionController.addEventListener('click', () => {
          const newCaption = window.prompt('Enter a caption for your video', $caption.textContent || '');
          if (newCaption === null) return;

          $caption.textContent = newCaption;
          dispatchNodeView();
        });

        $leftController.addEventListener('click', () => {
          $video.setAttribute('style', `${$video.style.cssText} margin: 0 auto 0 0;`);
          dispatchNodeView();
        });
        $centerController.addEventListener('click', () => {
          $video.setAttribute('style', `${$video.style.cssText} margin: 0 auto;`);
          dispatchNodeView();
        });
        $rightController.addEventListener('click', () => {
          $video.setAttribute('style', `${$video.style.cssText} margin: 0 0 0 auto;`);
          dispatchNodeView();
        });

        $positionController.appendChild($captionController);
        $positionController.appendChild($leftController);
        $positionController.appendChild($centerController);
        $positionController.appendChild($rightController);

        $container.appendChild($positionController);
      };

      $wrapper.setAttribute('style', `display: flex; max-width: 80vw;`);
      $wrapper.appendChild($container);

      $container.setAttribute('style', `${style}`);
      $container.appendChild($video);

      $caption.setAttribute('style', 'color: #9c9c9c; font-size: 12px; margin-top: 4px;');
      $caption.textContent = caption || '';
      $container.appendChild($caption);

      Object.entries(node.attrs).forEach(([key, value]) => {
        if (value === undefined || value === null) return;
        $video.setAttribute(key, value);
      });

      if (!editable) return { dom: $container };
      const isMobile = document.documentElement.clientWidth < 768;
      const dotPosition = isMobile ? '-8px' : '-4px';
      const dotsPosition = [
        `top: ${dotPosition}; left: ${dotPosition}; cursor: nwse-resize;`,
        `top: ${dotPosition}; right: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; left: ${dotPosition}; cursor: nesw-resize;`,
        `bottom: ${dotPosition}; right: ${dotPosition}; cursor: nwse-resize;`,
      ];

      let isResizing = false;
      let startX: number, startWidth: number;

      $container.addEventListener('click', (e) => {
        const isMobile = document.documentElement.clientWidth < 768;
        isMobile && (document.querySelector('.ProseMirror-focused') as HTMLElement)?.blur();

        if ($container.childElementCount > 3) {
          for (let i = 0; i < 5; i++) {
            $container.removeChild($container.lastChild as ChildNode);
          }
        }

        paintPositionController();

        $container.setAttribute('style', `position: relative; outline: 2px dashed #6C6C6C; ${style} cursor: pointer;`);

        Array.from({ length: 4 }, (_, index) => {
          const $dot = document.createElement('div');
          $dot.setAttribute(
            'style',
            `position: absolute; width: ${isMobile ? 16 : 9}px; height: ${isMobile ? 16 : 9}px; outline: 1.5px solid #6C6C6C; border-radius: 50%; ${dotsPosition[index]}`
          );

          $dot.addEventListener('mousedown', (e) => {
            e.preventDefault();
            isResizing = true;
            startX = e.clientX;
            startWidth = $container.offsetWidth;

            const onMouseMove = (e: MouseEvent) => {
              if (!isResizing) return;
              const deltaX = index % 2 === 0 ? -(e.clientX - startX) : e.clientX - startX;

              const newWidth = startWidth + deltaX;

              console.log($video, newWidth)
              $container.style.width = newWidth + 'px';
              $video.width = newWidth;
              $video.style.width = newWidth + 'px';
            };

            const onMouseUp = () => {
              if (isResizing) {
                isResizing = false;
              }
              dispatchNodeView();

              document.removeEventListener('mousemove', onMouseMove);
              document.removeEventListener('mouseup', onMouseUp);
            };

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
          });

          $dot.addEventListener(
            'touchstart',
            (e) => {
              e.cancelable && e.preventDefault();
              isResizing = true;
              startX = e.touches[0].clientX;
              startWidth = $container.offsetWidth;

              const onTouchMove = (e: TouchEvent) => {
                if (!isResizing) return;
                const deltaX = index % 2 === 0 ? -(e.touches[0].clientX - startX) : e.touches[0].clientX - startX;

                const newWidth = startWidth + deltaX;

                $container.style.width = newWidth + 'px';
                $video.width = newWidth;
                $video.style.width = newWidth + 'px';
              };

              const onTouchEnd = () => {
                if (isResizing) {
                  isResizing = false;
                }
                dispatchNodeView();

                document.removeEventListener('touchmove', onTouchMove);
                document.removeEventListener('touchend', onTouchEnd);
              };

              document.addEventListener('touchmove', onTouchMove);
              document.addEventListener('touchend', onTouchEnd);
            },
            { passive: false }
          );
          $container.appendChild($dot);
        });
      });

      document.addEventListener('click', (e: MouseEvent) => {
        const $target = e.target as HTMLElement;
        const isClickInside = $container.contains($target) || $target.style.cssText === iconStyle;

        if (!isClickInside) {
          const containerStyle = $container.getAttribute('style');
          const newStyle = containerStyle?.replace('outline: 2px dashed #6C6C6C;', '');
          $container.setAttribute('style', newStyle as string);

          if ($container.childElementCount > 3) {
            for (let i = 0; i < 5; i++) {
              $container.removeChild($container.lastChild as ChildNode);
            }
          }
        }
      });

      return {
        dom: $wrapper,
      };
    };
  },
});
