import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';
import { useCurrentEditor } from '@tiptap/react';

type level = 1 | 2 | 3 | 4 | 5 | 6;

const Heading = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const handleHeading = (level: level) => {
    editor.chain().focus().toggleHeading({ level }).run();
  };

  return (
    <div className="item_box heading">
      {Array.from({ length: 6 }).map((_, index) => (
        <IconButton
          key={`heading-${index + 1}`}
          text={'heading' + (index + 1)}
          icon={`h${index + 1}` as IconType}
          size='smallest'
          iconOnly
          fill="unfilled"
          color={editor.isActive('heading', { level: index + 1 }) ? 'primary' : 'grayscale'}
          isActive={editor.isActive('heading', { level: index + 1 })}
          onClick={() => handleHeading((index + 1) as level)}
          disabled={editor.isActive('codeBlock')}
        />
      ))}
    </div>
  );
};

export default Heading;
