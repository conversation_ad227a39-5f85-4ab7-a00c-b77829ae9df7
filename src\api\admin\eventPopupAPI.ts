import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';
import { buildQueryString, filterQueryString } from '@utils/queryString';

interface GetEventPopupListParams {
  params: { page: number; size: number; title?: string; startDate?: string; endDate?: string };
  isGetErrorMsg?: boolean;
}

const getEventPopupList = async ({ params, isGetErrorMsg = false }: GetEventPopupListParams) => {
  try {
    const queryString = buildQueryString(params);

    const response = await api.get(`/api-admin/event-popup${queryString}`);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 목록 조회');
  }
};

interface GetEventPopupDetailParams {
  id: string;
  isGetErrorMsg?: boolean;
}

const getEventPopupDetail = async ({ id, isGetErrorMsg = false }: GetEventPopupDetailParams) => {
  try {
    const response = await api.get(`/api-admin/event-popup/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 상세 조회');
  }
};
interface AddEventPopupParams {
  data: {
    title: string;
    subTitle: string;
    fileIds: string[];
    exposureStart: string;
    exposureEnd: string;
  };
  isGetErrorMsg?: boolean;
}

const addEventPopup = async ({ data, isGetErrorMsg = false }: AddEventPopupParams) => {
  try {
    const response = await api.post('/api-admin/event-popup', data);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 추가');
  }
};

interface EditEventPopupParams {
  id: string;
  data: {
    title: string;
    subTitle: string;
    fileIds: string[];
    exposureStart: string;
    exposureEnd: string;
    notOpenDay: number;
    linkUrl: string;
  };
  isGetErrorMsg?: boolean;
}

const editEventPopup = async ({ id, data, isGetErrorMsg = false }: EditEventPopupParams) => {
  try {
    const response = await api.post(`/api-admin/event-popup/${id}`, data);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 수정');
  }
};

interface DeleteEventPopupListParams {
  ids: string[];
  isGetErrorMsg?: boolean;
}

const deleteEventPopupList = async ({ ids, isGetErrorMsg = false }: DeleteEventPopupListParams) => {
  try {
    const response = await api.post('/api-admin/event-popup/delete', { ids });
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 삭제');
  }
};

interface DeleteEventPopupParams {
  id: string;
  isGetErrorMsg?: boolean;
}

const deleteEventPopupData = async ({ id, isGetErrorMsg = false }: DeleteEventPopupParams) => {
  try {
    const response = await api.post(`/api-admin/event-popup/${id}/delete`);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 삭제');
  }
};

interface GetEventPopupNowParams {
  isGetErrorMsg?: boolean;
}

const getEventPopupNow = async ({ isGetErrorMsg = false }: GetEventPopupNowParams) => {
  try {
    // 로그인 되어있지 않으면 호출하지 않음
    // if (!localStorage.getItem('accessToken')) return;

    const response = await api.get('/api-admin/evnt-popup/now');
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '이벤트 팝업 현재 조회');
  }
};

export {
  getEventPopupList,
  getEventPopupDetail,
  addEventPopup,
  editEventPopup,
  deleteEventPopupList,
  deleteEventPopupData,
  getEventPopupNow,
};
