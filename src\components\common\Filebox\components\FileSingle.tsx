import IconButton from '@components/common/Button/IconButton';
import React, { useEffect, useState } from 'react';
import Alert from '@components/common/Modal/Alert';
import { useAlertStore } from '@store/useAlertStore';
import { useFilePreviewer } from '@components/common/Filebox/store/useFilePreviewer';
import FileImagePreview from './FileImagePreview';

//  파일 미리보기 컴포넌트의 Props 인터페이스
interface FileSingleProps {
  file: File; // 미리보기할 파일 객체
  onDelete?: () => void; // 파일 삭제 시 실행될 콜백 함수
}

/**
 * 파일 미리보기 컴포넌트
 * 이미지 파일을 미리보기로 표시하고 파일 정보와 삭제 기능을 제공합니다.
 */
const FileSingle = ({ file, onDelete }: FileSingleProps) => {
  // 파일 미리보기 URL 상태
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  // 로딩 상태
  const [loading, setLoading] = useState<boolean>(true);
  // 에러 메시지 상태
  const [error, setError] = useState<string | null>(null);
  const { setPreviewModalState, setPreviewImageInfo, previewModalState } = useFilePreviewer();

  const initFilePreview = () => {
    setPreviewImage(null);
    setLoading(false);
    setError(null);
  };

  const handleError = (errorMsg: string) => {
    setError(errorMsg);
    setLoading(false);
    setPreviewImage(null);
  };

  const handlePreview = (e) => {
    e.preventDefault();
    setPreviewImageInfo({
      name: file.name,
      url: previewImage,
      size: file.size,
      type: file.type,
    });
    setPreviewModalState(true);
  };

  useEffect(() => {
    // 파일이 없는 경우 미리보기 초기화
    if (!file) {
      initFilePreview();
      return;
    }

    // 이미지 파일 타입 체크
    if (!file.type.startsWith('image/')) {
      setLoading(false);
      setPreviewImage(null);
      const reader = new FileReader();
      reader.abort();
      // URL.revokeObjectURL()를 사용하여 미리보기 URL의 메모리를 해제합니다.
      // 이는 브라우저의 메모리 누수를 방지하기 위한 중요한 정리 작업입니다.
      // preview URL이 더 이상 필요하지 않을 때 해당 URL이 차지하고 있던 메모리를 회수합니다.
      URL.revokeObjectURL(previewImage);
      // return;
    } else {
      // FileReader 인스턴스 생성
      const reader = new FileReader();

      // 파일 읽기 시작 시 로딩 상태 설정
      reader.onloadstart = () => {
        setLoading(true);
        setError(null);
      };

      // 파일 읽기 완료 시 미리보기 URL 설정
      reader.onload = (e) => {
        if (e.target?.result) {
          setPreviewImage(e.target.result as string);
          setLoading(false);
        } else {
          handleError('파일 미리보기를 생성할 수 없습니다.');
        }
      };

      // 파일 읽기 에러 발생 시 에러 메시지 설정
      reader.onerror = () => {
        handleError('파일을 읽는 중 오류가 발생했습니다.');
      };

      // 파일을 Data URL로 읽기 시도
      try {
        reader.readAsDataURL(file);
      } catch (err) {
        handleError('파일 처리 중 오류가 발생했습니다.');
      }

      // 컴포넌트 언마운트 시 FileReader 중단 및 메모리 정리
      return () => {
        reader.abort();
        // URL.revokeObjectURL()를 사용하여 미리보기 URL의 메모리를 해제합니다.
        // 이는 브라우저의 메모리 누수를 방지하기 위한 중요한 정리 작업입니다.
        // preview URL이 더 이상 필요하지 않을 때 해당 URL이 차지하고 있던 메모리를 회수합니다.
        URL.revokeObjectURL(previewImage);
      };
    }
  }, [file]);

  /**
   * 파일 삭제 핸들러
   * onDelete prop이 제공된 경우 실행하고 상태를 초기화합니다.
   */
  const handleDelete = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // 미리보기 URL 해제
    if (previewImage) {
      URL.revokeObjectURL(previewImage);
    }

    // 상태 초기화
    initFilePreview();

    // onDelete 콜백 실행
    if (onDelete) {
      onDelete();
    }
  };

  // 로딩 중일 때 표시할 컴포넌트
  if (loading) {
    return <div className="c_filebox_preview_loading">로딩중...</div>;
  }

  // 에러 발생 시 표시할 컴포넌트
  if (error) {
    return <div className="c_filebox_preview_error">{error}</div>;
  }

  // 미리보기 렌더링
  return (
    <div className="c_filebox_preview">
      <>
        {previewImage && (
          <div className="c_filebox_preview_image">
            <img src={previewImage} alt={file.name} onClick={handlePreview} />
            <IconButton
              icon="close"
              iconOnly
              size="smallest"
              design="circle"
              fill="unfilled"
              color="grayscale"
              text="삭제"
              onClick={handleDelete}
              className="c_filebox_preview_delete"
            />
          </div>
        )}
        {/* 파일 정보 영역 */}
        <div className="c_filebox_preview_info">
          <b className="c_filebox_preview_desc">파일을 드랍하시면, 기존의 파일과 대체됩니다.</b>
          <p className="c_filebox_preview_name">
            {file.name}
            {!previewImage && (
              <IconButton
                icon="close"
                iconOnly
                size="smallest"
                design="circle"
                fill="unfilled"
                color="grayscale"
                text="삭제"
                onClick={handleDelete}
                className="c_filebox_preview_delete"
              />
            )}
          </p>
          <p className="c_filebox_preview_size">{(file.size / 1024).toFixed(2)}KB</p>
        </div>
      </>
      {previewModalState && <FileImagePreview />}
    </div>
  );
};

export default FileSingle;
