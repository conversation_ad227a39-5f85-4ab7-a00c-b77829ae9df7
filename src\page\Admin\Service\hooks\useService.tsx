import { getServiceList } from '@api/admin/serviceAPI';
import { useAdminServiceGroupAPI } from '@api/admin/serviceGroupAPI';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';

const useService = () => {
  const { setServiceList, setServiceGroupList, setOriginalServiceGroupList, serviceGroupDetail } = useServiceStore();
  const { getServiceGroupList } = useAdminServiceGroupAPI();

  interface GetServiceListParams {
    serviceGroupId?: number;
    isGetErrorMsg?: boolean;
  }

  const handleGetServiceList = async ({
    serviceGroupId = serviceGroupDetail?.id,
    isGetErrorMsg = false,
  }: GetServiceListParams) => {
    const serviceListData = await getServiceList({ serviceGroupId, isGetErrorMsg });
    setServiceList(serviceListData);
  };

  const handleGetServiceGroupList = async () => {
    const serviceGroupListData = await getServiceGroupList({});
    setServiceGroupList(serviceGroupListData);
    setOriginalServiceGroupList(serviceGroupListData);
  };

  return { handleGetServiceList, handleGetServiceGroupList };
};

export default useService;
