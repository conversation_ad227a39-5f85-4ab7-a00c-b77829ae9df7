import { Option } from '@components/common/Form/FormCheckboxList';
import { DetailFormValues } from '@page/Admin/Account/type';
import { create } from 'zustand';

interface AdminAccountDetailStore {
  // 상세 데이터 폼
  detailFormValues: DetailFormValues;

  // 권한 목록
  roleListData: Option[];
  setRoleListData: (data: Option[]) => void;

  // ID 중복 확인 여부
  checkDuplicateId: boolean;
  setCheckDuplicateId: (data: boolean) => void;
}

export const useAdminAccountDetailStore = create<AdminAccountDetailStore>((set) => ({
  detailFormValues: {
    adminId: '',
    name: '',
    password: '',
    passwordConfirm: '',
    activeYn: 'Y',
    delYn: 'N',
    roleIds: [],
    loginFailureCnt: null,
  },

  roleListData: [],
  setRoleListData: (data) => set((state) => ({ roleListData: data })),

  checkDuplicateId: true,
  setCheckDuplicateId: (data) => set((state) => ({ checkDuplicateId: data })),
}));
