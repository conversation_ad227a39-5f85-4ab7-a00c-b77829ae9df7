@use '@styles/utils/mixin' as m;

.c_calendar_wrapper {
  .c_button_icon_text {
    background-color: var(--g_01);
    border: 1px solid var(--g_06) !important;
    span {
      font-weight: 500;
    }

    &:hover {
      background-color: var(--g_09);
    }
  }
}

.c_calendar_popup {
  border-radius: 0.25rem;
  overflow: hidden;
  z-index: 1000;
  border: 1px solid var(--g_05) !important;
  background-color: var(--modal_bg) !important;
  box-shadow: var(--shadow_l);

  .c_calendar_header {
    @include m.flex(center, space-between);
    padding: 0.375rem 0;
  }
  .c_calendar_desc {
    text-align: right;
    padding: 0 0.625rem;
    font-size: 0.725rem;
    color: var(--g_06) !important;
  }

  .c_calendar_time_wrapper { 
    @include m.flex(center, space-between);
    padding: 0.25rem;
    gap: 0.5rem;
    .c_input {
      flex: 1;
    }
  }

  .c_calendar_footer {
    @include m.flex(center, end);
    gap: 0.125rem;
    padding: 0.625rem;
    background-color: var(--modal_bg) !important;
  }
  .react-datepicker {
    @include m.flex(start, space-between);
    width: 100%;
    margin-bottom: 1rem;
    background-color: transparent;
    border: none;

    .react-datepicker__month-container {
      width: 233px;

      .react-datepicker__header {
        padding: 0;
        background-color: transparent;
        border-bottom: 0;

        .custom-header {
          @include m.flex(center, space-between);
          padding: 0 6.5px;
          margin-bottom: 18px;
          span {
            font-size: 14px;
            line-height: 14px;
          }
          button {
            width: 20px;
            height: 20px;
            margin: 0;
            padding: 0;
          }
        }
        .react-datepicker__day-names {
          @include m.flex(center, space-around);
          margin-bottom: 12px;
          > div {
            padding: 0;
            width: 11px;
            height: 14px;
            font-size: 12px;
            line-height: 14px;
          }
        }
      }
      .react-datepicker__month {
        margin: 0;
        .react-datepicker__week {
          @include m.flex(center, space-around);
          &:not(:last-child) {
            margin-bottom: 16px;
          }
          .react-datepicker__day--weekend {
            @for $i from 1 through 5 {
              &:nth-child(#{$i * 7 - 6}) {
                &::before {
                  width: 42px !important;
                  left: 105% !important;
                  border-radius: 12px 0 0 12px !important;
                }
              }
            }
            @for $i from 1 through 5 {
              &:nth-child(#{$i * 7}) {
                &::before {
                  width: 35px !important;
                  left: 26% !important;
                  border-radius: 0 12px 12px 0 !important;
                }
              }
            }
          }
          .react-datepicker__day {
            position: relative;
            padding: 0;
            margin: 0;
            width: 15px;
            height: 14px;
            font-size: 12px;
            line-height: 14px;

            &:hover {
              &::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: -1;
                width: 24px;
                height: 24px;
                border-radius: 12px;
                transition: all 0.3s ease-in-out;
              }
            }

            &:not(.react-datepicker__day--disabled):hover {
              background-color: transparent;
              color: var(--font_white);
              &::after {
                background-color: var(--p_05);
              }
            }

            &--keyboard-selected {
              background-color: transparent;
              &::after {
                background-color: var(--p_01);
              }
            }

            &.react-datepicker__day--in-range,
            &.react-datepicker__day--in-selecting-range {
              border-radius: 0;
              &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: -2;
                width: 70px;
                height: 24px;
              }
            }
            &.react-datepicker__day--range-start,
            &.react-datepicker__day--selecting-range-start,
            &.react-datepicker__day--range-end,
            &.react-datepicker__day--selecting-range-end {
              &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50% !important;
                transform: translate(-50%, -50%);
                z-index: -2;
                width: 24px !important;
                height: 24px;
                border-radius: 12px !important;
              }
            }
            &.react-datepicker__day--disabled,
            &.react-datepicker__day--outside-month {
              color: var(--g_05);
            }
            &.react-datepicker__day--in-range,
            &.react-datepicker__day--in-selecting-range {
              background-color: transparent;
              &::before {
                background-color: var(--p_03);
              }
            }
            &.react-datepicker__day--range-start,
            &.react-datepicker__day--selecting-range-start {
              background-color: transparent;
              &::before {
                background-color: var(--g_06);
              }
              &::after {
                background-color: var(--g_04);
              }
            }
            &.react-datepicker__day--range-end,
            &.react-datepicker__day--selected,
            &.react-datepicker__day--selecting-range-end {
              color: var(--font_white);
              background-color: transparent;
              &::before {
                background-color: var(--g_06);
              }
              &::after {
                background-color: var(--p_05);
              }
            }

            &::after {
              content: '';
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              z-index: -1;
              width: 24px !important;
              height: 24px;
              border-radius: 12px;
            }
          }
        }
      }
    }
  }
}

// [theme='dark'] {
//   .c_calendar_wrapper {
//     .c_button_icon_text {
//       border: 1px solid dc.$g_06 !important;
//     }
//   }

//   .c_calendar_popup {
//     border: 1px solid dc.$g_02 !important;
//     background-color: dc.$g_01 !important;
//     box-shadow: 0px 0px 10px 4px rgba(0, 0, 0, 0.4);

//     .c_calendar_header {
//       background-color: dc.$g_01 !important;
//     }

//     .c_calendar_desc {
//       color: dc.$g_06 !important;
//     }

//     .c_calendar_footer {
//       background-color: dc.$g_01 !important;
//     }

//     .react-datepicker {
//       background-color: transparent;
//       border: none;
//       color: white;
//       .react-datepicker__header {
//         background-color: transparent;
//         border-bottom: 0;
//         color: white;
//       }
//       .react-datepicker__day {
//         color: white;

//         &-name {
//           color: white;
//         }

//         &--keyboard-selected {
//           background-color: transparent;
//           color: black;
//           &::after {
//             background-color: cc.$p_01;
//           }
//         }

//         &:not(.react-datepicker__day--disabled):hover {
//           background-color: transparent;
//           color: white;
//           &::after {
//             background-color: cc.$p_05;
//           }
//         }
//         &.react-datepicker__day--disabled,
//         &.react-datepicker__day--outside-month {
//           color: dc.$g_05;
//         }
//         &.react-datepicker__day--in-range,
//         &.react-datepicker__day--in-selecting-range {
//           background-color: transparent;
//           &::before {
//             background-color: cc.$p_07;
//           }
//         }
//         &.react-datepicker__day--range-start,
//         &.react-datepicker__day--selecting-range-start {
//           background-color: transparent;
//           &::before {
//             background-color: dc.$g_06;
//           }
//           &::after {
//             background-color: dc.$g_04;
//           }
//         }
//         &.react-datepicker__day--range-end,
//         &.react-datepicker__day--selected,
//         &.react-datepicker__day--selecting-range-end {
//           background-color: transparent;
//           &::before {
//             background-color: dc.$g_06;
//           }
//           &::after {
//             background-color: cc.$p_05;
//           }
//         }
//       }
//     }
//   }
// }
