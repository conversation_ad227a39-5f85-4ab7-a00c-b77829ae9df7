import { forwardRef, useEffect, useRef, useState } from 'react';

import joinClassName from '@utils/joinClassName';
import IconButton from '@components/common/Button/IconButton';
import Input from '@components/common/Input/Input';

type SearchKeywordProps = { label: string; value: string | number }[]; // 추후 value 미필요 시 제거

export interface SearchBarProps {
  placeholder?: string;
  searchValue: string;
  setSearchValue: React.Dispatch<React.SetStateAction<string>>;
  searchList?: SearchKeywordProps;
  readonly?: boolean;
  disabled?: boolean;
  design?: 'error' | 'warning' | 'confirm';
  className?: string;
}

const SearchBar = forwardRef<HTMLDivElement, SearchBarProps>(
  (
    {
      placeholder = '검색어를 입력하세요',
      searchValue, // 검색어 state (*)
      setSearchValue, // 검색어 전달 state 함수 (*)
      searchList, // 검색어 list
      readonly = false,
      disabled = false,
      design,
      className,
    },
    ref
  ) => {
    const [isFocus, setIsFocus] = useState(false);
    const searchBarClass = joinClassName('c_searchbar_wrapper', isFocus && 'open', design, className);
    const searchInputClass = joinClassName('searchbar', searchValue !== '' && 'fill');
    const searchRef = useRef(null);

    const handleChangeSearchValue = (e) => {
      const value = e.target.value;

      if (searchValue.length > 0) {
        setSearchValue(value);
      } else {
        setSearchValue(value.trim()); // 검색어 입력 없을 경우 공백 search 방지
      }
    };

    useEffect(() => {
      if (!isFocus) {
        setSearchValue(searchValue.trim()); // focusOut 될 때 앞뒤 공백 제거
      }
    }, [isFocus]);

    const handleReset = () => {
      setSearchValue('');
    };

    const handleClickKeyword = (keyword) => {
      setSearchValue(keyword);
      setIsFocus(false);
    };

    useEffect(() => {
      // 외부 클릭을 감지하는 함수
      const handleClickOutside = (event) => {
        if (searchRef.current && !searchRef.current.contains(event.target)) {
          setIsFocus(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    return (
      <>
        <div
          className={searchBarClass}
          ref={(node) => {
            searchRef.current = node;
            if (typeof ref === 'function') {
              ref(node);
            } else if (ref) {
              ref.current = node;
            }
          }}
          data-readonly={readonly}
          aria-disabled={disabled}
        >
          <Input
            className={searchInputClass}
            placeholder={placeholder}
            onFocus={() => setIsFocus(true)}
            value={searchValue}
            onChange={handleChangeSearchValue}
            readOnly={readonly}
            disabled={disabled}
            design={design}
          />
          <IconButton
            text="close"
            icon="circle_close"
            iconOnly
            design="circle"
            color="grayscale"
            fill="unfilled"
            className={joinClassName(searchValue === '' && '!hidden')}
            onClick={handleReset}
          />
          {searchList && ( // searchlist가 있을 때만 보여 줌
            <ul className="c_search_result_lists">
              {searchList.map((keyword) => (
                <li
                  className="c_search_result_list_item"
                  key={keyword.value}
                  value={keyword.value}
                  onClick={() => handleClickKeyword(keyword.label)}
                >
                  {keyword.label}
                </li>
              ))}
            </ul>
          )}
        </div>
      </>
    );
  }
);

export default SearchBar;
