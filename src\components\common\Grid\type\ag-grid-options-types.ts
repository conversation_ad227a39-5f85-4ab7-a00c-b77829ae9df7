import type * as AgGridTypes from '@ag-grid-community/core';
/**************************************************************************
 * 문서용 타입입니다.
 * 실제 사용은 AgGridTypes.GridOptions 따르며, 이 타입은 설명 및 가이드 참고용입니다.
 ***************************************************************************/

// [셀 이벤트 관련 옵션 ]
export interface CellEventOptions {
  /**
   * 셀 값 변경 시 호출되는 이벤트 핸들러
   * - 용도: 셀 편집 후 값이 실제로 바뀐 경우 처리
   * - 발생 조건: 편집이 완료되고, 이전 값과 다른 경우
   * - 예시: (params) => console.log('새 값:', params.newValue)
   */
  onCellValueChanged?: (params: AgGridTypes.NewValueParams<any, any>) => void;

  /**
   * 셀 클릭 시 호출되는 이벤트 핸들러
   * - 용도: 셀을 클릭했을 때 동작 처리
   * - 예시: (event) => console.log('클릭된 셀:', event)
   */
  onCellClicked?: (event: AgGridTypes.CellClickedEvent<any, any>) => void;

  /**
   * 셀을 더블 클릭했을 때 호출되는 이벤트 핸들러
   * - 용도: 셀 더블 클릭 시 동작 처리
   * - 예시: (event) => console.log('더블클릭된 셀:', event)
   */
  onCellDoubleClicked?: (event: AgGridTypes.CellDoubleClickedEvent<any, any>) => void;

  /**
   * 셀에서 마우스 우클릭(컨텍스트 메뉴) 발생 시 호출
   * - 용도: 우클릭 시 커스텀 메뉴 표시 등 처리
   * - 예시: (event) => console.log('우클릭 메뉴:', event)
   */
  onCellContextMenu?: (event: AgGridTypes.CellContextMenuEvent<any, any>) => void;
}
export interface TooltipOptions {
  /**
   * 셀에 툴팁으로 보여줄 데이터의 필드명
   * - tooltipValueGetter보다 우선 적용됨
   * - 예: 'name'
   */
  tooltipField?: string;

  /**
   * 툴팁으로 표시할 값을 반환하는 함수
   * - tooltipField가 없을 때 사용됨
   * - 예: (params) => `가격: ${params.value}원`
   */
  tooltipValueGetter?: (params: any) => string;

  /**
   * 사용자 정의 툴팁 컴포넌트
   * - 예: MyCustomTooltip
   */
  tooltipComponent?: string | { new (): AgGridTypes.ITooltipComp };

  /**
   * 사용자 정의 툴팁 컴포넌트에 전달할 파라미터
   * - 예: { color: 'red' }
   */
  tooltipComponentParams?: any;

  /**
   * 브라우저 기본 툴팁 사용 여부
   * - true: 브라우저의 기본 title 속성으로 툴팁 표시
   * - false: AG Grid의 커스텀 툴팁 사용 (기본값)
   */
  enableBrowserTooltips?: boolean;

  /**
   * 툴팁 표시 지연 시간 (ms)
   * - 마우스 오버 후 툴팁이 표시되기까지의 대기 시간
   * - 기본값: 2000ms
   * - enableBrowserTooltips가 true일 경우 적용되지 않음
   */
  tooltipShowDelay?: number;

  /**
   * 툴팁 자동 숨김까지의 대기 시간 (ms)
   * - 기본값: 10000ms
   * - enableBrowserTooltips가 true이고, tooltipHideTriggers에 timeout이 포함되면 적용되지 않음
   */
  tooltipHideDelay?: number;

  /**
   * 마우스 커서를 따라 툴팁이 이동할지 여부
   * - true: 툴팁이 커서를 따라다님
   * - 기본값: false
   */
  tooltipMouseTrack?: boolean;

  /**
   * 툴팁이 언제 표시될지 정의
   * - 'standard': 항상 표시됨
   * - 'whenTruncated': 텍스트가 잘린 경우에만 표시됨 (ellipsis)
   * - enableBrowserTooltips가 true일 경우 동작하지 않음
   */
  tooltipShowMode?: 'standard' | 'whenTruncated';

  /**
   * 툴팁 표시 트리거 방식
   * - 'hover': 마우스 오버 시 툴팁 표시 (기본값)
   * - 'focus': 포커스 시 툴팁 표시
   */
  tooltipTrigger?: 'hover' | 'focus';

  /**
   * 툴팁과 상호작용 가능 여부
   * - true: 툴팁 위에 마우스를 올려도 사라지지 않음
   * - false: 툴팁에 포커스가 없어지면 사라짐 (기본값)
   */
  tooltipInteraction?: boolean;
}
