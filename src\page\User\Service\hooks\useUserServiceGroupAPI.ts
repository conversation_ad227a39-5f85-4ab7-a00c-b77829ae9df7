import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { ServiceGroupData } from '@page/User/Service/type';

const errorMessage = '서비스 그룹 조회 중 오류가 발생했습니다.';

export const useUserServiceGroupAPI = () => {
  const USER_SERVICE_GROUP = '/api-admin/user/service-groups';
  const {
    serviceGroupDetail,
    setServiceGroupDetail,
    setServiceGroupList,
    setIsOpenServiceGroupModal,
    setOriginalServiceGroupList,
  } = useServiceStore();
  const { openAlert } = useAlert();

  // 서비스 그룹 목록 조회
  const getServiceGroupList = async () => {
    try {
      const response = await api.get(USER_SERVICE_GROUP);
      const listData = response.data.data;
      setServiceGroupList(listData);
      setOriginalServiceGroupList(listData);
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 그룹 삭제
  const deleteServiceGroup = async (id: number) => {
    try {
      const response = await api.post(`${USER_SERVICE_GROUP}/${id}/delete`);
      openAlert(response.data.message, () => {
        if (id === serviceGroupDetail?.id) {
          setServiceGroupDetail(null);
        }
        getServiceGroupList();
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 그룹 등록
  const addServiceGroup = async (data: ServiceGroupData) => {
    try {
      const response = await api.post(USER_SERVICE_GROUP, data);
      setIsOpenServiceGroupModal(false);
      openAlert(response.data.message, () => {
        getServiceGroupList();
        setServiceGroupDetail(response.data.data);
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 그룹 수정
  const updateServiceGroup = async (id: number, data: ServiceGroupData) => {
    try {
      const response = await api.post(`${USER_SERVICE_GROUP}/${id}`, data);
      setIsOpenServiceGroupModal(false);
      openAlert(response.data.message, () => {
        getServiceGroupList();
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  return { getServiceGroupList, deleteServiceGroup, addServiceGroup, updateServiceGroup };
};
