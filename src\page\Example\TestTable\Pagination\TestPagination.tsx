import React, { useState } from 'react';
import TestTable from '../TestTable';
import Pagination from '@components/common/Pagination';

const TestPagination = () => {
  const [currentPage, setCurrentPage] = useState(1);

  const handlePage = (d) => {
    console.log(d);
    setCurrentPage(d);
  };

  return (
    <TestTable
      compName="pagination"
      headChild={
        <>
          <tr>
            <th colSpan={3}>Pagination</th>
          </tr>
          <tr>
            <th>Status</th>
            <th>Default</th>
          </tr>
        </>
      }
      bodyChild={
        <tr>
          <th>default</th>
          <td>
            <p>current Page: {currentPage}</p>
            <Pagination totalCount={100} currentPage={currentPage} onPageChange={handlePage} />
          </td>
        </tr>
      }
    />
  );
};

export default TestPagination;
