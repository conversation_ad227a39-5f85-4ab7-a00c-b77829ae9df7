@use '@styles/utils/colors_common' as *;
@use '@styles/utils/mixin' as *;

.admin_page {
  &_list {
  }

  &_detail {
    .c_table_cell {
      span {
        min-height: 24px;
      }
    }

    .c_editor {
      background-color: var(--g_01);
      border: none;
      box-shadow: var(--shadow_l);
      min-height: 150px;
    }

    .button_wrapper {
      @include flex(center, space-between);
      margin-top: 0.625rem;
      margin-bottom: 1rem;

      .right {
        @include flex(center, flex-end);
        gap: 0.25rem;
      }
    }
  }

  &_add,
  &_edit {
    .button_wrapper {
      @include flex(center, flex-end);
      gap: 0.25rem;
      margin-top: 0.625rem;
    }
  }
}

.page_add_modal {
  width: 1080px;

  .c_table_wrapper {
    margin-bottom: 0.625rem;
    .c_table {
      tr {
        td {
          .c_input_wrapper {
            width: 100%;
            .c_input {
              width: 100%;
            }
          }

          .c_select_wrapper {
            width: 100%;
            .c_select {
              width: 100%;
              .c_selected_label[data-selected-value='0'] {
                span {
                  color: var(--g_05) !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .button_wrapper {
    @include flex(center, flex-end);
    gap: 0.25rem;
  }
}
