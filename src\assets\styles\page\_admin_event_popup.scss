@use '@styles/utils/mixin' as m;

.admin_event_popup {
  &_list {
    .c_table_container_header {
      .page_size {
        @include m.flex(center, start);
        gap: 1rem;
      }
      .button_wrapper {
        @include m.flex(center, start);
        gap: 0.25rem;
      }
    }

    .ag-cell {
      line-height: 100px;
      .ag-cell-wrapper {
        display: inline-flex;
      }
    }

    .grid_thumbnail,
    .grid_empty_thumbnail {
      width: 100%;
      height: 80%;
      margin: 15% auto;
      object-fit: cover;
      object-position: center;
    }

    .grid_empty_thumbnail {
      background-color: var(--g_01);
      border-radius: 0.5rem;
    }
  }

  &_detail {
    .c_table_wrapper {
      margin-bottom: 0.75rem;
    }

    .content {
      .left_content {
        width: calc(100% - 489px);

        figure {
          overflow: hidden;
          img {
            cursor: pointer;
            transition: transform 0.3s ease;
            &:hover {
              transform: scale(1.05);
            }
          }
        }
      }
      .right_content {
        position: sticky;
        top: 85px;
        width: 489px;
        .event_popup_preview {
          width: 100%;
          height: 100%;
          padding: 1rem;
          background-color: white;
          .c_modal_body {
            width: 440px;
            height: 550px;
            padding: 0;

            .swiper {
              width: 100%;
              height: 100%;
              .desc {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                z-index: 8;
                background-color: rgba(#fff, 0.8);
                text-align: left;
                padding: 1rem 2rem 2rem 2rem;
                h3 {
                  font-size: 1.5rem;
                  font-weight: 600;
                }
              }

              .swiper-slide {
                width: 100%;
                height: 100%;

                .slide_img {
                  width: 100%;
                  height: 100%;
                  cursor: pointer;
                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    object-position: center;
                  }
                }

                .no_image {
                  @include m.flex(center, center);
                  width: 100%;
                  height: 100%;
                  background-color: var(--g_01);
                }
              }
            }
          }
          .c_modal_footer {
            @include m.flex(center, space-between);
          }
        }
      }
    }

    .button_wrapper {
      @include m.flex(center, space-between);
      gap: 0.25rem;

      .right {
        @include m.flex(center, start);
        gap: 0.25rem;
      }
    }
  }

  &_test {
    .button_wrapper {
      @include m.flex(center, start);
      gap: 0.25rem;
    }

    .event_popup_list {
      display: flex;
      gap: 1rem;
      .event_popup_preview_wrapper {
        position: relative;
        .event_popup_preview {
          display: inline-block;
          width: 384px;
          padding: 0.75rem;
          background-color: white;
          border-radius: 0.5rem;
          .c_modal_body {
            width: 360px;
            height: 450px;
            padding: 0;

            .swiper {
              width: 100%;
              height: 100%;
              .desc {
                position: absolute;
                bottom: 0;
                left: 0;
                width: 100%;
                z-index: 8;
                background-color: rgba(#fff, 0.8);
                text-align: left;
                padding: 0.75rem 1.5rem 1.5rem 1.5rem;
                h3 {
                  font-size: 1rem;
                  font-weight: 600;
                }
                p {
                  font-size: 0.75rem;
                }
              }

              .swiper-slide {
                width: 100%;
                height: 100%;

                .slide_img {
                  width: 100%;
                  height: 100%;
                  cursor: pointer;
                  img {
                    width: 100%;
                    height: 100%;
                    object-fit: contain;
                    object-position: center;
                  }
                }

                .no_image {
                  @include m.flex(center, center);
                  width: 100%;
                  height: 100%;
                  background-color: var(--g_01);
                }
              }
            }
          }
          .c_modal_footer {
            @include m.flex(center, space-between);
          }
        }
        .deactive_pop_cover {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(#000, 0.5);
          z-index: 100;
        }
      }
    }
  }

  @include m.bp_large {
    .admin_event_popup_detail {
      .content {
        .left_content {
          width: calc(100% - 445px);
        }

        .right_content {
          top: 75px;
          width: 445px;

          .event_popup_preview {
            padding: 0.875rem;
            .c_modal_body {
              width: 400px;
              height: 500px;

              .swiper {
                .desc {
                  padding: 0.875rem 1.75rem 1.75rem 1.75rem;

                  h3 {
                    font-size: 1.25rem;
                    font-weight: 600;
                  }
                  p {
                    font-size: 0.875rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  @include m.bp_medium {
    .admin_event_popup_detail {
      .content {
        .left_content {
          width: calc(100% - 401px);
        }

        .right_content {
          top: 65px;
          width: 401px;

          .event_popup_preview {
            padding: 0.75rem;
            .c_modal_body {
              width: 360px;
              height: 450px;

              .swiper {
                .desc {
                  padding: 0.75rem 1.5rem 1.5rem 1.5rem;
                  h3 {
                    font-size: 1rem;
                    font-weight: 600;
                  }
                  p {
                    font-size: 0.75rem;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
