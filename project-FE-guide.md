# BRIT 노드 플랫폼 FE 개발 가이드

## 기술 스택

- **프레임워크**: React18
- **언어**: TypeScript
- **스타일링**: SCSS Modules
- **상태 관리**: zustand

## 사용 라이브러리

- **그리드 라이브러리**: ag-grid (community ver.)
- **에디터 라이브러리**: tiptap
- **날짜 라이브러리**: dayjs

## 최초 실행

```bash
npm install
npm run start
```

## 실행

```bash
npm run start
```

## 디렉토리 구조

```
src/
│── api/                             # API 폴더
│── assets/                          # assets 폴더
│   │── fonts/                          # fonts 폴더
│   │── images/                         # images source 폴더
│   │── styles/                         # 스타일 폴더
│   │   │── colors/                       # 컬러 관리 폴더
│   │   │── components/                   # 공통 컴포넌트 스타일 폴더
│   │   │── layout/                       # 레이아웃 스타일 폴더
│   │   │── page/                         # 페이지 스타일 폴더
│   │   │── utils/                        # mixin, variables 등 스타일 util 폴더
│   │   │── styles.scss
│── components/                      # 공통 컴포넌트 폴더
│   │── common/                         # 공통 컴포넌트 폴더
│   │── layout/                         # 공통 레이아웃 폴더
│── hooks/                           # 공통 hooks 폴더
│── page/                            # 페이지 폴더 (모든 페이지는 아래 규칙을 따름)
│   │── VOC/                            # 생성된 페이지(라우터 기준) 폴더
│   │   │── components/                   # 해당 페이지 관련 컴포넌트 폴더
│   │   │── hooks/                        # 해당 페이지 관련 hooks 폴더
│   │   │── store/                        # 해당 페이지 관련 store 폴더
│   │   │── VOCPage.tsx
│   │   │── type.ts
│── route/                           # 라우트 폴더
│── store/                           # 공통 store(상태) 폴더
│── types/                           # 공통 types 폴더
│── utils/                           # 공통 utils 폴더
│── App.tsx
│── main.tsx
```

### Common, Types

- import 경로는 **절대경로**를 사용한다.
- 타입 선언 시 **컴포넌트 혹은 API의 Props로 사용되는 type은 해당 컴포넌트 내에 선언**하고, 그 외 **페이지와 관련된 타입은 Page/ExamplePage 아래의 type.ts 파일에 작성**한다.

  - ex.

    ```
    // TableBodyRow.tsx

    interface Props {
      title?: string | React.ReactNode;
      required?: boolean;
      children?: React.ReactNode;
      }

    const TableBodyRow = ({ title = '', required, children }: Props) => {
      return (
        <tr>
          ...
        </tr>
      );
      };

    export default TableBodyRow;
    ```

- alert, confirm 컴포넌트 사용 시 state만 호출한다.
  - 컴포넌트에서 `<Alet />`, `<Confirm />` 컴포넌트 호출 금지 (상태 공통 관리)
  - 관련 sotre: `useAlertSotre.ts`, `useConfirmStore.ts`

### API 선언 공통 규칙

- **common**: 공통 관련 API 폴더
- **admin**: 관리자 관련 API 폴더
- **user**: 사용자 관련 API 폴더

</br>

- 포스트맨(스웨거) 폴더링 기준으로 api 파일을 생성한다. (`~Api.ts`)
- 포스트맨 기준 같이 폴더링 되어 있는 api는 한 파일에 작성한다.
- 포스트맨에서 제공되는 제목 기준으로 주석을 단다.
  - 빠른 검색을 위함 (api 중복 선언 방지)
- `catch` 구문에 `defaultHandleError` 함수 적용 (필수\*)
  - **defaultHandleError**: api 에러 시 alert창 보여 주는 함수
  - `isGetErrorMsg` 옵션 `true` 설정 시 alert이 아닌 error 객체를 응답값으로 받을 수 있다.
  -
- ex. [관리자] 관리자 메뉴 관리 하위의 관리자 메뉴 목록 조회 API

  - api > admin 폴더 내에 `menuMangeApi.ts` 생성

  ```
  // menuManageApi.ts

  // [관리자] 관리자 메뉴 관리

  /**
  * 관리자 메뉴 목록 조회
  */

  interface GetAdminMenusProps {
    isGetErrorMsg?: boolean
  }

  export const getAdminMenus = async ({ isGetErrorMsg = false }: GetAdminMenusProps) => {
    try {
      const apiUrl = `/api-admin/admin/menus`;
      const response = await api.get(apiUrl);

      return response.data.data;
    } catch (error: unknown) {
      defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 목록 조회');
    }
  };

  ```

### 스타일링 관련 공통 규칙

- className을 조건문으로 처리할 때에는 **joinClassName.ts** 유틸 함수를 적극 사용한다.
  - ex.
    ```
    const inputClass = joinClassName('c_input', align, design, inputSize, className);
    ```
