@use '@styles/utils/mixin' as m;
@use '@styles/utils/colors_dark' as dc;
@use '@styles/utils/colors_light' as lc;
@use '@styles/utils/colors_common' as cc;

[theme='dark'] {
  .manage_user_menu {
    .manage_user_contents {
      .manage_user_contents_left {
        // 카테고리 리스트
        .user_menu_category_wrap {
          .user_menu_category {
            background-color: dc.$g_02;

            .user_menu_category_top {
              .user_menu_category_parent_wrap {
                border-top: 1px solid rgba(dc.$g_10, 0.5);

                &:last-child {
                  border-bottom: 1px solid rgba(dc.$g_10, 0.5);
                }

                .user_menu_category_parent {
                  &.clicked {
                    background-color: dc.$g_01;
                  }
                }

                .user_menu_category_child_wrap {
                  .user_menu_category_child {
                    &::before {
                      content: url('@assets/images/icon/icon_star_white.svg');
                    }

                    &.clicked {
                      background-color: cc.$p_05;
                      &::before {
                        content: url('@assets/images/icon/icon_star_yellow.svg');
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .manage_user_contents_right {
        .user_menu_info_wrap {
          // 조회 내용 없을 경우
          .user_menu_info_nothing {
            background-color: rgba(dc.$g_02, 0.65);
          }

          .menu_required {
            &::after {
              color: cc.$error;
            }
          }
        }
      }
    }
  }
}

[theme='light'] {
  .manage_user_menu {
    .manage_user_contents {
      .manage_user_contents_left {
        // 카테고리 리스트
        .user_menu_category_wrap {
          .user_menu_category {
            background-color: white;

            .user_menu_category_top {
              .user_menu_category_parent_wrap {
                border-top: 1px solid rgba(lc.$g_07, 0.5);

                &:last-child {
                  border-bottom: 1px solid rgba(lc.$g_07, 0.5);
                }

                .user_menu_category_parent {
                  &.clicked {
                    background-color: lc.$g_03;
                  }
                }

                .user_menu_category_child_wrap {
                  .user_menu_category_child {
                    &::before {
                      content: url('@assets/images/icon/icon_star_black.svg');
                    }
                    
                    &.clicked {
                      background-color: cc.$p_05;
                      color: white;
                      &::before {
                        content: url('@assets/images/icon/icon_star_yellow.svg');
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .manage_user_contents_right {
        .user_menu_info_wrap {
          // 조회 내용 없을 경우
          .user_menu_info_nothing {
            background-color: rgba(lc.$g_05, 0.65);
          }

          .menu_required {
            &::after {
              color: cc.$error;
            }
          }
        }
      }
    }
  }
}
