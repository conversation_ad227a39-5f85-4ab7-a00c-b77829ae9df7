// hooks/useEventBus.ts
import { useEffect } from 'react';
import mitt, { Emitter } from 'mitt';

type Events = Record<string, any>;
const emitter = mitt<Events>();

export default function useEventBus() {
  const on = <K extends keyof Events>(type: K, handler: (event: Events[K]) => void) => {
    useEffect(() => {
      emitter.on(type, handler);
      return () => {
        emitter.off(type, handler);
      };
    }, [type, handler]);
  };

  const emit = <K extends keyof Events>(type: K, event: Events[K]) => {
    emitter.emit(type, event);
  };

  return { eventBus: { on, emit } };
}
