import { create } from 'zustand';
import { ServiceData, ServiceGroupData } from '@page/User/Service/type';

interface ServiceStore {
  isOpenServiceGroupModal: boolean;
  setIsOpenServiceGroupModal: (val: boolean) => void;

  modalType: 'add' | 'edit' | null;
  setModalType: (val: 'add' | 'edit' | null) => void;

  serviceGroupList: ServiceGroupData[];
  setServiceGroupList: (val: ServiceGroupData[]) => void;

  originalServiceGroupList: ServiceGroupData[];
  setOriginalServiceGroupList: (val: ServiceGroupData[]) => void;

  serviceGroupDetail: ServiceGroupData | null;
  setServiceGroupDetail: (val: ServiceGroupData | null) => void;

  serviceList: ServiceData[];
  setServiceList: (val: ServiceData[]) => void;

  originalServiceList: ServiceData[];
  setOriginalServiceList: (val: ServiceData[]) => void;

  isOpenServiceModal: boolean;
  setIsOpenServiceModal: (val: boolean) => void;

  serviceDetail: ServiceData | null;
  setServiceDetail: (val: ServiceData | null) => void;
}

export const useServiceStore = create<ServiceStore>((set) => ({
  // 서비스 그룹 모달 상태
  isOpenServiceGroupModal: false,
  setIsOpenServiceGroupModal: (val) => set(() => ({ isOpenServiceGroupModal: val })),

  // 서비스 그룹 모달 타입
  modalType: null,
  setModalType: (val) => set(() => ({ modalType: val })),

  // 서비스 그룹 목록
  serviceGroupList: null,
  setServiceGroupList: (val) => set(() => ({ serviceGroupList: val })),

  originalServiceGroupList: null,
  setOriginalServiceGroupList: (val) => set(() => ({ originalServiceGroupList: val })),

  // 서비스 그룹 상세
  serviceGroupDetail: null,
  setServiceGroupDetail: (val) => set(() => ({ serviceGroupDetail: val })),

  // 서비스 목록
  serviceList: [],
  setServiceList: (val) => set(() => ({ serviceList: val })),

  originalServiceList: [],
  setOriginalServiceList: (val) => set(() => ({ originalServiceList: val })),

  // 서비스 모달 상태
  isOpenServiceModal: false,
  setIsOpenServiceModal: (val) => set(() => ({ isOpenServiceModal: val })),

  // 서비스 상세
  serviceDetail: null,
  setServiceDetail: (val) => set(() => ({ serviceDetail: val })),
}));
