export const filterQueryString = (queryStrings: (string | undefined | null)[]): string => {
  return queryStrings.filter(Boolean).join('&'); // 값이 없는 항목은 제거, &로 결합
};

export const buildQueryString = (params: any) => {
  if (!params) return '';

  let queryString = '';
  Object.entries(params).forEach(([key, value], index) => {
    if (value === undefined || value === null || value === '') return;
    if (index === 0) {
      queryString += `?${key}=${value}`;
    } else {
      queryString += `&${key}=${value}`;
    }
  });

  return queryString;
};
