import joinClassName from '@utils/joinClassName';
import { LabelInputProps } from './types';
import Input from '@components/common/Input/Input';

/**label + input이 함께 있는 형태의 input */
const LabelInput = ({
  label, // label (*)
  placeholder, // placeholer (*)
  relateId, // label과 input을 연결해주는 id (*)
  error, // 에러메세지가 있는 경우 text 전달값

  /**label input 디자인 요소 */
  design = 'default',
  className, // 추가 클래스 요소

  ...attributes
}: LabelInputProps) => {
  const labelInputClass = joinClassName('c_label_input', design, className);

  return (
    <div className={labelInputClass}>
      <label htmlFor={relateId}>{label}</label>
      <Input id={relateId} placeholder={placeholder} {...attributes} />
      {error && <p>{error}</p>}
    </div>
  );
};

export default LabelInput;
