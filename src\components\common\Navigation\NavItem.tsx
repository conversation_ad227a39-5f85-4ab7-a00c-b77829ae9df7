import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import joinClassName from '@utils/joinClassName';
import { useLayoutStore } from '@store/useLayoutStore';

import { Menu } from 'types/adminMenuType';
import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';
import NavContainer from './NavContainer';

export interface NavItemProps {
  id: string | number | null;
  text: string;
  url: string;
  icon?: IconType;
  tag?: 'li' | 'div';
  className?: string;
  childs?: Menu[];
  depth?: number;
  isOpenChildMenuId?: string | number | null;
  setIsOpenChildMenuId?: (isOpenChildMenuId: string | number | null) => void;
}

const NavItem = ({
  id = null,
  tag = 'li',
  className,
  text,
  url,
  icon = 'arrow_right',
  childs,
  depth,
  isOpenChildMenuId = null,
  setIsOpenChildMenuId,
}: NavItemProps) => {
  const Tag = tag;
  const navigate = useNavigate();
  const location = useLocation();
  const currentPath = location.pathname;
  // 현재 주소와 URL을 각각 '/'로 나누고, depth 번째가 같은경우에는 활성화
  const activeCondition = currentPath === url || currentPath.startsWith(url);

  const { lnbState } = useLayoutStore();

  const handleNavigate = () => {
    if (depth === 1 && childs?.length > 0 && lnbState) {
      // 메뉴 레벨이 1이고 자식 메뉴가 있는 경우 자식 메뉴를 표시
      setIsOpenChildMenuId(id);
    } else {
      // 메뉴 레벨이 2이거나 자식 메뉴가 없는 경우 메뉴 이동
      navigate(url);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const isOpenChildMenuClass = isOpenChildMenuId === id ? 'open' : '';

  useEffect(() => {
    if (lnbState) {
      if (currentPath.split('/')[depth] === url?.split('/')[depth]) {
        setIsOpenChildMenuId(id);
      }
    }
  }, [lnbState, currentPath]);

  return (
    <Tag className={joinClassName('c_nav_item', className, depth, isOpenChildMenuClass)}>
      <IconButton
        text={text}
        onClick={handleNavigate}
        icon={icon}
        iconOnly={!lnbState}
        fill="unfilled"
        color={activeCondition ? 'primary' : 'grayscale'}
      />
      {childs?.length > 0 && depth < 2 && <NavContainer menuList={childs} depth={depth} />}
    </Tag>
  );
};

export default NavItem;
