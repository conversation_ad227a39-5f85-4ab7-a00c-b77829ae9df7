import api from '@api/api';
import { useAlertStore } from '@store/useAlertStore';

/**
 * 파일이 이미지라면 새창에서 이미지 오픈
 *
 * 아니라면 다운로드
 *
 */
interface DownloadFileOptions {
  fileId: string;
  imgDownload?: boolean;
  downloadName?: string;
}

export const downloadFile = async ({ fileId, imgDownload = false, downloadName }: DownloadFileOptions) => {
  const USER_FILE = '/api/files';

  try {
    const response = await api.get(`${USER_FILE}/${fileId}`, {
      responseType: 'arraybuffer',
    });

    const contentType = response.headers['content-type'];
    const contentDisposition = response.headers['content-disposition'];
    const blob = new Blob([response.data], { type: contentType });
    const dataUrl = URL.createObjectURL(blob);

    // 데이터가 없으면
    if (response.data.byteLength == 0) {
      useAlertStore.getState().setAlertState({
        isOpen: true,
        content: '존재하지 않는 파일입니다',
        onConfirm: () => {
          useAlertStore.getState().initAlertState();
        },
      });
      return;
    }

    //이미지면 새창
    if (contentType.startsWith('image/')) {
      if (!imgDownload) {
        openImage(dataUrl);
      } else {
        download(dataUrl, downloadName);
      }
    } else {
      // 아니라면 다운로드
      download(dataUrl, getFileNameAtContentDisposition(contentDisposition));
    }
  } catch (error) {
    useAlertStore.getState().setAlertState({
      isOpen: true,
      content: error?.response?.data?.message,
      onConfirm: () => {
        useAlertStore.getState().initAlertState();
      },
    });
  }
};

const openImage = (url: string) => {
  window.open(url, '_blank'); // 새 창 열기
};

const download = (url: string, filename: string) => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename; // 파일 이름 설정
  link.click(); // 다운로드 실행

  URL.revokeObjectURL(url); // URL 해제
};

const getFileNameAtContentDisposition = (contentDisposition: string) => {
  const fileName = contentDisposition ? contentDisposition.split('filename=')[1].replace(/"/g, '') : 'download';

  return fileName;
};
