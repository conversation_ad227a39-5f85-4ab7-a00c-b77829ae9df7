import Button from '@components/common/Button/Button';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useEffect, useRef, useState } from 'react';
import { useServiceStore } from '@page/Admin/Service/store/useServiceStore';
import ServiceModal from './ServiceModal';
import { AgGridReact } from '@ag-grid-community/react';
import { ColDef } from '@ag-grid-community/core';
import { ServiceData } from '@page/User/Service/type';
import { useConfirmStore } from '@store/useConfirmStore';
import SubTitle from '@components/common/Title/SubTitle';
import { CustomFilter } from './CustomFilter';
import { useAlertStore } from '@store/useAlertStore';
import Grid from '@components/common/Grid/Grid';
import useService from '@page/Admin/Service/hooks/useService';
import { deleteServiceData } from '@api/admin/serviceAPI';

const ServiceList = () => {
  const { serviceList, serviceGroupDetail, setModalType, setIsOpenServiceModal, setServiceDetail } = useServiceStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();
  const { handleGetServiceList } = useService();

  const gridRef = useRef<AgGridReact>(null);
  const [rowData, setRowData] = useState<ServiceData[]>([]);
  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    { headerName: 'No.', field: 'sort', width: 90, sortable: true, unSortIcon: true },
    {
      headerName: 'Method',
      field: 'method',
      width: 130,
      sortable: true,
      unSortIcon: true,
      filter: CustomFilter,
      filterParams: {
        name: 'method',
        params: ['GET', 'POST', 'PUT', 'DELETE'],
      },
    },
    {
      headerName: '활성화',
      field: 'useYn',
      width: 90,
      cellRenderer: (params: any) => {
        return params.value === 'Y' ? '활성' : '비활성';
      },
      filter: CustomFilter,
      filterParams: {
        name: 'useYn',
        params: ['Y', 'N'],
      },
    },
    {
      headerName: '서비스 명',
      field: 'name',
      flex: 1,
      cellRenderer: (params: any) => {
        return <span style={{ userSelect: 'text' }}>{params.value}</span>;
      },
      filter: true,
      floatingFilter: true,
    },
    {
      headerName: 'URL',
      field: 'url',
      flex: 1,
      cellRenderer: (params: any) => {
        return <span style={{ userSelect: 'text' }}>{params.value}</span>;
      },
      filter: true,
      floatingFilter: true,
    },
    {
      headerName: '상세',
      field: 'detail',
      width: 90,
      cellRenderer: (params: any) => {
        return (
          <Button
            text="상세"
            onClick={() => handleModifyService(params.data)}
            clickLog={{ buttonSection: '서비스 목록' }}
          />
        );
      },
    },
    {
      headerName: '삭제',
      field: 'delete',
      width: 90,
      cellRenderer: (params: any) => {
        return (
          <Button
            text="삭제"
            color="red"
            onClick={() => handleDeleteServicePopup(params.data)}
            clickLog={{ buttonSection: '서비스 목록' }}
          />
        );
      },
    },
  ]);

  const handleRegisterService = () => {
    setModalType('add');
    setIsOpenServiceModal(true);
    setServiceDetail(null);
  };

  const handleModifyService = (serviceInfo: ServiceData) => {
    setModalType('edit');
    setIsOpenServiceModal(true);
    setServiceDetail(serviceInfo);
  };

  const handleDeleteServicePopup = (serviceInfo: ServiceData) => {
    setConfirmState({
      title: '알림',
      isOpen: true,
      content: (
        <p>
          <span className="c_text_bold ">{serviceInfo.name}</span> <br />
          서비스를 삭제하시겠습니까?
        </p>
      ),
      onConfirm: async () => {
        const responseMsg = await deleteServiceData({ id: serviceInfo.id });
        if (responseMsg) {
          const serviceGroupId = serviceInfo.serviceGroupId;
          handleGetServiceList({ serviceGroupId, isGetErrorMsg: false }); // 그룹별 서비스 목록 조회
          activeAlert(responseMsg);
        }

        initConfirmState();
      },
      onCancel: () => {
        initConfirmState();
      },
    });
  };

  // 상위에서 서비스 그룹선택 변경시 검색 초기화
  useEffect(() => {
    handleGetServiceList({ serviceGroupId: serviceGroupDetail?.id });
  }, [serviceGroupDetail]);

  useEffect(() => {
    if (serviceList) {
      const data = serviceList.map((item, idx) => ({
        ...item,
        sort: serviceList.length - idx,
      }));
      setRowData(data);
    }
  }, [serviceList]);

  return (
    <>
      <TableContainerHeader
        leftChildren={
          <>
            <SubTitle>서비스 목록</SubTitle>
            <span className="total_cnt">( 총 {serviceList?.length} 개 )</span>
          </>
        }
        rightChildren={
          <Button text="서비스 등록" onClick={handleRegisterService} clickLog={{ buttonSection: '서비스 목록' }} />
        }
      />
      <div className="service_list">
        <Grid
          sizeReset
          autoSizeStrategy={'onGridSizeChanged'}
          placeholder="데이터가 없습니다. 서비스를 추가해주세요."
          ref={gridRef}
          columns={columnDefs}
          rowData={rowData}
          defaultColDef={{
            width: 170,
          }}
        />
      </div>
      <ServiceModal />
    </>
  );
};

export default ServiceList;
