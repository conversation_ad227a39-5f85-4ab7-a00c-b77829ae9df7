import React from 'react';
import { Controller, Control, useFormContext } from 'react-hook-form';
import Input from '@components/common/Input/Input';
import { InputProps } from '@components/common/Input/types';
import joinClassName from '@utils/joinClassName';

interface FormInputProps extends InputProps {
  name: string; // Controller에서 사용하는 필드 이름
  rules?: any; // 검증 규칙
  wrapperClassName?: string;
  label?: string;
}

const FormInput: React.FC<FormInputProps> = ({ name, rules, wrapperClassName, label, ...attributes }) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <>
          {label && <label htmlFor={name}>{label}</label>}
          <div className={joinClassName('c_input_wrapper', wrapperClassName)}>
            <Input
              {...field}
              {...attributes}
              error={fieldState.error?.message} // 에러 메시지 표시
            />
          </div>
        </>
      )}
    />
  );
};

export default FormInput;
