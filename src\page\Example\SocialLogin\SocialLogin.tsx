import api from "@api/api";
import { useEffect, useState } from "react";
import './SocialLogin.css';

const SocialLogin = () => {
    const [providers, setProviders] = useState([]);
    const [loading, setLoading] = useState(true);
    const [loginForm, setLoginForm] = useState({
        email: '',
        password: ''
    });
    const [loginLoading, setLoginLoading] = useState(false);
    const [loginError, setLoginError] = useState('');

    useEffect(() => {
        // 소셜 로그인 제공자 목록 조회
        const fetchProviders = async () => {
            try {
                const response = await api.get('/auth/social/providers');
                const data = response.data;

                if (data.data) {
                    setProviders(Object.values(data.data));
                }
            } catch (error) {
                console.error('소셜 로그인 제공자 조회 실패:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchProviders();
    }, []);

    const handleSocialLogin = (provider) => {
        // 백엔드 OAuth2 엔드포인트로 리다이렉트
        const loginUrl = `${provider.loginUrl}`;
        window.location.href = loginUrl;
    };

    const getProviderIcon = (code) => {
        switch (code) {
            case 'google':
                return (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
                        <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
                        <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
                        <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
                    </svg>
                );
            case 'naver':
                return (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M16.273 12.845 7.376 0H0v24h7.726V11.156L16.624 24H24V0h-7.727v12.845z" fill="white"/>
                    </svg>
                );
            case 'kakao':
                return (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3C6.486 3 2 6.262 2 10.5c0 2.665 1.67 5.01 4.19 6.398L5.5 20.5l3.808-2.052C10.131 18.81 11.037 19 12 19c5.514 0 10-3.262 10-7.5S17.514 3 12 3z" fill="#3C1E1E"/>
                    </svg>
                );
            default:
                return (
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                    </svg>
                );
        }
    };

    const getProviderConfig = (code) => {
        switch (code) {
            case 'google':
                return {
                    className: 'google',
                    style: {
                        backgroundColor: '#ffffff',
                        color: '#757575',
                        border: '1px solid #dadce0'
                    }
                };
            case 'naver':
                return {
                    className: 'naver',
                    style: {
                        backgroundColor: '#03c75a',
                        color: 'white',
                        border: 'none'
                    }
                };
            case 'kakao':
                return {
                    className: 'kakao',
                    style: {
                        backgroundColor: '#fee500',
                        color: '#3c1e1e',
                        border: 'none'
                    }
                };
            default:
                return {
                    className: 'default',
                    style: {
                        backgroundColor: '#6c757d',
                        color: 'white',
                        border: 'none'
                    }
                };
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setLoginForm(prev => ({
            ...prev,
            [name]: value
        }));
        // 입력 시 에러 메시지 초기화
        if (loginError) {
            setLoginError('');
        }
    };

    const handleEmailLogin = async (e) => {
        e.preventDefault();

        if (!loginForm.email || !loginForm.password) {
            setLoginError('이메일과 비밀번호를 모두 입력해주세요.');
            return;
        }

        setLoginLoading(true);
        setLoginError('');

        try {
            const response = await api.post('/auth/login', {
                email: loginForm.email,
                password: loginForm.password
            });

            if (response.data.success) {
                // 로그인 성공 처리
                console.log('로그인 성공:', response.data);
                // 토큰 저장 및 리다이렉트 등의 처리
                // localStorage.setItem('token', response.data.token);
                // window.location.href = '/dashboard';
            } else {
                setLoginError(response.data.message || '로그인에 실패했습니다.');
            }
        } catch (error) {
            console.error('로그인 오류:', error);
            setLoginError(error.response?.data?.message || '로그인 중 오류가 발생했습니다.');
        } finally {
            setLoginLoading(false);
        }
    };

    if (loading) {
        return (
            <div className="social-login-loading">
                <div className="loading-spinner"></div>
                <p>소셜 로그인 옵션을 불러오는 중...</p>
            </div>
        );
    }

    return (
        <div className="social-login-container">
            <div className="social-login-title">
                <h3>로그인</h3>
            </div>

            <form className="login-form" onSubmit={handleEmailLogin}>
                <div className="form-group">
                    <label htmlFor="email">이메일</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        value={loginForm.email}
                        onChange={handleInputChange}
                        placeholder="이메일 주소를 입력하세요"
                        required
                    />
                </div>

                <div className="form-group">
                    <label htmlFor="password">비밀번호</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        value={loginForm.password}
                        onChange={handleInputChange}
                        placeholder="비밀번호를 입력하세요"
                        required
                    />
                </div>

                {loginError && (
                    <div className="login-error">
                        {loginError}
                    </div>
                )}

                <button
                    type="submit"
                    className="login-button"
                    disabled={loginLoading}
                >
                    {loginLoading ? (
                        <span className="button-loading">
                            <div className="loading-spinner-small"></div>
                            로그인 중...
                        </span>
                    ) : '로그인'}
                </button>

                <div className="login-options">
                    <div className="remember-me">
                        <input type="checkbox" id="remember" />
                        <label htmlFor="remember">로그인 상태 유지</label>
                    </div>
                    <a href="#" className="forgot-password">비밀번호 찾기</a>
                </div>
            </form>

            <div className="social-login-divider">
                <span>또는</span>
            </div>

            <div className="social-login-buttons">
                {providers.map((provider) => {
                    const config = getProviderConfig(provider.code);
                    return (
                        <button
                            key={provider.code}
                            onClick={() => handleSocialLogin(provider)}
                            className={`social-login-button ${config.className}`}
                            style={config.style}
                        >
                            <div className="provider-icon">
                                {getProviderIcon(provider.code)}
                            </div>
                            <div className="provider-text">
                                {provider.description} 계정으로 계속하기
                            </div>
                        </button>
                    );
                })}
            </div>
        </div>
    );
}

export default SocialLogin;