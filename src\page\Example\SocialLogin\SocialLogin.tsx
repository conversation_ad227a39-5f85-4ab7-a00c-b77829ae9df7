import api from "@api/api";
import { useEffect, useState } from "react";

const SocialLogin = () => {
    const [providers, setProviders] = useState([]);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        // 소셜 로그인 제공자 목록 조회
        const fetchProviders = async () => {
            try {
                const response = await api.get('/auth/social/providers');
                const data = response.data;

                if (data.data) {
                    setProviders(Object.values(data.data));
                }
            } catch (error) {
                console.error('소셜 로그인 제공자 조회 실패:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchProviders();
    }, []);

    const handleSocialLogin = (provider) => {
        // 백엔드 OAuth2 엔드포인트로 리다이렉트
        const loginUrl = `${provider.loginUrl}`;
        window.location.href = loginUrl;
    };

    const getProviderIcon = (code) => {
        switch (code) {
            case 'google':
                return '🔍'; // 또는 Google 아이콘 이미지
            case 'naver':
                return '🟢'; // 또는 Naver 아이콘 이미지
            case 'kakao':
                return '💬'; // 또는 Kakao 아이콘 이미지
            default:
                return '🔐';
        }
    };

    const getProviderStyle = (code) => {
        switch (code) {
            case 'google':
                return {
                    backgroundColor: '#4285f4',
                    color: 'white'
                };
            case 'naver':
                return {
                    backgroundColor: '#03c75a',
                    color: 'white'
                };
            case 'kakao':
                return {
                    backgroundColor: '#fee500',
                    color: '#000000'
                };
            default:
                return {
                    backgroundColor: '#6c757d',
                    color: 'white'
                };
        }
    };

    if (loading) {
        return (
            <div className="social-login-loading">
                <p>소셜 로그인 옵션을 불러오는 중...</p>
            </div>
        );
    }

    return (
        <div className="social-login-container">
            <div className="social-login-title">
                <h3>소셜 계정으로 로그인</h3>
                <p>간편하게 소셜 계정으로 로그인하세요</p>
            </div>

            <div className="social-login-buttons">
                {providers.map((provider) => (
                    <button
                        key={provider.code}
                        onClick={() => handleSocialLogin(provider)}
                        className="social-login-button"
                        style={getProviderStyle(provider.code)}
                    >
                        <span className="provider-icon">
                            {getProviderIcon(provider.code)}
                        </span>
                        <span className="provider-text">
                            {provider.description}로 로그인
                        </span>
                    </button>
                ))}
            </div>
        </div>
    );
}

export default SocialLogin;