import { useLoadingStore } from '@store/useLoadingStore';
import SettingTypes from './Components/SettingTypes';
import SettingList from './Components/SettingList';

const ManageSettingPage = () => {
  return (
    <div className="content horizontal">
      <div className="left_content vertical">
        <SettingTypes />
      </div>
      <div className="right_content vertical">
        <SettingList />
      </div>
    </div>
  );
};

export default ManageSettingPage;
