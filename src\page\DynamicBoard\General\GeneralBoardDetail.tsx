import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getGeneralBoardDetail, deleteGeneralBoardPost } from '@api/admin/dynamicBoardApi';
import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import TextEditor from '@components/common/TextEditor/TextEditor';
import Attachment from '@page/Admin/Post/components/Attachment';
import { AdminPostDetailType } from '@page/Admin/Post/type';
import { useYnOptions } from '@constants/options';
import type { Menu } from './type';

const GeneralBoardDetail = (menuInfo: Menu) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [data, setData] = useState<AdminPostDetailType | undefined>(undefined);

  const fetchData = async () => {
    const response = await getGeneralBoardDetail({ bbsId: menuInfo.boardTempletId, postId: id });
    setData(response);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const onClickBtnDelete = () => {
    utils.showConfirm('delete', async () => {
      const res = await deleteGeneralBoardPost({ bbsId: menuInfo.boardTempletId, postId: id });
      if (res?.code === 'DELETE') {
        utils.showAlert(res.message, onNavigateList);
      }
    });
  };

  const onNavigateList = () => {
    navigate(`${menuInfo.url}`);
  };

  return (
    <div className="admin_post">
      <div className="admin_post_detail_wrapper">
        <TableContainer>
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '제목',
                isFullWidth: true,
                contents: data?.title,
              }}
            />
            <TableBodyRow
              rowData={{
                title: '공지 여부',
                isFullWidth: true,
                contents: utils.getLabel(data?.notiYn, useYnOptions),
              }}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '작성자',
                  contents: data?.dateInfo.createUser,
                },
                {
                  title: '작성 일시',
                  contents: data?.dateInfo.createDate,
                },
              ]}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '수정자',
                  contents: data?.dateInfo.updateUser,
                },
                {
                  title: '수정 일시',
                  contents: data?.dateInfo.updateDate,
                },
              ]}
            />
            <TableBodyRow
              rowData={[
                {
                  title: '첨부파일',
                  isFullWidth: true,
                  contents: <Attachment data={data?.attchFiles} />,
                },
              ]}
            />
          </TableBody>
        </TableContainer>
        <TextEditor className="admin_post_detail_contents" content={data?.content} readOnly />
        <div className="admin_post_detail_btns">
          <Button text="목록" onClick={onNavigateList} />
          <div className="admin_post_detail_btns_right">
            <Button text="삭제" color="red" onClick={onClickBtnDelete} />
            <Button text="수정" onClick={() => navigate(`${menuInfo.url}/edit/${id}`)} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default GeneralBoardDetail;
