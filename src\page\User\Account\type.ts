// 활성 상태 코드
export type TActive = 'Y' | 'N';

// 계정 목록 조회 쿼리 파라미터
export interface UserListParams {
  page: number | null;
  size: number | null;
  userId: string | null;
  name: string | null;
  activeYn: string | null;
}

// 계정 목록 조회 검색 폼 값
export interface ListFormValues {
  userId: string; // 사용자 아이디
  activeYn: { label: string; value: string } | null; // 활성 상태 코드
  name: string; // 사용자 이름
}

// 날짜 정보
export interface DateInfo {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

// 계정 목록 아이템
export interface UserListItem {
  id: number;
  userId: string;
  name: string;
  activeYn: TActive; // 활성 상태 코드
  dateInfo: DateInfo;
}

// 계정 목록 페이지네이션 정보
export interface PaginationData {
  totalCount: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}


// Detail 페이지 타입
export type pageType = 'add' | 'edit';

// Detail 페이지 폼 값
export interface DetailFormValues {
  userId: string;
  name: string;
  email: string;
  password: string;
  activeYn: TActive;
  roleIds: number[];
}

// 테이블 속성
export interface BodyRowProps {
  title: string;
  children?: React.ReactNode;
  required?: boolean;
}

// 관리자 계정 상세
export interface UserDetailItem {
  id: number | string;
  userId: string;
  name: string;
  activeYn: TActive;
  roleIds: number[];
}

// 권한
export interface Role {
  id: number;
  name: string;
}