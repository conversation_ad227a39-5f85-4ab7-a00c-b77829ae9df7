import type { Meta, StoryObj } from '@storybook/react';
import DataGrid from '../DataGrid';
import type { AgColumnOptions } from '../type/ag-column-options-types';
import { useRef } from 'react';
import { CenteredDecorator } from './_gridDecorator';

/**
 * 자세한 옵션 설명은 {@link AgColumnOptions}  참고
 */

const meta: Meta<typeof DataGrid> = {
  title: 'Components/Grid/ColumnOtions',
  component: DataGrid,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [CenteredDecorator],
};

export default meta;

type Story = StoryObj<typeof DataGrid>;

const Template = (args: any) => {
  // const gridRef = useRef(null);
  return <DataGrid {...args} />;
};

export const BaseOptions: Story = {
  args: {
    columnDefs: [
      {
        headerName: '이름',
        field: 'name',
        valueGetter: (params) => `${params.data.name}님`,
      },
      {
        headerName: '성별',
        field: 'gender',
        refData: { M: '남성', F: '여성' },
        valueFormatter: (params) => params.colDef.refData?.[params.value] ?? params.value,
      },
      {
        headerName: '가격',
        field: 'price',
        valueFormatter: (params) => `${params.value.toLocaleString()}원`,
        cellDataType: 'number',
      },
      {
        headerName: 'ARIA Role',
        field: 'price',
        cellAriaRole: (params) => (params.value > 5000 ? 'note' : 'gridcell'),
      },
      {
        headerName: '고유 키 (keyCreator)',
        field: 'id',
        filter: 'agSetColumnFilter',
        keyCreator: (value: number) => `ID-${value}`,
      },
      {
        headerName: '값 비교',
        field: 'value',
        editable: true,
        equals: (a: number, b: number) => Math.floor(a) === Math.floor(b),
      },
      {
        headerName: '탭키로 이동 제한',
        field: 'name',
        suppressNavigable: (params: any) => params.node.rowIndex === 0,
      },
      {
        headerName: 'Enter 키 입력 억제',
        field: 'name',
        editable: true,
        suppressKeyboardEvent: (params: any) => params.event.key === 'Enter',
      },
      {
        headerName: '컨텍스트 테스트',
        field: 'name',
        cellRenderer: (params: any) => {
          const contextUser = params.context?.user;
          return `${params.value} (${contextUser ?? 'No user'})`;
        },
      },
    ],
    rowData: [
      { id: 1, name: '홍길동', gender: 'M', price: 1234.5677777777, editable: false },
      { id: 2, name: '이순신', gender: 'F', price: 9876.548888888, editable: true },
    ],
  },
};

export const EditableOptions: Story = {
  render: Template,
  args: {
    columnDefs: [
      {
        field: 'id',
        headerName: 'ID',
        editable: true,
        valueSetter: (params) => {
          params.data.id = params.newValue;
          return true;
        },
        valueParser: (params) => Number(params.newValue),
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 10 },
        cellEditorSelector: () => ({ component: 'agTextCellEditor' }),
        cellEditorPopup: true,
        cellEditorPopupPosition: 'under',
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'name',
        headerName: '이름',
        editable: true,
        valueSetter: (params) => {
          params.data.name = params.newValue;
          return true;
        },
        valueParser: (params) => params.newValue.trim(),
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 20 },
        cellEditorSelector: () => ({ component: 'agTextCellEditor' }),
        cellEditorPopup: false,
        cellEditorPopupPosition: 'over',
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'price',
        headerName: '가격',
        editable: (params) => params.data.isEditable,
        valueSetter: (params) => {
          params.data.price = Number(params.newValue);
          return true;
        },
        valueParser: (params) => Number(params.newValue),
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 8 },
        cellEditorSelector: () => ({ component: 'agTextCellEditor' }),
        cellEditorPopup: true,
        cellEditorPopupPosition: 'over',
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'category',
        headerName: '카테고리',
        editable: true,
        valueSetter: (params) => {
          params.data.category = params.newValue;
          return true;
        },
        valueParser: (params) => params.newValue.trim(),
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: ['전자', '의류', '식품', '가전', '도서'],
        },
        singleClickEdit: true,
        useValueParserForImport: false,
      },
      {
        field: 'stock',
        headerName: '재고',
        editable: true,
        valueSetter: (params) => {
          params.data.stock = parseInt(params.newValue);
          return true;
        },
        valueParser: (params) => parseInt(params.newValue),
        cellEditor: 'agTextCellEditor',
        cellEditorParams: { maxLength: 5 },
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'createdAt',
        headerName: '등록일',
        editable: false,
        valueSetter: () => false,
        valueParser: (params) => new Date(params.newValue).toISOString(),
        cellEditor: false,
        singleClickEdit: false,
        useValueParserForImport: false,
      },
      {
        field: 'status',
        headerName: '상태',
        editable: true,
        valueSetter: (params) => {
          params.data.status = params.newValue;
          return true;
        },
        valueParser: (params) => params.newValue.toUpperCase(),
        cellEditor: 'agSelectCellEditor',
        cellEditorParams: {
          values: ['ACTIVE', 'INACTIVE', 'DISCONTINUED'],
        },
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'description',
        headerName: '설명',
        editable: true,
        valueSetter: (params) => {
          params.data.description = params.newValue;
          return true;
        },
        valueParser: (params) => params.newValue,
        cellEditor: 'agLargeTextCellEditor',
        cellEditorParams: { maxLength: 200, rows: 4, cols: 30 },
        singleClickEdit: false,
        cellEditorPopup: true,
        cellEditorPopupPosition: 'under',
      },
      {
        field: 'rating',
        headerName: '평점',
        editable: true,
        valueSetter: (params) => {
          params.data.rating = parseFloat(params.newValue);
          return true;
        },
        valueParser: (params) => parseFloat(params.newValue),
        cellEditor: 'agTextCellEditor',
        singleClickEdit: true,
        useValueParserForImport: true,
      },
      {
        field: 'editorNote',
        headerName: '비고',
        editable: true,
        valueSetter: (params) => {
          params.data.editorNote = params.newValue;
          return true;
        },
        valueParser: (params) => params.newValue,
        cellEditor: 'agLargeTextCellEditor',
        cellEditorParams: { maxLength: 100, rows: 2, cols: 30 },
        singleClickEdit: false,
        cellEditorPopup: true,
        cellEditorPopupPosition: 'under',
      },
    ],
    rowData: [
      {
        id: 1,
        name: '상품 A',
        price: 1200,
        category: '전자',
        stock: 50,
        createdAt: '2024-01-01T10:00:00.000Z',
        status: 'ACTIVE',
        description: '전자 기기 설명입니다.',
        rating: 4.5,
        editorNote: '',
      },
      {
        id: 2,
        name: '상품 B',
        price: 2300,
        category: '의류',
        stock: 120,
        createdAt: '2024-01-02T11:00:00.000Z',
        status: 'INACTIVE',
        description: '봄 신상 의류.',
        rating: 3.8,
        editorNote: '',
      },
      {
        id: 3,
        name: '상품 C',
        price: 1500,
        category: '식품',
        stock: 300,
        createdAt: '2024-01-03T12:00:00.000Z',
        status: 'ACTIVE',
        description: '건강식품입니다.',
        rating: 4.2,
        editorNote: '',
      },
      {
        id: 4,
        name: '상품 D',
        price: 7600,
        category: '가전',
        stock: 15,
        createdAt: '2024-01-04T13:00:00.000Z',
        status: 'DISCONTINUED',
        description: '구형 가전 제품입니다.',
        rating: 2.5,
        editorNote: '',
      },
      {
        id: 5,
        name: '상품 E',
        price: 990,
        category: '도서',
        stock: 80,
        createdAt: '2024-01-05T14:00:00.000Z',
        status: 'ACTIVE',
        description: '인기 도서입니다.',
        rating: 4.8,
        editorNote: '',
      },
      {
        id: 6,
        name: '상품 F',
        price: 3100,
        category: '전자',
        stock: 40,
        createdAt: '2024-01-06T15:00:00.000Z',
        status: 'INACTIVE',
        description: '전자 기기 F 설명',
        rating: 3.0,
        editorNote: '',
      },
      {
        id: 7,
        name: '상품 G',
        price: 4100,
        category: '식품',
        stock: 100,
        createdAt: '2024-01-07T16:00:00.000Z',
        status: 'ACTIVE',
        description: '다이어트 식품',
        rating: 4.0,
        editorNote: '',
      },
      {
        id: 8,
        name: '상품 H',
        price: 8800,
        category: '가전',
        stock: 10,
        createdAt: '2024-01-08T17:00:00.000Z',
        status: 'DISCONTINUED',
        description: '고급 가전 제품입니다.',
        rating: 2.9,
        editorNote: '',
      },
      {
        id: 9,
        name: '상품 I',
        price: 520,
        category: '도서',
        stock: 60,
        createdAt: '2024-01-09T18:00:00.000Z',
        status: 'INACTIVE',
        description: '어린이 도서',
        rating: 4.1,
        editorNote: '',
      },
      {
        id: 10,
        name: '상품 J',
        price: 7600,
        category: '의류',
        stock: 25,
        createdAt: '2024-01-10T19:00:00.000Z',
        status: 'ACTIVE',
        description: '겨울 신상 의류',
        rating: 4.6,
        editorNote: '',
      },
    ],
  },
};

export const SortOptions: Story = {
  render: Template,
  args: {
    columnDefs: [
      {
        field: 'name',
        headerName: '이름',
        sortable: true,
        sort: 'asc',
        sortIndex: 1,
        sortingOrder: ['asc', 'desc', null],
        unSortIcon: true,
      },
      {
        field: 'price',
        headerName: '가격',
        sortable: true,
        initialSort: 'desc',
        initialSortIndex: 0,
      },
      {
        field: 'category',
        headerName: '카테고리',
        sortable: true,
        sortIndex: 2,
        sortingOrder: ['desc', 'asc', null],
      },
      {
        field: 'stock',
        headerName: '재고',
        sortable: true,
        comparator: (a, b, nodeA, nodeB, isDescending) => {
          const aVal = Number(a);
          const bVal = Number(b);
          return isDescending ? bVal - aVal : aVal - bVal;
        },
      },
      {
        field: 'rating',
        headerName: '평점',
        sortable: true,
        unSortIcon: true,
        sortingOrder: ['asc', null],
      },
      {
        field: 'createdDate',
        headerName: '등록일',
        sortable: true,
        initialSort: 'desc',
        initialSortIndex: 1,
      },
      {
        field: 'brand',
        headerName: '브랜드',
        sortable: true,
        sortingOrder: ['desc', 'asc'],
      },
      {
        field: 'sales',
        headerName: '판매량',
        sortable: true,
        comparator: (a, b) => Number(b) - Number(a),
      },
    ],
    rowData: [
      {
        name: '갤럭시 S24',
        price: 1200000,
        category: '전자제품',
        stock: 30,
        rating: 4.5,
        createdDate: '2024-12-01',
        brand: '삼성',
        sales: 8500,
      },
      {
        name: '아이폰 15',
        price: 1350000,
        category: '전자제품',
        stock: 12,
        rating: 4.7,
        createdDate: '2024-11-20',
        brand: '애플',
        sales: 9200,
      },
      {
        name: '에어프라이어',
        price: 89000,
        category: '가전',
        stock: 58,
        rating: 4.2,
        createdDate: '2023-09-15',
        brand: '쿠쿠',
        sales: 4300,
      },
      {
        name: '울트라북 노트북',
        price: 2100000,
        category: '전자제품',
        stock: 7,
        rating: 4.8,
        createdDate: '2024-06-10',
        brand: 'LG',
        sales: 3100,
      },
      {
        name: '게이밍 의자',
        price: 230000,
        category: '가구',
        stock: 20,
        rating: 4.1,
        createdDate: '2024-03-02',
        brand: '시크릿랩',
        sales: 2900,
      },
      {
        name: '무선 청소기',
        price: 640000,
        category: '가전',
        stock: 15,
        rating: 3.9,
        createdDate: '2023-12-25',
        brand: '다이슨',
        sales: 5600,
      },
      {
        name: '커피 머신',
        price: 180000,
        category: '가전',
        stock: 25,
        rating: 4.0,
        createdDate: '2024-01-19',
        brand: '네스프레소',
        sales: 4700,
      },
      {
        name: '기계식 키보드',
        price: 150000,
        category: '컴퓨터 주변기기',
        stock: 40,
        rating: 4.6,
        createdDate: '2023-08-05',
        brand: '로지텍',
        sales: 6700,
      },
      {
        name: '스마트워치',
        price: 370000,
        category: '웨어러블',
        stock: 18,
        rating: 4.3,
        createdDate: '2024-10-10',
        brand: '삼성',
        sales: 5300,
      },
      {
        name: '블루투스 이어폰',
        price: 110000,
        category: '음향기기',
        stock: 100,
        rating: 4.4,
        createdDate: '2024-05-30',
        brand: '소니',
        sales: 7500,
      },
    ],
  },
};

export const HeaderGroupOptions: Story = {
  render: Template,
  args: {
    columnDefs: [
      {
        headerName: '제품 정보',
        headerClass: '',
        headerGroupComponentParams: { fontWeight: 'bold' },
        headerTooltip: '제품 정보 그룹',
        suppressSpanHeaderHeight: true,
        suppressStickyLabel: true,
        children: [
          {
            headerName: '제품명',
            field: 'name',
            headerTooltip: '제품명 툴팁',
            headerClass: 'header-product',
            autoHeaderHeight: true,
            groupId: 'product-info', // 그룹 고유 ID
            marryChildren: true, // 자식 컬럼을 항상 함께 움직이도록 설정
            openByDefault: true, // 기본적으로 그룹 열기
          },
          {
            headerName: '가격',
            field: 'price',
            headerTooltip: '가격 툴팁',
            headerClass: 'header-price',
            autoHeaderHeight: true,
          },
        ],
      },
      {
        headerName: '기타 정보',
        headerGroupComponentParams: { fontWeight: 'normal' },
        headerTooltip: '기타 정보 그룹',
        children: [
          {
            headerName: '카테고리',
            field: 'category',
            headerTooltip: '카테고리 툴팁',
            headerClass: 'header-category',
            marryChildren: true,
            openByDefault: false, // 기본적으로 그룹 닫기
            columnGroupShow: 'open', // 그룹이 열릴 때만 보일 컬럼 설정
          },
          {
            headerName: '재고',
            field: 'stock',
            headerTooltip: '재고 툴팁',
            headerClass: 'header-stock',
          },
          {
            headerName: '평점',
            field: 'rating',
            headerTooltip: '평점 툴팁',
            headerClass: 'header-rating',
          },
        ],
      },
    ],
    rowData: [
      { name: '갤럭시 S24', price: 1200000, category: '전자제품', stock: 30, rating: 4.5 },
      { name: '아이폰 15', price: 1350000, category: '전자제품', stock: 12, rating: 4.7 },
      { name: '에어프라이어', price: 89000, category: '가전', stock: 58, rating: 4.2 },
      { name: '울트라북 노트북', price: 2100000, category: '전자제품', stock: 7, rating: 4.8 },
      { name: '게이밍 의자', price: 230000, category: '가구', stock: 20, rating: 4.1 },
    ],
    defaultColDef: {
      resizable: true,
      sortable: true,
      filter: true,
      tooltipShowDelay: 0,
      floatingFilter: true,
    },
  },
};

export const DisplayOptions: Story = {
  render: Template,
  args: {
    columnDefs: [
      {
        headerName: 'ID',
        field: 'id',
        pinned: 'left',
        lockPosition: 'left',
        width: '10px',
      },
      {
        headerName: '이름',
        field: 'name',
        pinned: 'left',
        lockPosition: true,
        width: '20px',
      },
      {
        headerName: '입사일',
        field: 'joinDate',
        suppressMovable: true,
        valueFormatter: ({ value }) => new Date(value).toLocaleDateString('ko-KR'),
      },
      {
        headerName: '연봉',
        field: 'salary',
        hide: true,
        valueFormatter: ({ value }) => `₩${value.toLocaleString('ko-KR')}`,
      },
      {
        headerName: '재택 근무',
        field: 'remote',
      },
      {
        headerName: '성과 점수',
        field: 'performance',
        valueFormatter: ({ value }) => `${value.toFixed(1)} / 5.0`,
      },
      {
        headerName: '프로젝트 수',
        field: 'projectCount',
      },
      {
        headerName: '출근일 수',
        field: 'workDays',
      },
      {
        headerName: '야근 여부',
        field: 'overtime',
        cellRenderer: ({ value }) => (value ? '⭕' : '❌'), //셀 값이 boolean 타입일 경우, 자동으로 체크박스 스타일 적용 막는법
      },
      {
        headerName: '기여도',
        field: 'contribution',
        valueFormatter: ({ value }) => `${value}%`,
      },
      {
        headerName: '건강검진 여부',
        field: 'healthCheck',
        editable: true,
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },

      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '메모',
        field: 'note',
      },

      {
        headerName: '메모',
        field: 'note',
      },
      {
        headerName: '비고',
        field: 'etc',
        pinned: 'right',
        lockPosition: 'right', // 우측 고정은 허용, 그 외 위치로 이동 불가
        suppressMovable: true,
      },
    ],
    rowData: [
      {
        id: 101,
        name: 'Alice Kim',
        role: 'Frontend Developer',
        level: 'Senior',
        joinDate: '2021-03-15',
        salary: 85000000,
        remote: true,
        performance: 4.5,
        projectCount: 12,
        workDays: 215,
        overtime: false,
        contribution: 95,
        healthCheck: true,
        note: '우수한 프론트 역량 보유',
        etc: '정보치리기사자격증,sqld자격증,데이터분석가,파이썬',
      },
      {
        id: 102,
        name: 'Brian Choi',
        role: 'Backend Developer',
        level: 'Junior',
        joinDate: '2023-01-02',
        salary: 56000000,
        remote: false,
        performance: 3.8,
        projectCount: 4,
        workDays: 178,
        overtime: true,
        contribution: 75,
        healthCheck: false,
        note: '성장 잠재력 높음',
        etc: '',
      },
      {
        id: 103,
        name: 'Claire Lee',
        role: 'Product Manager',
        level: 'Mid',
        joinDate: '2022-08-10',
        salary: 74000000,
        remote: true,
        performance: 4.2,
        projectCount: 7,
        workDays: 202,
        overtime: false,
        contribution: 88,
        healthCheck: true,
        note: '',
        etc: '',
      },
      {
        id: 104,
        name: 'David Park',
        role: 'UX Designer',
        level: 'Senior',
        joinDate: '2020-05-27',
        salary: 79000000,
        remote: false,
        performance: 4.7,
        projectCount: 15,
        workDays: 220,
        overtime: true,
        contribution: 91,
        healthCheck: false,
        note: '디자인 시스템 개선 주도',
        etc: '',
      },
      {
        id: 105,
        name: 'Emma Yoon',
        role: 'QA Engineer',
        level: 'Mid',
        joinDate: '2022-12-01',
        salary: 62000000,
        remote: true,
        performance: 4.0,
        projectCount: 9,
        workDays: 198,
        overtime: false,
        contribution: 82,
        healthCheck: true,
        note: '',
        etc: '',
      },
    ],
    defaultColDef: {
      resizable: true,
      sortable: true,
      filter: true,
      floatingFilter: true,
    },
  },
};

export const FilterOptions: Story = {
  args: {
    columnDefs: [
      {
        headerName: '도서 제목',
        field: 'title',
        filter: 'agTextColumnFilter',
        filterParams: { caseSensitive: false },
        floatingFilter: true,
      },
      {
        headerName: '저자',
        field: 'author',
        filter: 'agTextColumnFilter',
        floatingFilter: true,
      },
      {
        headerName: '장르',
        field: 'genre',
        filter: 'agSetColumnFilter',
        suppressFiltersToolPanel: false,
      },
      {
        headerName: '대출일',
        field: 'borrowedDate',
        filter: 'agDateColumnFilter',
        filterParams: {
          comparator: (filterDate: Date, cellValue: string) => {
            const cellDate = new Date(cellValue);
            if (cellDate < filterDate) return -1;
            if (cellDate > filterDate) return 1;
            return 0;
          },
        },
        floatingFilter: true,
      },
      {
        headerName: '반납 예정일',
        field: 'dueDate',
        filter: 'agDateColumnFilter',
        floatingFilter: true,
      },
      {
        headerName: '연체 여부',
        field: 'isOverdue',
        filter: 'agDateColumnFilter',
        valueFormatter: (params) => (params.value ? '연체' : '정상'),
      },
      {
        headerName: '퀵 필터 대상',
        field: 'searchableText',
        filter: 'agTextColumnFilter',
        tooltipValueGetter: (params) => `${params.data.title} ${params.data.author} ${params.data.genre}`,
        valueGetter: (params) => `${params.data.title} ${params.data.author} ${params.data.genre}`,
        getQuickFilterText: (params) => `${params.data.title} ${params.data.author} ${params.data.genre}`,
      },
      {
        headerName: '대출 기간',
        field: 'borrowDuration',
        filter: 'agNumberColumnFilter',
        filterParams: {
          inRangeInclusive: true,
        },
      },
    ],
    rowData: [
      {
        title: '자바스크립트 완벽 가이드',
        author: 'David Flanagan',
        genre: '프로그래밍',
        borrowedDate: '2024-03-01',
        dueDate: '2024-03-15',
        isOverdue: false,
        borrowDuration: 14, // 대출 기간
      },
      {
        title: '클린 코드',
        author: 'Robert C. Martin',
        genre: '소프트웨어 공학',
        borrowedDate: '2024-02-10',
        dueDate: '2024-02-24',
        isOverdue: true,
        borrowDuration: 14,
      },
      {
        title: '이것이 취업을 위한 코딩 테스트다',
        author: '나동빈',
        genre: '알고리즘',
        borrowedDate: '2024-03-25',
        dueDate: '2024-04-08',
        isOverdue: false,
        borrowDuration: 14,
      },
      {
        title: 'Do it! Vue.js 3',
        author: '장기효',
        genre: '프론트엔드',
        borrowedDate: '2024-01-15',
        dueDate: '2024-01-29',
        isOverdue: true,
        borrowDuration: 14,
      },
      {
        title: '누구나 자료구조와 알고리즘',
        author: 'Jay Wengrow',
        genre: '알고리즘',
        borrowedDate: '2024-03-10',
        dueDate: '2024-03-24',
        isOverdue: false,
        borrowDuration: 14,
      },
      {
        title: '리팩터링 2판',
        author: 'Martin Fowler',
        genre: '소프트웨어 공학',
        borrowedDate: '2024-02-28',
        dueDate: '2024-03-13',
        isOverdue: false,
        borrowDuration: 14,
      },
      {
        title: '타입스크립트 핸드북',
        author: 'Boris Cherny',
        genre: '프로그래밍',
        borrowedDate: '2024-03-20',
        dueDate: '2024-04-03',
        isOverdue: false,
        borrowDuration: 14,
      },
    ],
  },
};
