import axios from 'axios';
import qs from 'qs';
import { useLoadingStore } from '@store/useLoadingStore';
import { useAlertStore } from '@store/useAlertStore';
// import { useSessionTimeoutStore } from '@store/useSessionTimeoutStore';
import { useLogStore } from '@store/useLogStore';

const api = axios.create({
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
  },
});

api.defaults.paramsSerializer = (params) => {
  const filteredParams = Object.fromEntries(Object.entries(params).filter(([_, value]) => value));
  return qs.stringify(filteredParams);
};

// 요청 인터셉터
api.interceptors.request.use(
  (config) => {    
    useLoadingStore.getState().setIsLoading(true);

    // 헤더에 globalTraceId 담기
    config.headers.globalTraceId = useLogStore.getState().logParams.globalTraceId;

    // 헤더에 accesstoken 담기
    const accessToken = localStorage.getItem('accessToken');

    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    } else {
      useLoadingStore.getState().setIsLoading(false);
      window.location.href = '/login';
    }

    return config;
  },
  (error) => {
    useLoadingStore.getState().setIsLoading(false);
    console.log('인터셉터 실패');
    window.location.href = '/login';

    return Promise.reject(error);
  }
);

// 응답 인터셉터
api.interceptors.response.use(
  (response) => {
    useLoadingStore.getState().setIsLoading(false);

    // 세션 만료 시간 저장
    // const sessionTimeout = response.data.sessionTimeout;

    // if (sessionTimeout) {
    //   useSessionTimeoutStore.getState().setSessionTimeout(sessionTimeout);
    // }

    // 응답값에 auth token 있을 경우
    const authorization = response.headers['Authorization'];

    if (authorization) {
      // token 갱신
      const accessToken = authorization?.replace(/^Bearer\s/, '') || '';

      if (accessToken) {
        localStorage.setItem('accessToken', accessToken);
        console.log('토큰 갱신됐음');
      } else {
        console.log('갱신 실패:: 로그인 페이지로 이동');
        localStorage.removeItem('accessToken');
        window.location.href = '/login';
      }
    }

    return response;
  },
  (error) => {
    // apiErrorHandler.ts 에서 처리
    return Promise.reject(error);
  }
);

export default api;
