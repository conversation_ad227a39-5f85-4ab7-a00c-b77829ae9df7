import api from '@api/api';
import { ServiceGroupData } from '@page/Admin/Service/type';
import { defaultHandleError } from '@utils/apiErrorHandler';

export const useAdminServiceGroupAPI = () => {
  const ADMIN_SERVICE_GROUP = '/api-admin/admin/service-groups';

  // 서비스 그룹 목록 조회
  interface GetServiceGroupListParams {
    isGetErrorMsg?: boolean;
  }

  const getServiceGroupList = async ({ isGetErrorMsg = false }: GetServiceGroupListParams) => {
    try {
      const response = await api.get(ADMIN_SERVICE_GROUP);
      return response.data.data;
    } catch (error) {
      defaultHandleError(error, isGetErrorMsg, '서비스 그룹 목록 조회');
    }
  };

  // 서비스 그룹 삭제
  interface DeleteServiceGroupParams {
    id: number;
    isGetErrorMsg?: boolean;
  }

  const deleteServiceGroup = async ({ id, isGetErrorMsg = false }: DeleteServiceGroupParams) => {
    try {
      const response = await api.post(`${ADMIN_SERVICE_GROUP}/${id}/delete`);
      return response.data.message;
    } catch (error) {
      defaultHandleError(error, isGetErrorMsg, '서비스 그룹 삭제');
    }
  };

  // 서비스 그룹 등록
  interface AddServiceGroupParams {
    data: ServiceGroupData;
    isGetErrorMsg?: boolean;
  }

  interface AddServiceGroupResponse {
    message: string;
    data: ServiceGroupData;
  }

  const addServiceGroup = async ({
    data,
    isGetErrorMsg = false,
  }: AddServiceGroupParams): Promise<AddServiceGroupResponse | Error | undefined> => {
    try {
      const response = await api.post(ADMIN_SERVICE_GROUP, data);
      return { message: response.data.message, data: response.data.data };
    } catch (error) {
      defaultHandleError(error, isGetErrorMsg, '서비스 그룹 등록');
    }
  };

  // 서비스 그룹 수정

  interface UpdateServiceGroupParams {
    id: number;
    data: ServiceGroupData;
    isGetErrorMsg?: boolean;
  }

  const updateServiceGroup = async ({ id, data, isGetErrorMsg = false }: UpdateServiceGroupParams) => {
    try {
      const response = await api.post(`${ADMIN_SERVICE_GROUP}/${id}`, data);
      return response.data.message;
    } catch (error) {
      defaultHandleError(error, isGetErrorMsg, '서비스 그룹 수정');
    }
  };

  return { getServiceGroupList, deleteServiceGroup, addServiceGroup, updateServiceGroup };
};
