import { useAlertStore } from "store/useAlertStore";

export const useAlert = () => {
  const { setAlertState } = useAlertStore();

  const openAlert = (content: string, onConfirm: () => void = null) => {
    setAlertState({
      isOpen: true,
      content: content,
      onCancel: closeAlert,
      onConfirm: () => {
        onConfirm && onConfirm();
        closeAlert();
      },
    });
  };

  const closeAlert = () => {
    setAlertState({
      isOpen: false,
      content: '',
      onConfirm: () => {},
    });
  };

  return { openAlert, closeAlert };
};