import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';

const errorMessage = '서비스 목록 조회 중 오류가 발생했습니다.';

export const useUserServiceAPI = () => {
  const USER_SERVICE = '/api-admin/user/services';
  const USER_SERVICE_BY_GROUP = '/api-admin/user/services/group';
  const { serviceGroupDetail, setServiceList, setIsOpenServiceModal, setOriginalServiceList } = useServiceStore();
  const { openAlert } = useAlert();

  // 서비스 목록 조회
  const getServiceList = async () => {
    try {
      const response = serviceGroupDetail
        ? await api.get(`${USER_SERVICE_BY_GROUP}/${serviceGroupDetail.id}`) // 그룹별 서비스 호출
        : await api.get(USER_SERVICE); // 전체 서비스 호출

      const serviceListData = response.data.data;
      setServiceList(serviceListData);
      setOriginalServiceList(serviceListData);
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 삭제
  const deleteServiceData = async (id: number) => {
    try {
      const response = await api.post(`${USER_SERVICE}/${id}/delete`);
      openAlert(response.data.message, () => {
        getServiceList();
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 등록
  const addServiceData = async (data: any) => {
    try {
      const response = await api.post(USER_SERVICE, data);
      setIsOpenServiceModal(false);
      openAlert(response.data.message, () => {
        getServiceList();
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  // 서비스 수정
  const updateServiceData = async (id: number, data: any) => {
    try {
      const response = await api.post(`${USER_SERVICE}/${id}`, data);
      setIsOpenServiceModal(false);
      openAlert(response.data.message, () => {
        getServiceList();
      });
    } catch (error) {
      openAlert(error.response?.data?.message || errorMessage);
    }
  };

  return { getServiceList, deleteServiceData, addServiceData, updateServiceData };
};
