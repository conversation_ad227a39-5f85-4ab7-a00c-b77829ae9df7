import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import { UseFormReturn } from 'react-hook-form';
import { useUserAccountListStore } from '../store/useUserAccountListStore';
import { ListFormValues, UserListParams } from '../type';
import { useNavigate } from 'react-router-dom';

/**
 * 사용자 계정 목록 API 훅 Props 인터페이스
 */
interface UseUserAccountListAPIProps {
  searchMethods?: UseFormReturn<ListFormValues>; // 검색 폼 메서드 (선택적)
}

export const useUserAccountListAPI = ({ searchMethods }: UseUserAccountListAPIProps = {}) => {
  const navigate = useNavigate();
  const USER_ACCOUNT = '/api-admin/users';
  const { setUserListData, setPaginationData, currentPage, pageSize } = useUserAccountListStore();
  const { openAlert } = useAlert();

  // 계정 목록 조회
  const getAccountList = async () => {
    try {
      const params: UserListParams = {
        page: currentPage - 1,
        size: pageSize,
        ...searchMethods?.getValues(),
        activeYn: searchMethods.getValues('activeYn')?.value,
      };
      const response = await api.get(`${USER_ACCOUNT}`, { params });
      const data = response.data;
      setUserListData(data.data);
      setPaginationData(data.pageInfo);
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  // 계정 삭제
  const deleteAccount = async (id: string | number) => {
    try {
      const response = await api.post(`${USER_ACCOUNT}/${id}/delete`);
      const data = response.data;
      openAlert(data.message, () => {
        getAccountList();
      });
    } catch (error) {
      openAlert(error?.response?.data?.message);
    }
  };

  const goAddAccount = () => {
    navigate('/user/account/add');
  };

  const goEditAccount = (id: number | string) => {
    navigate(`/user/account/edit/${id}`);
  };

  return { getAccountList, deleteAccount, goAddAccount, goEditAccount };
};
