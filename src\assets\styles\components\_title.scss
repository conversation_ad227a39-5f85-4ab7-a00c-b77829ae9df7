@use '@styles/utils/variables' as v;
@use '@styles/utils/mixin' as m;

.c_title {
  @include m.flex(end, space-between);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--g_05);

  &_text {
    font-size: 1.5rem;
    line-height: 1.5rem;
    font-weight: 600;
  }

  .c_bread_crumb {
    @include m.flex(end, center);
    gap: 0.5rem;

    span {
      font-size: 1rem;
      line-height: 1rem;
      font-weight: 500;
      color: var(--g_07);

      &:not(:last-child) {
        &::after {
          content: ' >';
          font-weight: 500;
        }
      }

      &:last-child {
        font-weight: 800;
      }
    }
  }

  @include m.bp_large() {
    &_text {
      font-size: 1.375rem;
      line-height: 1.375rem;
      font-weight: 600;
    }

    .c_bread_crumb {
      span {
        font-size: 0.875rem;
        line-height: 0.875rem;
      }
    }
  }

  @include m.bp_medium() {
    &_text {
      font-size: 1.25rem;
      line-height: 1.25rem;
      font-weight: 500;
    }
    .c_bread_crumb {
      span {
        font-size: 0.75rem;
        line-height: 0.75rem;
      }
    }
  }
}

.c_sub_title {
  &_text {
    font-size: 1.25rem;
    line-height: 2.25rem;
    font-weight: 600;
  }

  @include m.bp_large() {
    &_text {
      font-size: 1.125rem;
      line-height: 1.125rem;
    }
  }

  @include m.bp_medium() {
    &_text {
      font-size: 1rem;
      line-height: 1rem;
    }
  }
}
