import TestButtonCompWrapper from './TestTable/Button/TestButtonCompWrapper';
import TestCheckRadio from './TestTable/CheckRadio/TestCheckRadio';
import TestChips from './TestTable/Chip/TestChips';
import TestFilebox from './TestTable/Filebox/TestFilebox';
import TestInputCompWrapper from './TestTable/Input/TestInputCompWrapper';
import TestModal from './TestTable/Modal/TestModal';
import TestPagination from './TestTable/Pagination/TestPagination';
import TestSelectBox from './TestTable/SelectBox/TestSelectBox';
import TestTextEditor from './TestTable/TextEditor/TestTextEditor';

const TestComponents = () => {
  return (
    <div className="test_components">
      <h1 className="subject_title">Button Components</h1>
      <TestButtonCompWrapper />
      <h1 className="subject_title">Input Field Components</h1>
      <TestInputCompWrapper />
      <h1 className="subject_title">Checkbox & Radio Components</h1>
      <TestCheckRadio />
      <h1 className="subject_title">Select</h1>
      <TestSelectBox />
      <h1 className="subject_title">Pagination</h1>
      <TestPagination />
      <h1 className="subject_title">Modal</h1>
      <TestModal />
      <h1 className="subject_title">Chip</h1>
      <TestChips />
      <h1 className="subject_title">Text Editor</h1>
      <TestTextEditor />
      <h1 className="subject_title">Filebox</h1>
      <TestFilebox />
    </div>
  );
};

export default TestComponents;
