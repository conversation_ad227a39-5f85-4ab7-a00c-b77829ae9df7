import ReactDOM from 'react-dom';
import { ReactNode, useEffect } from 'react';
import joinClassName from '@utils/joinClassName';

interface ModalPortalProps {
  children: ReactNode;
  onClickedDim?: (props?: any) => void; // dim 클릭했을 때 실행 함수
  transparentDim?: boolean; // dim이 투명한 경우
  isTopVisible?: boolean; //최상위 모달이어야 하는 경우우
}

/**모달 기본 portal */
const ModalPortal = ({ children, onClickedDim = () => {}, transparentDim = false, isTopVisible }: ModalPortalProps) => {
  // modal 팝업 시 scroll lock
  useEffect(() => {
    document.documentElement.style.overflow = 'hidden';

    return () => {
      document.documentElement.style.overflow = '';
    };
  }, []);

  return ReactDOM.createPortal(
    <>
      {children}
      <div
        className={joinClassName('c_dim', transparentDim ? 'transparent' : '', isTopVisible ? 'top_active' : '')}
        onClick={onClickedDim}
      />
    </>,
    document.getElementById('modal-root')
  );
};

export default ModalPortal;
