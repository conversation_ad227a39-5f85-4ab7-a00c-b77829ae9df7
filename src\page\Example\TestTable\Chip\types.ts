import { IconType } from '@components/common/Button/types';

export interface InputChipProps extends ChipProps {
  onDelete: () => void;
  imgSrc?: string;
}

export interface ActionChipProps extends ChipProps {
  imgSrc?: string;
  active?: boolean;
}

export interface FilterChipProps extends ChipProps {
  active: boolean;
}

export interface ChoiceChipProps extends ChipProps {
  active: boolean;
  icon?: IconType;
}

export interface InputChipState {
  isFocused: boolean;
  isHovered: boolean;
  isChecked?: boolean;
}

export interface ChipProps {
  label: string;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  onClick?: () => void;
}
