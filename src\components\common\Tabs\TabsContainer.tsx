import { createContext, useContext, useEffect } from 'react';
import { createTabStore } from './store/createTabStore';
import type { StoreApi, UseBoundStore } from 'zustand';

interface TabStore {
  activeTab: string;
  setActiveTab: (tabId: string) => void;
}

type TabStoreType = UseBoundStore<StoreApi<TabStore>>;

const TabContext = createContext<TabStoreType | null>(null);

export const useTabContext = () => {
  const context = useContext(TabContext);
  if (!context) {
    throw new Error('Tab 컴포넌트는 TabsContainer 내부에서만 사용할 수 있습니다.');
  }
  return context;
};

interface TabsContainerProps {
  children: React.ReactNode;
  defaultTab: string;
  containerId: string;
}

export const TabsContainer: React.FC<TabsContainerProps> = ({ children, defaultTab, containerId }) => {
  const store = createTabStore(containerId);
  const setActiveTab = store.getState().setActiveTab;

  useEffect(() => {
    if (defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab]);

  return (
    <TabContext.Provider value={store}>
      <div className="c_tabs_container">{children}</div>
    </TabContext.Provider>
  );
};
