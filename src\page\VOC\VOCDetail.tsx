import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import joinClassName from '@utils/joinClassName';
import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import TextEditor from '@components/common/TextEditor/TextEditor';
import VOCCard from './components/VOCCard';
import VOCDetailInfoItem from './components/VOCDetailInfoItem';
import { useVOCStore } from './store/useVOCStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { useAlertStore } from '@store/useAlertStore';
import { parseSelectOptions } from '@page/VOC/hooks/useVOC';
import { postVOCComment, getVOCCategoryOptions, getVOCStatusOptions, getVOCDetail, editVOCStatusAndCategory } from '@api/admin/vocAPI';

const VOCDetail = () => {
  // 라우팅 이동 핸들러
  const navigate = useNavigate();
  // URL 파라미터에서 VOC ID 추출
  const { id } = useParams();


  // VOC 관련 전역 상태 관리
  const { VOCDetail, categoryOptions, statusOptions, setCategoryOptions, setStatusOptions, setVOCDetail } =
    useVOCStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const { activeAlert } = useAlertStore();

  // form 관련 기본값 설정
  const methods = useForm({
    defaultValues: {
      status: statusOptions.find((option) => option.value === VOCDetail?.vocInfo?.status),
      category: categoryOptions.find((option) => option.value === VOCDetail?.vocInfo?.category),
    },
  });

  // 수정 모드 상태 관리
  const [isEdit, setIsEdit] = useState<boolean>(false);
  // VOC 댓글 값
  const [VOCComment, setVOCComment] = useState<string>('');

  // 댓글 등록 핸들러
  const handleClickRegister = async () => {
    if (VOCComment) {
      setConfirmState({
        isOpen: true,
        content: '등록하시겠습니까?',
        onConfirm: async () => {
          const resultMessage = await postVOCComment({ vocId: Number(id), content: VOCComment });
          if (resultMessage) {
            initConfirmState();
            setVOCComment('');
            getVOCDetailData();
            activeAlert(resultMessage);
          }
        },
        onCancel: initConfirmState,
      });
    } else {
      activeAlert('코멘트를 입력해주세요.');
    }
  };

  // 수정 버튼 클릭 핸들러
  const handleClickEdit = () => {
    if (isEdit) {
      setConfirmState({
        isOpen: true,
        content: '수정하시겠습니까?',
        onConfirm: () => {
          handleSubmit(methods.getValues());
          initConfirmState();
          setIsEdit(false);
        },
        onCancel: () => {
          initConfirmState();
        },
      });
    } else {
      setIsEdit(true);
    }
  };

  const handleClickList = () => {
    navigate(`/voc`);
  }

  // VOC 카테고리 옵션 데이터 조회
  const getVOCCategoryOptionsData = async () => {
    const data = await getVOCCategoryOptions({});
    setCategoryOptions(parseSelectOptions({ data }));
  };

  // VOC 상태 옵션 데이터 조회
  const getVOCStatusOptionsData = async () => {
    const data = await getVOCStatusOptions({});
    setStatusOptions(parseSelectOptions({ data }));
  };

  // VOC 상세 데이터 조회
  const getVOCDetailData = async () => {
    const data = await getVOCDetail({ vocId: Number(id) });
    setVOCDetail(data);
  };

  // VOC 상태 및 카테고리 수정 제출 핸들러
  const handleSubmit = async (data: any) => {
    const parseData = {
      category: data.category.value,
      status: data.status.value,
    };
    const resultMessage = await editVOCStatusAndCategory({ vocId: Number(id), data: parseData });
    if (resultMessage) {
      getVOCDetailData();
      activeAlert(resultMessage);
    }
  };

  // 초기 데이터 로드
  useEffect(() => {
    getVOCDetailData();
    getVOCCategoryOptionsData();
    getVOCStatusOptionsData();
  }, []);

  return (
    <div className='voc'>
      <div className="voc_detail">
        <div className="voc_detail_inner content horizontal reverse">
          {/* 왼쪽 영역: 댓글 리스트 및 댓글 작성 */}
          <div className="left_content">
            <ul className="voc_card_list">
              {VOCDetail?.commentList.map((comment: any) => (
                <VOCCard
                  key={comment.id}
                  content={comment.content}
                  createUserType={comment.createUserType}
                  updateUserType={comment.updateUserType}
                  id={comment.id}
                  vocId={comment.vocId}
                  dateInfo={comment.dateInfo}
                />
              ))}
            </ul>
            {/* 댓글 작성 에디터 */}
            <div className="voc_text_editor">
              <div className="voc_text_editor_inner">
                <div className="left_cont">
                  <div className="user_icon"></div>
                </div>
                <div className="right_cont">
                  <div className="voc_text_editor_header">
                    <h3 className="user_name">
                      {localStorage.getItem('loginUsername')}
                      <span>{' <로그인 유저권한>'}</span>
                    </h3>
                  </div>
                  <TextEditor className="voc_text_editor_content" onChange={setVOCComment} content={VOCComment} />
                  <div className="voc_text_editor_footer">
                    <Button text="목록" color="grayscale" onClick={handleClickList} />
                    <Button text="등록" onClick={handleClickRegister} />
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* 오른쪽 영역: VOC 상세 정보 */}
          <Form className="right_content" onSubmit={handleSubmit} methods={methods}>
            <h1 className="c_title">
              <p className="c_title_text">{VOCDetail?.vocInfo?.title}</p>
            </h1>
            {VOCDetail?.vocInfo && (
              <ul className="voc_detail_info">
                {/* VOC 상태 정보 */}
                <VOCDetailInfoItem label="상태">
                  {isEdit ? (
                    <FormSelectBox
                      options={statusOptions}
                      defaultValue={statusOptions.find((option) => option.value === VOCDetail.vocInfo.status)}
                      name="status"
                    />
                  ) : (
                    <span className={joinClassName('voc_status_card', VOCDetail.vocInfo.status)}>
                      {VOCDetail.vocInfo.status}
                    </span>
                  )}
                </VOCDetailInfoItem>
                {/* VOC 카테고리 정보 */}
                <VOCDetailInfoItem label="카테고리">
                  {isEdit ? (
                    <FormSelectBox
                      options={categoryOptions}
                      defaultValue={categoryOptions.find((option) => option.value === VOCDetail.vocInfo.category)}
                      name="category"
                    />
                  ) : (
                    <span className="value">
                      {categoryOptions.find((option) => option.value === VOCDetail.vocInfo.category)?.label}
                    </span>
                  )}
                </VOCDetailInfoItem>
                {/* 수정 관련 정보 */}
                {VOCDetail.vocInfo?.dateInfo?.updateUser && (
                  <>
                    <VOCDetailInfoItem
                      label="수정자"
                      children={<span className="value">{VOCDetail.vocInfo.dateInfo.updateUser}</span>}
                    />
                    <VOCDetailInfoItem
                      label="수정일"
                      children={<span className="value">{VOCDetail.vocInfo.dateInfo.updateDate}</span>}
                    />
                  </>
                )}
                {/* 등록 관련 정보 */}
                <VOCDetailInfoItem
                  label="등록자"
                  children={<span className="value">{VOCDetail.vocInfo.dateInfo.createUser}</span>}
                />
                <VOCDetailInfoItem
                  label="등록일"
                  children={<span className="value">{VOCDetail.vocInfo.dateInfo.createDate}</span>}
                />
                <VOCDetailInfoItem
                  label="조회수"
                  children={<span className="value">{VOCDetail.vocInfo.viewCnt}</span>}
                />
              </ul>
            )}
            <div className="voc_detail_footer">
              {isEdit && (
                <Button
                  text="취소"
                  color="grayscale"
                  className="voc_detail_cancel_btn"
                  onClick={() => {
                    setIsEdit(false);
                    methods.reset();
                  }}
                />
              )}
              <Button text="수정" className="voc_detail_edit_btn" onClick={handleClickEdit} />
            </div>
          </Form>
        </div>
      </div>      
    </div>
  );
};

export default VOCDetail;
