type SwitchCellEditorProps = {
  value: boolean;
  onValueChange: (val: boolean) => void;
};

const SwitchCellEditor = ({ value, onValueChange }: SwitchCellEditorProps) => {
  return (
    <div className="flex justify-center items-center h-full w-full">
      <label className="relative inline-flex items-center cursor-pointer">
        <input
          type="checkbox"
          checked={value}
          onChange={({ target: { checked } }) => onValueChange(checked)}
          className="sr-only peer"
          autoFocus
        />
        <div className="w-10 h-5 bg-gray-300 rounded-full peer peer-checked:bg-[#5089ef] transition-colors"></div>
        <div className="absolute left-0.5 top-0.5 w-4 h-4 bg-white rounded-full transition-transform peer-checked:translate-x-5"></div>
      </label>
    </div>
  );
};

export default SwitchCellEditor;
