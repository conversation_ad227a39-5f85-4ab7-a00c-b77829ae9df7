import { useEffect, useRef, useState } from 'react';
import { getFrontLogs, getFrontLogsCsv } from '@api/admin/logManageApi';
import adminSystemLogFields from '@page/System/adminSystemLogFields.json';

import CalendarRange, { DateRange } from '@components/common/Calendar/CalendarRange';
import ControlBox from '@components/common/ControlBox/ControlBox';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import Button from '@components/common/Button/Button';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import SelectBox from '@components/common/SelectBox/SelectBox';
import Loading from '@components/common/Loading';
import { FrontLogType } from '@page/System/FrontLog/type';

import dayjs from 'dayjs';
import { useForm } from 'react-hook-form';
import { useConfirmStore } from '@store/useConfirmStore';
import { useDownloadingStore } from '@store/useLoadingStore';
import { useAlertStore } from '@store/useAlertStore';
import { ColDef, ColGroupDef } from '@ag-grid-community/core';

const FrontLog = () => {
  const { isDownloading } = useDownloadingStore();
  const { setAlertState } = useAlertStore();
  const { setConfirmState } = useConfirmStore();
  const [isGridLoading, setIsGridLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(0);
  const [pageSize, setPageSize] = useState<{ label: string; value: number }>({ label: '1,000', value: 1000 });
  const gridRef = useRef(null);
  const [gridData, setGridData] = useState<FrontLogType[]>([]);
  const [gridTotalData, setGridTotalData] = useState<number>(0);
  const [downloadParams, setDownloadParams] = useState<{
    startDate: Date;
    endDate: Date;
    loginId: string;
    globalTraceId: string;
    menuName: string;
  }>({
    startDate: new Date(dayjs().subtract(31, 'days').startOf('day').toDate()),
    endDate: new Date(dayjs().toDate()),
    loginId: '',
    globalTraceId: '',
    menuName: '',
  });
  const [column] = useState<(ColDef | ColGroupDef)[] | null>([
    {
      field: 'seq',
      headerName: 'No.',
      sortable: false,
    },
    ...adminSystemLogFields.FRONT_LOG.map((field) =>
      field.field === 'actionDate' // 날짜 포맷팅
        ? {
            ...field,
            valueFormatter: (params) => dayjs(params.value).format('YYYY-MM-DD HH:mm'),
          }
        : field
    ),
  ]);

  const searchMethods = useForm<any>({
    defaultValues: {
      loginId: '',
      globalTraceId: '',
      menuName: '',
      period: {
        startDate: new Date(dayjs().subtract(31, 'days').startOf('day').toDate()),
        endDate: new Date(dayjs().toDate()),
      },
    },
  });

  const { setValue, getValues } = searchMethods;

  // 기간 선택
  const handleDateRangeChange = (range: DateRange) => {
    setValue('period', range);
  };

  // 로그인 로그 데이터 조회
  const getFrontLogData = async () => {
    setIsGridLoading(true);

    try {
      const loginIdData = getValues('loginId');
      const globalTraceIdData = getValues('globalTraceId');
      const menuNameData = getValues('menuName');
      const preiodData = getValues('period');

      // 엑셀 다운로드 params 저장
      setDownloadParams({
        startDate: preiodData.startDate,
        endDate: preiodData.endDate,
        loginId: loginIdData,
        globalTraceId: globalTraceIdData,
        menuName: menuNameData,
      });

      const data = await getFrontLogs({
        page: page,
        size: pageSize.value,
        startDate: preiodData.startDate,
        endDate: preiodData.endDate,
        loginId: loginIdData,
        globalTraceId: globalTraceIdData,
        menuName: menuNameData,
      });

      if (data) {
        setGridData(() => {
          return data.data.map((newData, index) => {
            return {
              ...newData,
              seq: data.pageInfo.totalCount - data.pageInfo.pageSize * data.pageInfo.currentPage - index,
            };
          });
        });

        setGridTotalData(data.pageInfo.totalCount);
      }
    } finally {
      setIsGridLoading(false);
    }
  };

  // 최초 조회 / 페이지 이동 시 / 데이터 사이즈 변경 시
  useEffect(() => {
    getFrontLogData();
  }, [page, pageSize]);

  // 조회 버튼
  const handleSubmit = async () => {
    setPage(0);
    getFrontLogData();
  };

  // 엑셀 다운로드
  const handleCsvDownload = async () => {
    setConfirmState((prev) => ({ ...prev, isOpen: false }));

    try {
      await getFrontLogsCsv({
        startDate: downloadParams.startDate,
        endDate: downloadParams.endDate,
        loginId: downloadParams.loginId,
        globalTraceId: downloadParams.globalTraceId,
        menuName: downloadParams.menuName,
      });
    } finally {
      setAlertState({
        isOpen: true,
        content: '다운로드가 완료되었습니다',
        onConfirm: () => {
          setAlertState({
            isOpen: false,
            content: '',
            onConfirm: () => {},
          });
        },
      });
    }
  };

  return (
    <div className="manage_log">
      <Form onSubmit={handleSubmit} methods={searchMethods}>
        <ControlBox className="manage_log_control_box">
          <div className="manage_log_search">
            <FormInput autoComplete="off" name="loginId" label="로그인 ID" placeholder="로그인 ID를 입력하세요" />
            <FormInput
              autoComplete="off"
              name="globalTraceId"
              label="글로벌 추적 ID"
              placeholder="글로벌 추적 ID를 입력하세요"
            />
            <FormInput autoComplete="off" name="menuName" label="메뉴명" placeholder="메뉴명을 입력하세요" />
            <label htmlFor="period">기간</label>
            <CalendarRange
              onChange={handleDateRangeChange}
              selectedDate={{
                startDate: new Date(dayjs().subtract(31, 'days').startOf('day').toDate()),
                endDate: new Date(dayjs().toDate()),
              }}
              desc="* 최대 1년까지 조회 가능합니다."
              // maxPeriod={31}
              maxPeriod={365}
              maxDate={new Date(dayjs().format('YYYY-MM-DD'))}
            />
            <Button text="조회" type="submit" clickLog={{ buttonSection: '검색창' }} />
          </div>
        </ControlBox>
      </Form>
      <div className="manage_log_content">
        <div className="manage_log_content_control">
          <div className="manage_log_content_control_data_size">
            <p>전체 {gridTotalData.toLocaleString()} 건</p>
            <SelectBox
              selectedValue={pageSize}
              setSelectedValue={setPageSize}
              defaultValue={pageSize}
              options={[
                { label: '500', value: 500 },
                { label: '1,000', value: 1000 },
                { label: '3,000', value: 3000 },
                { label: '5,000', value: 5000 },
                { label: '10,000', value: 10000 },
              ]}
            />
          </div>
          <Button
            text="엑셀 다운로드"
            clickLog={{ buttonSection: '엑셀 다운로드' }}
            onClick={() => {
              setConfirmState({
                isOpen: true,
                className: 'manage_log_excel',
                confirmType: null,
                title: '엑셀 다운로드',
                content: (
                  <>
                    <p>
                      아래 조건으로 다운로드를 진행합니다.
                      <br />
                      파일 크기에 따라 다운로드에 시간이 다소 소요될 수 있습니다.
                    </p>
                    <ul>
                      <li>
                        {`기간: ${dayjs(downloadParams.startDate).format('YYYY-MM-DD')} ~ ${dayjs(downloadParams.endDate).format('YYYY-MM-DD')}`}
                      </li>
                      <li>로그인 ID: {!!downloadParams.loginId ? downloadParams.loginId : '전체'}</li>
                      <li>글로벌 추척 ID: {!!downloadParams.globalTraceId ? downloadParams.globalTraceId : '전체'}</li>
                      <li>메뉴명: {!!downloadParams.menuName ? downloadParams.menuName : '전체'}</li>
                    </ul>
                  </>
                ),
                onCancel: () => setConfirmState((prev) => ({ ...prev, isOpen: false })),
                onConfirm: () => handleCsvDownload(),
              });
            }}
          />
        </div>
        <Grid
          isLoading={isGridLoading}
          ref={gridRef}
          columns={column}
          rowData={gridData}
          autoSizeStrategy={{ type: 'fitCellContents' }}
        />
        <Pagination
          pageCount={10}
          currentPage={page + 1}
          totalCount={gridTotalData}
          itemCountPerPage={pageSize.value}
          onPageChange={(e) => {
            setPage(e - 1);
          }}
        />
      </div>
      <Loading isLoading={isDownloading} />
    </div>
  );
};

export default FrontLog;
