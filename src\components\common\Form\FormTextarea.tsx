import TextArea from '@components/common/Input/TextArea';
import { TextAreaProps } from '@components/common/Input/types';
import { Controller, useFormContext } from 'react-hook-form';

interface FormTextareaProps extends TextAreaProps {
  name: string;
  rules?: object;
}

const FormTextarea = ({ name, rules, ...attributes }: FormTextareaProps) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState }) => (
        <div className="c_textarea_wrapper">
          <TextArea
            {...field}
            {...attributes}
            error={fieldState.error?.message} // 에러 메시지 표시
          />
        </div>
      )}
    />
  );
};

export default FormTextarea;
