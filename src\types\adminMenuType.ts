import { IconType } from '@components/common/Button/types';
import { DateInfo } from '@page/Admin/Account/type';

export type Menu = {
  id: number;
  url: string;
  name: string;
  sortNo: number;
  depth: number;
  useYn: string;
  desc: string;
  parentId: string | number | null;
  icon?: IconType;
  childMenus?: Menu[];
  childPages?: Page[];
  pageId: number,
  pageName: string,
  pageTypeCode: string,
  filePath: string,
  pageFileName: string,
  newWindowYn: string,
  dateInfo: {
    createDate: Date;
    createUser: string | null;
    updateDate: Date;
    updateUser: string | null;
  };
};

export type Page = {
  id: number | null;
  parentId: number | null;
  boardTempletId: number | null;
  pageName: string;
  pageFileName: string;
  pageContent: string | null;
  pageLink: string | null;
  // 페이지 타입 코드 : P(페이지, 상세), L(리스트), R(등록), M(수정), B(게시판), T(테스트)
  pageTypeCode: 'P' | 'L' | 'R' | 'M' | 'B' | 'T';
  pageDesc: string | null;
  roleUseYn: string | null;
  pageRole: string | null;
  dateInfo: DateInfo;
  filePath: string;
}