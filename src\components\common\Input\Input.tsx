import { forwardRef } from 'react';
import joinClassName from '@utils/joinClassName';
import ErrorMsg from '@components/common/Error/ErrorMsg';
import { InputProps } from './types';

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      placeholder, // placeholder(*)

      /**input 디자인 요소 */
      className, // 추가 클래스 요소
      design = 'default',
      align = 'left',
      inputSize = 'medium',
      error,
      ...attributes
    },
    ref
  ) => {
    const inputClass = joinClassName('c_input', align, design, inputSize, className);

    return (
      <>
        <input ref={ref} placeholder={placeholder} className={inputClass} {...attributes} />
        <ErrorMsg text={error} />
      </>
    );
  }
);

export default Input;
