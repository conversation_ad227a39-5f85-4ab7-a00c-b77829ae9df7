import _ from 'lodash';

declare global {
  const _: typeof _;
  const utils: {
    joinClassName: (...classNames: (string | undefined | false | null)[]) => string;
    getLabel: <T extends string | number>(value: T | undefined, options: { label: string; value: T }[]) => string;
    getValue: <T extends string | number>(
      label: string | undefined,
      options: { label: string; value: T }[]
    ) => T | undefined;
    showConfirm: (type: string, content: ReactNode, onConfirm?: () => void, onCancel?: () => void) => void;
    showAlert: (content: string | ReactNode, onConfirm?: () => void) => void;
    flattenValues: (input: ItemType | ItemType[]) => ItemType | ItemType[];
    buildQueryString: (params: Record<string, any>) => string;
    addSeqNo: (data: ItemType[], pageInfo?: PaginationInfo) => ItemType[];
  };
}
