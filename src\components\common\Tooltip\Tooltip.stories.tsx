import { Meta, StoryObj } from '@storybook/react';
import Tooltip from './Tooltip';
import { TooltipProps } from './types';

/**
 * 자세한 옵션 설명은 {@link TooltipProps} 참고
 */

const meta: Meta<typeof Tooltip> = {
  title: 'Components/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
  args: {
    id: 'tooltip-test',
    content: '툴팁 내용입니다.',
    place: 'top',
    offset: 8,
    className: '',
    classNameArrow: '',
    variant: 'dark',
    wrapper: 'span',
    openOnClick: false,
    positionStrategy: 'absolute',
    delayShow: 0,
    delayHide: 0,
    float: false,
    noArrow: false,
    clickable: false,
    globalCloseEvents: { escape: true },
    imperativeModeOnly: false,
    style: {},
    defaultIsOpen: false,
    hidden: false,
    disableTooltip: () => false,
    border: '',
    opacity: 0.9,
    arrowColor: '',
    disableStyleInjection: false,
    role: 'tooltip',
  },
};
export default meta;

type Story = StoryObj<typeof Tooltip>;

export const Defalut: Story = {};

export const AddImg: Story = {
  args: {
    render: ({ content }) => (
      <div style={{ textAlign: 'center' }}>
        <img src="" alt="kitten" />
        <div>{content}</div>
      </div>
    ),
    content: '이미지 툴팁',
  },
};

export const JSXTooltip: Story = {
  args: {
    children: <button>JSX요소에도 툴팁을 적용할 수 있어요</button>,
  },
};

export const openEvents: Story = {
  args: {
    openEvents: { click: true },
  },
};

export const closeEvents: Story = {
  args: {
    closeEvents: { click: true },
  },
};

export const onlyUsingTooltipIcon: Story = {
  args: {
    id: '',
    content: '',
  },
};
