import IconButton from '@components/common/Button/IconButton';
import ListTable from '@components/common/Table/ListTable';
import { useAuthStore } from '@page/Admin/Role/store/useAuthStore';
import { AuthData } from '@page/Admin/Role/type';

interface AuthListProps {
  getAuthDetailInfoData: (id: number) => void;
  handleOnClickDeleteAuth: (e: React.MouseEvent, id: number) => void;
}

const AuthList = ({ getAuthDetailInfoData, handleOnClickDeleteAuth }: AuthListProps) => {
  const { authListData, authData } = useAuthStore();

  return (
    <ListTable
      title="권한명"
      list={authListData.map((data: AuthData, idx) => (
        <div
          key={`auth-list-${idx}`}
          className={data.id === authData?.id ? 'active' : ''}
          onClick={() => getAuthDetailInfoData(data.id)}
        >
          <span>{data.name}</span>
          <IconButton
            icon="close"
            iconOnly
            fill="unfilled"
            design="circle"
            color="grayscale"
            text="삭제"
            onClick={(e: React.MouseEvent) => handleOnClickDeleteAuth(e, data.id)}
            clickLog={{ buttonSection: '권한리스트' }}
          />
        </div>
      ))}
    />
  );
};

export default AuthList;
