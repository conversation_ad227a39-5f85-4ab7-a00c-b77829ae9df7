import { SettingType } from '@page/System/Setting/types';
import { create } from 'zustand';

interface SettingStore {
  // 설정 타입
  settingType: SettingType;
  setSettingType: (data: SettingType) => void;

  // 설정 타입 목록
  settingTypeListData: any;
  setSettingTypesData: (data: any) => void;

  // 설정 목록
  settingListData: any;
  setSettingListData: (data: any) => void;
}

export const useSettingStore = create<SettingStore>((set) => ({
  settingType: 'NORMAL',
  setSettingType: (data) => set({ settingType: data }),

  // 데이터 초기 상태 및 설정 함수
  settingTypeListData: {
    code: '',
    desc: '',
  },
  setSettingTypesData: (data) => set({ settingTypeListData: data }),

  settingListData: null,
  setSettingListData: (data) => set({ settingListData: data }),
}));
