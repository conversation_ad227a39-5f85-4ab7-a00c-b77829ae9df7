import Loading from '@components/common/Loading';
import Title from '@components/common/Title/Title';
import { useState } from 'react';
import { useLoadingStore } from '@store/useLoadingStore';
import CategoryList from './Components/CategoryList';
import MenuDetail from './Components/MenuDetail';

const UserMenuPage = () => {
  const [clickedMenuId, setClickedMenuId] = useState(0);
  const { isLoading } = useLoadingStore();

  return (
    <div className="manage_user_menu">
      <h1 className="c_title">
        <p className="c_title_text">사용자 메뉴 관리</p>
      </h1>
      <div className="manage_user_contents">
        <div className="manage_user_contents_left">
          <h1 className="c_title">
            <p className="c_title_text">카테고리 리스트</p>
          </h1>
          <CategoryList handleClickedMenuId={setClickedMenuId} clickedMenuId={clickedMenuId} />
        </div>
        <div className="manage_user_contents_right">
          <h1 className="c_title">
            <p className="c_title_text">메뉴 정보</p>
          </h1>
          <MenuDetail categoryId={clickedMenuId} />
        </div>
      </div>
      <Loading isLoading={isLoading} />
    </div>
  );
};

export default UserMenuPage;
