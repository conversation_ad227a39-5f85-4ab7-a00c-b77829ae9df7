import React, { useEffect, useState } from 'react';
import InputChip from '@components/common/Chip/InputChip';
import ActionChip from '@components/common/Chip/ActionChip';
import FilterChip from '@components/common/Chip/FilterChip';
import ChoiceChip from '@components/common/Chip/ChoiceChip';
import profileImg from '@assets/images/favicon-32x32.png';
import TestTable from '@page/Example/TestTable/TestTable';

const TestChips = () => {
  const [activeAction, setActiveAction] = useState(false);
  const [activeFilter, setActiveFilter] = useState(false);
  const [activeChoice, setActiveChoice] = useState(false);

  const handleActionActive = () => {
    setActiveAction(!activeAction);
  };

  const handleFilterActive = () => {
    setActiveFilter(!activeFilter);
  };

  const handleChoiceActive = () => {
    setActiveChoice(!activeChoice);
  };

  useEffect(() => {}, [activeChoice, activeFilter]);

  return (
    <TestTable
      compName="chips"
      headChild={
        <>
          <tr>
            <th colSpan={4}>Chips</th>
          </tr>
          <tr>
            <th style={{ width: '11%' }}>Status</th>
            <th style={{ width: '33%' }}>default</th>
            <th style={{ width: '33%' }}>with Image</th>
            <th style={{ width: '33%' }}>active</th>
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr>
            <th>InputChip</th>
            <td>
              <InputChip label="chip button ok???" onDelete={() => {}} />
            </td>
            <td>
              <InputChip label="chip button ok???" imgSrc={profileImg} onDelete={() => {}} />
            </td>
            <td>-</td>
          </tr>
          <tr>
            <th>ActionChip</th>
            <td>
              <ActionChip label="chip button ok???" active={activeAction} onClick={handleActionActive} />
            </td>
            <td>
              <ActionChip label="chip button ok???" imgSrc={profileImg} active={false} />
            </td>
            <td>
              <ActionChip label="chip button ok???" imgSrc={profileImg} active={true} />
            </td>
          </tr>
          <tr>
            <th>FilterChip</th>
            <td>
              <FilterChip label="FilterChip has no max-width" active={activeFilter} onClick={handleFilterActive} />
            </td>
            <td>-</td>
            <td>
              <FilterChip label="FilterChip has no max-width" active />
            </td>
          </tr>
          <tr>
            <th>ChoiceChip</th>
            <td>
              <ChoiceChip label="chip button ok???" active={activeChoice} onClick={handleChoiceActive} />
            </td>
            <td>-</td>
            <td>
              <ChoiceChip label="chip button ok???" active />
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestChips;
