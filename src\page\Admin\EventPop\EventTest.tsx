import { getEventPopupNow } from '@api/admin/eventPopupAPI';
import { useEventStore } from '@store/useEventStore';
import React, { useEffect, useState } from 'react';
import EventModal from '@components/common/Modal/EventModal';
import SubTitle from '@components/common/Title/SubTitle';
import Button from '@components/common/Button/Button';
import ControlBox from '@components/common/ControlBox/ControlBox';
import { useNavigate } from 'react-router-dom';
import EventPopupPreview from '@page/Admin/EventPop/components/EventPopupPreview';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';

const EventTest = () => {
  const navigate = useNavigate();
  const { eventPopupData, setEventPopupData } = useEventStore();
  const [deActivePopList, setDeActivePopList] = useState<string[]>([]);

  const getAllStorageList = () => {
    return Object.keys(localStorage)
      .filter((key) => key.includes('no_repeat_event'))
      .reduce((obj, key) => {
        obj[key] = localStorage.getItem(key);
        return obj;
      }, {});
  };

  const getEventPopupData = async () => {
    const data = await getEventPopupNow({});
    setEventPopupData(data);
  };

  const getDeActivePopList = () => {
    const allStorageList = getAllStorageList();
    setDeActivePopList(Object.keys(allStorageList));
  };

  const handleClickReload = () => {
    window.location.reload();
  };

  const handleClickStorageDelete = () => {
    // localStorage에서 'no_repeat_event'를 포함하는 모든 항목 가져오기
    const allStorageList = getAllStorageList();

    // no_repeat_event 키를 포함하는 모든 항목 삭제
    Object.entries(allStorageList).forEach(([key, _]) => {
      localStorage.removeItem(key);
    });

    handleClickReload();
  };

  const checkDeActivePop = (id: number) => {
    return deActivePopList.includes('no_repeat_event_' + id.toString());
  };

  useEffect(() => {
    if (!localStorage.getItem('accessToken')) return;
    getEventPopupData();
    getDeActivePopList();
  }, []);

  return (
    <div className="admin_event_popup">
      <div className="admin_event_popup_test">
        <ControlBox>
          <label>현재 활성화된 팝업 중 "다시 보지 않기"를 한 경우 블러 처리가 됩니다.</label>
        </ControlBox>
        <TableContainerHeader
          leftChildren={<SubTitle>현재 활성화 된 이벤트 팝업</SubTitle>}
          rightChildren={
            <div className="button_wrapper">
              <Button text="목록" color="grayscale" onClick={() => navigate('/admin/event_popup')} />
              <Button text="새로 고침" fill="outlined" onClick={handleClickReload} />
              <Button text="쿠키 삭제 후 새로 고침" onClick={handleClickStorageDelete} />
            </div>
          }
        />
        <div className="event_popup_list">
          {eventPopupData &&
            eventPopupData.map((data) => (
              <div className="event_popup_preview_wrapper" key={data.id}>
                <EventPopupPreview
                  title={data.title}
                  subTitle={data.subTitle}
                  linkUrl={data.linkUrl}
                  notOpenDay={{ label: data.notOpenDay === 1 ? '하루' : '일주일', value: data.notOpenDay }}
                  responseImageList={data.fileInfo}
                  imageFiles={null}
                />
                {checkDeActivePop(data.id) && <div className="deactive_pop_cover" />}
              </div>
            ))}
        </div>
        {eventPopupData && eventPopupData.map((data) => <EventModal key={data.id} data={data} />)}
      </div>
    </div>
  );
};

export default EventTest;
