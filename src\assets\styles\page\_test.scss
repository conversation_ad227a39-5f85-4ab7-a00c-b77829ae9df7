@use '@styles/utils/mixin' as m;

.test_components {
  width: 100%;
  .subject_title {
    font-size: 2rem;
    line-height: 2.3rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
  }
  .c_table_wrapper {
    margin-bottom: 20px;
  }
  table {
    width: 100%;
    text-align: center;

    td,
    th {
      .example_wrapper {
        @include m.inline_flex(center, center);
        gap: 20px;
      }
      button {
        margin: 0 auto;
      }
      .button_wrapper {
        margin-top: 20px;
        button {
          margin: 0;
        }
      }
      .c_input,
      .c_input_wrapper,
      .c_searchbar_wrapper {
        width: 210px;
      }
      .c_searchbar_wrapper {
        margin: 0 auto;
        .c_input {
          width: 100%;
        }
      }
    }
  }
}
