interface CalcNoSortProps {
  totalCount: number;
  currentPage: number;
  pageSize: number;
  index: number;
}

export const calcNoSort = ({ totalCount, currentPage, pageSize, index }: CalcNoSortProps) => {
  if (totalCount < currentPage * pageSize) {
    return 0 + totalCount - index;
  }

  return totalCount - currentPage * pageSize - index;
};

interface AddCalcNoSortProps {
  data: any[];
  pageInfo: {
    totalCount: number;
    currentPage: number;
    pageSize: number;
  };
}

export const addCalcNoSort = ({ data, pageInfo }: AddCalcNoSortProps) => {
  return data.map((item, index) => {
    return {
      ...item,
      sort: calcNoSort({
        totalCount: pageInfo.totalCount,
        currentPage: pageInfo.currentPage,
        pageSize: pageInfo.pageSize,
        index,
      }),
    };
  });
};
