@use '@styles/utils/mixin' as m;

.c_control_box {
  @include m.flex(center);
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.25rem;
  margin-bottom: 1.25rem;
  background-color: var(--control_box);

  .c_select,
  .c_input {
    min-width: 225px;
    max-width: 300px;
    width: 100%;
  }

  label {
    font-size: 1rem;
    font-weight: 600;
  }

  &.divide {
    justify-content: space-between;

    > .left,
    > .right {
      @include m.flex(center);
      gap: 1rem;
    }
  }

  @include m.bp_large() {
    padding: 1.25rem;

    .c_select,
    .c_input {
      min-width: 200px;
    }

    label {
      font-size: 0.875rem;
    }
  }

  @include m.bp_medium() {
    padding: 1rem;

    .c_select,
    .c_input {
      min-width: 175px;
    }

    label {
      font-size: 0.75rem;
    }
  }
}
