import type * as AgGridTypes from '@ag-grid-community/core';

// @ag-grid-community/core 에서 제공하지 않는 타입 선언
export type GetFindTextFunc = (params: { value: any; data: any; node: AgGridTypes.RowNode }) => string;
export type HeaderStyle = any;
export type HeaderStyleFunc = (params: {
  colDef: AgGridTypes.ColDef;
  column: any;
  columnApi: any;
  api: any;
  context: any;
}) => any;

/**************************************************************************
 * 문서용 타입입니다.
 * 실제 사용은 ColDef를 따르며, 이 타입은 설명 및 가이드 참고용입니다.
 ***************************************************************************/

export interface AgColumnOptions {}

// [ 컬럼 기본 옵션]
export interface BaseOptions {
  /**
   * 셀 데이터에 매핑되는 필드명
   * - 용도: row object에서 값을 가져올 키
   * - 예시: 'name', 'address.street'
   * - 기본값: undefined
   */
  field?: string;

  /**
   * 컬럼 고유 ID
   * - 용도: API에서 컬럼을 식별할 때 사용
   * - 예시: 'userId'
   * - 기본값: field와 동일, 없을 경우 자동 생성
   */
  colId?: string;

  /**
   * 컬럼 타입 설정
   * - 용도: 공통 ColumnType을 설정하여 중복 제거
   * - 예시: 'numericColumn', ['currencyColumn', 'centeredText']
   * - 기본값: undefined
   */
  type?: string | string[];

  /**
   * 셀 데이터 타입 설정
   * - 용도: 셀 값의 데이터 타입 명시 또는 추론
   * - 예시: true, false, 'number', 'text', 'boolean', 'date', 'object'
   * - 기본값: true
   * - 참고: AG Grid 29 이상에서 사용 가능
   */
  cellDataType?: boolean | string;

  /**
   * 셀 값을 가져오는 함수 또는 식
   * - 용도: 데이터를 동적으로 가공하여 표시
   * - 예시: 'data.name + "님"', (params) => `${params.data.price}원`
   * - 기본값: undefined
   */
  valueGetter?: string | ((params: AgGridTypes.ValueGetterParams) => any);

  /**
   * 셀 값을 포맷하는 함수 또는 식
   * - 용도: 셀의 값을 문자열로 변환하여 포맷팅
   * - 예시: (params) => `$${params.value.toFixed(2)}`
   * - 기본값: undefined
   */
  valueFormatter?: string | ((params: AgGridTypes.ValueFormatterParams) => string);

  /**
   * 참조 데이터 매핑 객체
   * - 용도: 내부 값 ↔ 표시 값 매핑
   * - 예시: { 'M': 'Male', 'F': 'Female' }
   * - 기본값: undefined
   * - 필터 또는 valueFormatter에서 주로 사용됨
   */
  refData?: { [key: string]: string };

  /**
   * 고유 키 생성 함수
   * - 용도: Set 필터, 편집기 dropdown 등에서 고유 문자열 키 생성(숫자나 객체처럼 기본적으로 문자열 변환이 애매한 값들에 대해)
   * - 예시: (value) => `key-${value}`
   * - 기본값: undefined
   */
  keyCreator?: (value: any) => string;

  /**
   * 값 비교 함수
   * - 용도: 셀 값 변경 여부 감지 ( true면 셀렌더링X )
   * - 예시: (oldValue, newValue) => oldValue.id === newValue.id
   * - 기본값: (a, b) => a === b
   */
  equals?: (valueA: any, valueB: any) => boolean;

  /**
   * 셀 포커스 탭키 이동 가능 여부 또는 조건 함수
   * - 용도: 특정 셀에 탭키 이동 허용/제한
   * - 예시: true, false, (params) => params.node.rowIndex !== 0
   * - 기본값: false
   */
  suppressNavigable?: boolean | ((params: any) => boolean);

  /**
   * 키보드 이벤트를 억제할 조건 함수
   * - 용도: 특정 키보드 입력 무시
   * - 예시: (params) => params.event.key === 'Enter'
   * - 기본값: false
   */
  suppressKeyboardEvent?: (params: any) => boolean;

  /**
   * 사용자 정의 컨텍스트 정보 전달
   * - 용도: 커스텀 렌더러, 에디터 등에 context 전달
   * - 예시: { userId: 123 }
   * - 기본값: undefined
   * - 기본값: 그리드옵션 context도 추가해야함.
   *  - <AgGridReact
             className="ag-theme-quartz"
             ref={ref}
             columnDefs={columnDefs}
             rowData={rowData}
             {...props}
            context={{ user: '관리자' }}
           />
   */
  context?: any;

  /**
   * 셀에 설정할 ARIA role
   * - 용도: 접근성 향상을 위한 ARIA 역할 설정
   * - 예시: 'gridcell', (params) => params.value > 0 ? 'note' : 'gridcell'
   * - 기본값: undefined
   */
  cellAriaRole?: string | ((params: any) => string);
}

// [디스플레이 옵션]
export interface DisplayOptions {
  /**
   * 컬럼 숨김 여부
   * - 용도: 컬럼을 화면에서 숨김 처리
   * - 예시: true
   * - 기본값: false
   */
  hide?: boolean;

  /**
   * 초기 렌더링 시 숨김 처리
   * - 용도: 최초 로드시만 숨김. 이후에는 상태 유지되지 않음
   * - 예시: true
   * - 기본값: false
   * - 참고: `columnState` 복원 시 기본값 적용됨
   */
  initialHide?: boolean;

  /**
   * 컬럼 위치 잠금
   *
   * 이 옵션은 컬럼이 왼쪽(`pinned: 'left'`) 또는 오른쪽(`pinned: 'right'`)에 고정되어 있을 때,
   * 사용자가 해당 고정 상태를 변경하지 못하도록 잠그는 역할
   *
   * 예를 들어:
   * - `lockPosition: true` → 현재 설정된 고정 위치를 유지하고, 사용자가 드래그 등으로 이동할 수 없게함.
   * - `lockPosition: 'left'` → 이 컬럼은 반드시 왼쪽에만 고정되며, 다른 위치로 옮길 수 없음.
   * - `lockPosition: 'right'` → 이 컬럼은 반드시 오른쪽에만 고정되며, 다른 위치로 옮길 수 없음.
   *
   * 기본값: `false` (잠그지 않음, 사용자가 위치를 자유롭게 변경할 수 있음)
   *
   * 참고: 이 옵션은 `pinned`과 함께 사용하면 효과.
   */
  lockPosition?: boolean | 'left' | 'right';

  /**
   * 컬럼 이동 제한
   * - 용도: 드래그로 컬럼 위치 이동을 제한
   * - 예시: true
   * - 기본값: false
   */
  suppressMovable?: boolean;
}

//Todo: 검증필요
// [ Editable 관련 컬럼 옵션 (Editable 관련) ]
export interface EditableOptions {
  /**
   * 셀 편집 가능 여부 또는 조건
   * - 용도: 셀을 편집 가능하게 설정하거나 조건부 편집 허용
   * - 예시: true, false, (params) => params.data.editable
   * - 기본값: false
   */
  editable?: boolean | AgGridTypes.EditableCallback<any>;

  /**
   * 셀 입력값을 실제 값으로 설정하는 함수
   * - 용도: 사용자가 입력한 값을 데이터에 반영
   * - 예시: (params) => { params.data.value = params.newValue; return true; }
   * - 기본값: undefined
   *
   */
  valueSetter?: string | AgGridTypes.ValueSetterFunc<any>;

  /**
   * 셀 입력값을 파싱하여 내부 값으로 변환
   * - 용도: 문자열 입력 등을 가공 후 저장
   * - 예시: (params) => Number(params.newValue)
   * - 기본값: undefined
   */
  valueParser?: string | AgGridTypes.ValueParserFunc<any>;

  /**
   * 셀 에디터 컴포넌트 설정
   * - 용도: 기본 또는 사용자 정의 에디터 지정
   * - 예시: 'agTextCellEditor', MyCustomEditorComponent
   * - 기본값: 'agTextCellEditor'
   */
  cellEditor?: string | { new (): AgGridTypes.ICellEditorComp } | boolean;

  /**
   * 셀 에디터에 전달할 파라미터 객체
   * - 용도: 에디터에 추가 설정 값 전달
   * - 예시: { maxLength: 10 }
   * - 기본값: undefined
   */
  cellEditorParams?: any;

  /**
   * 동적으로 에디터를 선택하는 함수
   * - 용도: 조건에 따라 에디터를 다르게 설정
   * - 예시: (params) => ({ component: 'numericEditor' })
   */
  cellEditorSelector?: AgGridTypes.CellEditorSelectorFunc;

  /**
   * 셀 에디터를 팝업 형태로 띄울지 여부
   * - 용도: 셀 내부가 아닌 외부 팝업으로 에디터 표시
   * - 예시: true
   * - 기본값: false
   */
  cellEditorPopup?: boolean;

  /**
   * 팝업 에디터의 위치
   * - 용도: 셀 기준 팝업의 위치 설정
   * - 예시: 'over', 'under'
   * - 기본값: 'over'
   */
  cellEditorPopupPosition?: 'over' | 'under';

  /**
   * 클릭 한 번으로 바로 편집 시작
   * - 용도: 더블클릭 없이 편집 모드 진입
   * - 예시: true
   * - 기본값: false
   */
  singleClickEdit?: boolean;

  /**
   * valueParser를 import 시에도 적용
   * - 용도: clipboard 또는 csv import 시에도 파싱 적용
   * - 예시: true
   * - 기본값: false
   */
  useValueParserForImport?: boolean;
}

// [ 필터 관련 옵션 ]
export interface FilterOptions {
  /**
   * 필터 종류 지정
   * - 용도: 필터 타입 설정 ('agTextColumnFilter', 'agNumberColumnFilter', 커스텀 컴포넌트 등)
   * - 예시:
   *    - 'agSetColumnFilter'  : 기본 필터
   *    - 'agTextColumnFilter' : 기본 텍스트 필터
   *    - 'agNumberColumnFilter' : 기본 숫자 필터
   *    - 'agDateColumnFilter' : 날짜 필터
   *    - CustomFilterComponent : 커스텀 필터 컴포넌트
   *
   * - 주의 : 필터타입을 명시적으로 주어도 셀데이터의 타입에 따라 자동으로 매치됨.
   *
   */ filter?: any;

  /**
   * 필터에 전달할 추가 파라미터
   * - 용도: 각 필터 유형별 추가 설정
   * - 예시: { debounceMs: 500, caseSensitive: true }
   */
  filterParams?: any;

  /**
   * 필터 기준으로 사용할 값 정의 함수
   * - 용도: 기본 셀 값이 아닌 별도의 값을 필터링 기준으로 사용할 때
   * - 예시: 'data.name', (params) => params.data.name
   */
  filterValueGetter?: string | AgGridTypes.ValueGetterFunc<any>;

  /**
   * 퀵 필터에 사용할 텍스트 정의
   * - 용도: 기본 값이 아닌 커스텀 텍스트를 퀵 필터 기준으로 설정
   * - 예시: (params) => `${params.data.name} ${params.data.age}`
   */
  getQuickFilterText?: (params: any) => string;

  /**
   * Floating filter 사용 여부
   * - 용도: 컬럼 헤더 아래에 간단한 필터 UI 표시
   * - 기본값: false
   */
  floatingFilter?: boolean;

  /**
   * Floating filter로 사용할 커스텀 컴포넌트
   * - 용도: 기본 UI 대신 사용자 정의 필터 컴포넌트 사용
   */
  floatingFilterComponent?: any;

  /**
   * Floating filter 컴포넌트에 전달할 파라미터
   */
  floatingFilterComponentParams?: any;

  /**
   * Columns Tool Panel에서 필터 UI 숨김 여부
   * - 용도: 툴패널에서 필터 설정을 숨기고 싶을 때
   * - 예시: true
   */
  suppressFiltersToolPanel?: boolean;

  /**
   * 날짜 필터에서 사용할 날짜 선택 컴포넌트
   * - 용도: 기본 날짜 picker 대신 사용자 정의 컴포넌트 사용
   */
  dateComponent?: any;

  /**
   * 사용자 정의 날짜 컴포넌트에 전달할 파라미터
   */
  dateComponentParams?: any;
}

//[ 셀 검색(find) 관련 옵션 ]
export interface FindCellOptions {
  getFindText?: GetFindTextFunc; // 셀 검색 시 비교할 텍스트 반환
}

//[ 헤더 관련 옵션 ]
export interface HeaderOptions {
  /**
   * 헤더 텍스트
   * - 용도: 컬럼의 헤더로 표시할 텍스트
   * - 예시: '이름', '생년월일'
   */
  headerName?: string;

  /**
   * 헤더 텍스트를 동적으로 계산하는 함수 또는 식
   * - 용도: 조건에 따라 헤더명을 동적으로 설정
   * - 예시: (params) => `User: ${params.colDef.field}`
   */
  headerValueGetter?: string | AgGridTypes.HeaderValueGetterFunc;

  /**
   * 헤더 툴팁 텍스트
   * - 용도: 마우스 오버 시 툴팁 표시
   */
  headerTooltip?: string;

  /**
   * 헤더 스타일 설정 (style 객체 또는 함수)
   * - 예시: { fontWeight: 'bold' }, (params) => ({ color: 'red' })
   */
  headerStyle?: HeaderStyle | HeaderStyleFunc;

  /**
   * 헤더 CSS 클래스 설정 (클래스명 또는 조건 함수)
   * - 예시: 'custom-header', (params) => 'header-' + params.colDef.field
   */
  headerClass?: AgGridTypes.HeaderClass;

  /**
   * 커스텀 헤더 컴포넌트
   * - 용도: 기본 헤더 대신 사용자 정의 헤더 사용
   * - 예시: CustomHeaderComponent
   */
  headerComponent?: any;

  /**
   * 커스텀 헤더 컴포넌트에 전달할 파라미터
   */
  headerComponentParams?: any;

  /**
   * 헤더 텍스트 줄바꿈 허용 여부
   * - 예시: true → 긴 텍스트를 줄바꿈하여 표시
   */
  wrapHeaderText?: boolean;

  /**
   * 헤더 높이를 텍스트에 맞춰 자동 조절
   * - 용도: wrapHeaderText와 함께 사용 시 유용
   */
  autoHeaderHeight?: boolean;

  /**
   * 컬럼 메뉴에 표시할 탭 지정
   * - 예시: ['filterMenuTab', 'generalMenuTab', 'columnsMenuTab']
   */
  menuTabs?: AgGridTypes.ColumnMenuTab[];

  /**
   * 헤더의 메뉴 버튼 숨김 여부
   * - 예시: true → 메뉴 버튼 비표시
   */
  suppressHeaderMenuButton?: boolean;

  /**
   * 헤더의 필터 버튼 숨김 여부 (floating filter와 다름)
   * - 예시: true
   */
  suppressHeaderFilterButton?: boolean;

  /**
   * 헤더 영역에서 컨텍스트 메뉴(우클릭) 비활성화
   * - 예시: true
   */
  suppressHeaderContextMenu?: boolean;

  /**
   * 헤더에서 키보드 이벤트 비활성화 조건
   * - 예시: (params) => params.event.key === 'Enter'
   */
  suppressHeaderKeyboardEvent?: (params: any) => boolean;

  /**
   * floating filter의 버튼을 숨김
   * - 예시: true → 헤더에서 X, ≡ 아이콘이 제거됨
   */
  suppressFloatingFilterButton?: boolean;
}

// [고정 관련 옵션]
export interface PinnedOptions {
  /**
   * 컬럼 고정 위치 설정
   * - 용도: 컬럼을 왼쪽('left') 또는 오른쪽('right')에 고정
   * - 예시: 'left', 'right', false, null
   * - 주의: true는 'left'로 해석됨
   */
  pinned?: boolean | 'left' | 'right' | null;

  /**
   * 컬럼 초기 고정 위치 설정 (초기 로드시만 적용)
   * - 용도: pinned와 비슷하지만, 이후 동적 변경에는 반응하지 않음
   * - 예시: 'left', 'right', false
   */
  initialPinned?: boolean | 'left' | 'right';

  /**
   * 사용자의 고정 해제 또는 변경을 방지
   * - 용도: 컬럼을 UI에서 이동하지 못하도록 고정
   * - 예시: true → UI에서 고정 상태 변경 불가
   */
  lockPinned?: boolean;
}

// [드래그 & 드롭 관련 옵션]
export interface RowDragOptions {
  /**
   * 행 드래그 가능 여부 또는 조건부 함수
   * - 용도: row drag 기능 활성화
   * - 예시: true, (params) => params.data.type === 'draggable'
   */
  rowDrag?: boolean | AgGridTypes.RowDragCallback;

  /**
   * 드래그 시 표시할 텍스트를 반환하는 함수
   * - 용도: 행을 드래그할 때 보여줄 텍스트 지정
   * - 예시: (params) => `이름: ${params.data.name}`
   */
  rowDragText?: (params: AgGridTypes.IRowDragItem) => string;

  /**
   * HTML5 DnD 지원을 위한 드래그 시작 조건
   * - 용도: 외부 드래그 처리(ex. 다른 컴포넌트로 drop)
   * - 예시: true, (params) => params.node.selectable
   */
  dndSource?: boolean | AgGridTypes.DndSourceCallback;

  /**
   * DnD 드래그 중 커스텀 동작 처리
   * - 용도: 드래그 이벤트 중 커스텀 작업 수행 (ex. 데이터 전송)
   * - 예시: (params) => console.log(params)
   */
  dndSourceOnRowDrag?: (params: AgGridTypes.RowDragEvent) => void;
}

// [ 그룹핑 관련 옵션 ]
export interface GroupOptions {
  rowGroup?: boolean; // 그룹핑 여부
  initialRowGroup?: boolean; // 초기 그룹핑 여부
  rowGroupIndex?: number | null; // 그룹핑 순서
  initialRowGroupIndex?: number;
  enableRowGroup?: boolean; // 그룹핑 가능 여부
  showRowGroup?: string | boolean; // 어떤 컬럼에서 그룹핑 표현할지
}

// [ 셀 스타일 관련 옵션 ]
export interface RenderStyleOptions {
  /**
   * 셀에 적용할 스타일 (CSS 속성 객체 또는 함수)
   * - 예시: { color: 'red' }, (params) => ({ fontWeight: 'bold' })
   */
  cellStyle?: any;

  /**
   * 셀에 적용할 클래스명 (문자열, 배열, 또는 함수)
   * - 예시: 'highlight', ['center', 'bold'], (params) => 'my-class'
   */
  cellClass?: string | string[] | ((params: any) => string | string[]);

  /**
   * 조건부 클래스 적용 규칙
   * - 예시: { 'negative-value': (params) => params.value < 0 }
   */
  cellClassRules?: { [cssClassName: string]: (params: any) => boolean };

  /**
   * 셀 렌더러 컴포넌트 지정
   * - 예시: MyCustomRenderer, 'agGroupCellRenderer'
   */
  cellRenderer?: any;

  /**
   * 셀 렌더러에 전달할 파라미터
   * - 예시: { color: 'blue' }
   */
  cellRendererParams?: any;

  /**
   * 조건에 따라 동적으로 셀 렌더러 선택
   * - 예시: (params) => ({ component: MyRenderer })
   */
  cellRendererSelector?: (params: any) => {
    component?: any;
    params?: any;
  } | null;

  /**
   * 로딩 셀 렌더러 컴포넌트 지정
   * - 용도: 서버 사이드 행 모델(SSRM) 사용 시, 셀이 로딩 중일 때 표시할 컴포넌트를 지정
   * - 예시: 'agLoadingCellRenderer', MyCustomLoadingRenderer
   * - 기본값: 'agLoadingCellRenderer'
   */
  loadingCellRenderer?: any;

  /**
   * 로딩 셀 렌더러에 전달할 파라미터
   * - 용도: 로딩 셀 렌더러에서 사용할 사용자 정의 속성 전달 (예: 메시지, 스타일 등)
   * - 예시: { loadingMessage: '불러오는 중...', spinnerSize: 'small' }
   * - 기본값: 없음
   */
  loadingCellRendererParams?: any;

  /**
   * 조건에 따라 로딩 셀 렌더러를 동적으로 선택
   * - 용도: 컬럼 또는 행 상태에 따라 다른 렌더러 컴포넌트를 적용할 때 사용
   * - 예시:
   *   (params) => {
   *     return params.colDef.field === 'status'
   *       ? { component: StatusLoadingRenderer, params: { color: 'green' } }
   *       : null;
   *   }
   * - 기본값: 없음 (설정하지 않으면 loadingCellRenderer 사용)
   */
  loadingCellRendererSelector?: (params: any) => {
    component?: any;
    params?: any;
  } | null;

  /**
   * 셀 내용에 따라 행 높이 자동 조절
   * - 예시: true → 텍스트 양에 따라 행 높이 결정
   */
  autoHeight?: boolean;

  /**
   * 셀 텍스트 줄바꿈 설정
   * - 예시: true → 텍스트 줄바꿈 적용
   */
  wrapText?: boolean;

  /**
   * 셀 값 변경 시 깜빡임(플래시) 효과 활성화
   * - 예시: true
   */
  enableCellChangeFlash?: boolean;
}

// [ 정렬 관련 옵션 ]
export interface SortOptions {
  /**
   * - 용도: 컬럼 정렬 기능 사용 여부 설정
   * - 기본값: true
   */
  sortable?: boolean;

  /**
   * - 용도: 기본 정렬 방향 지정
   * - 기본값: undefined
   */
  sort?: 'asc' | 'desc';

  /**
   * 최초 컬럼 생성 시에만 적용되는 초기 정렬
   * - 용도: sort와 같지만, 최초 컬럼 생성 시에만 적용됨
   * - 예시: 'asc', 'desc'
   * - 기본값: undefined
   */
  initialSort?: 'asc' | 'desc';

  /**
   * 다중 정렬 시 정렬 우선순위
   * - 용도: 여러 컬럼이 동시에 정렬될 경우 우선 순위 지정
   * - 동적으로도 변경 가능 (예: API로 재정렬)
   */
  sortIndex?: number | null;

  /**
   * 최초 컬럼 생성 시에만 적용되는 정렬 우선순위
   * - 용도: 초기 렌더링 시 한 번만 적용되는 정렬 순서
   * - 이후에는 `sortIndex`를 사용해야 함
   */
  initialSortIndex?: number;

  /**
   * 정렬 순서 커스터마이징
   * - 용도: 클릭 시 정렬 순서 지정
   * - 예시: ['asc', 'desc', null]
   * - 기본값: undefined
   */
  sortingOrder?: ('asc' | 'desc' | null)[];

  /**
   * 커스텀 정렬 함수
   * - 용도: 기본 정렬 방식 대신 사용자 정의 정렬 로직 사용
   * - 예시: (valueA, valueB, nodeA, nodeB, isDescending) => number
   * - 기본값: undefined
   */
  comparator?: (valueA: any, valueB: any, nodeA: any, nodeB: any, isDescending: boolean) => number;

  /**
   * 정렬되지 않은 상태 아이콘 표시 여부
   * - 용도: 정렬이 적용되지 않은 경우에도 아이콘 표시
   * - 예시: true, false
   * - 기본값: false
   */
  unSortIcon?: boolean;
}

// [ 셀 병합 관련 옵션 ]
export interface SpanningOptions {
  /**
   * 셀이 여러 컬럼을 병합해서 보여야 할 경우 병합될 칸 수를 반환
   * - 용도: 셀 컬럼 병합
   * - 예시: (params) => 2
   */
  colSpan?: (params: AgGridTypes.ColSpanParams) => number;

  /**
   * 행 병합 여부 또는 병합 조건 함수
   * - 용도: 같은 값의 셀 병합
   * - 예시: true 또는 (params) => boolean
   */
  spanRows?: boolean | ((params: AgGridTypes.RowSpanParams) => boolean);
}

// [ 툴팁 관련 옵션 ]
export interface TooltipOptions {
  /**
   * 셀에 툴팁으로 보여줄 데이터의 필드명
   * - tooltipValueGetter보다 우선 적용됨
   * - 예: 'name'
   */
  tooltipField?: string;

  /**
   * 툴팁으로 표시할 값을 반환하는 함수
   * - tooltipField가 없을 때 사용됨
   * - 예: (params) => `가격: ${params.value}원`
   */
  tooltipValueGetter?: (params: any) => string;

  /**
   * 사용자 정의 툴팁 컴포넌트
   * - 예: MyCustomTooltip
   */
  tooltipComponent?: string | { new (): AgGridTypes.ITooltipComp };

  /**
   * 사용자 정의 툴팁 컴포넌트에 전달할 파라미터
   * - 예: { color: 'red' }
   */
  tooltipComponentParams?: any;
}

// [ 컬럼 너비 관련 옵션 ]
export interface WidthOptions {
  /**
   * 초기 컬럼 너비 (px)
   */
  width?: number;

  /**
   * 컬럼 생성 시 초기 너비, 이후 업데이트에는 적용되지 않음
   */
  initialWidth?: number;

  /**
   * 최소 너비 (px)
   */
  minWidth?: number;

  /**
   * 최대 너비 (px)
   */
  maxWidth?: number;

  /**
   * 남은 공간 비례 분배 설정 (CSS flex-grow와 유사)
   */
  flex?: number;

  /**
   * 초기 flex 설정, 이후 업데이트에는 적용되지 않음
   */
  initialFlex?: number;

  /**
   * 컬럼 리사이징 가능 여부
   */
  resizable?: boolean;

  /**
   * 사이즈 자동 맞춤(sizeToFit) 대상에서 제외할지 여부
   */
  suppressSizeToFit?: boolean;

  /**
   * 자동 너비 조정(autoSize)에서 제외할지 여부
   */
  suppressAutoSize?: boolean;
}

// [ 그룹 컬럼 관련 옵션 ]
export interface GroupOptions extends HeaderGroupOptions {
  /**
   * 자식 컬럼 또는 그룹 목록 (필수)
   */
  children: (BaseOptions | GroupOptions)[];

  /**
   * 그룹 고유 ID
   */
  groupId?: string;

  /**
   * 그룹 내 자식 컬럼들을 항상 함께 움직이도록 고정
   * - 용도: 드래그 시 그룹 안의 컬럼들이 함께 이동
   */
  marryChildren?: boolean;

  /**
   * 기본적으로 그룹 열기 여부
   */
  openByDefault?: boolean;

  /**
   * 그룹이 열려있을 때만/닫혀있을 때만 보일 컬럼 지정
   */
  columnGroupShow?: 'open' | 'closed';

  /**
   * Columns Tool Panel에 표시 안함
   */
  suppressColumnsToolPanel?: boolean;

  /**
   * Filters Tool Panel에 표시 안함
   */
  suppressFiltersToolPanel?: boolean;

  /**
   * 컬럼 그룹에 연관된 임의의 데이터 (컨텍스트)
   */
  context?: any;
}

export interface HeaderGroupOptions {
  /**
   * 헤더에 보여질 이름
   */
  headerName?: string;

  /**
   * 헤더 셀에 적용할 CSS 클래스
   */
  headerClass?: string | string[] | ((params: AgGridTypes.HeaderClassParams) => string | string[]);

  /**
   * 헤더 툴팁 텍스트
   */
  headerTooltip?: string;

  /**
   * 헤더 셀 높이를 자동으로 계산할지 여부
   */
  autoHeaderHeight?: boolean;

  /**
   * 사용자 정의 헤더 그룹 컴포넌트
   */
  headerGroupComponent?: any;

  /**
   * 헤더 그룹 컴포넌트에 전달할 파라미터
   */
  headerGroupComponentParams?: any;

  /**
   * 헤더 셀 높이 전체를 span 하지 않도록 설정
   */
  suppressSpanHeaderHeight?: boolean;

  /**
   * 스크롤 시 헤더 라벨 고정 해제
   */
  suppressStickyLabel?: boolean;
}
