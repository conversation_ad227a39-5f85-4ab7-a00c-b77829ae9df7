export const useYnOptions = [
  { label: '사용', value: 'Y' },
  { label: '미사용', value: 'N' },
];

export const requiredYnOptions = [
  { label: '필요', value: 'Y' },
  { label: '불필요', value: 'N' },
];

export const pageTypeOptions = [
  { label: '목록', value: 'L' },
  { label: '상세', value: 'P' },
  { label: '등록', value: 'R' },
  { label: '수정', value: 'M' },
  { label: '콘텐츠', value: 'C' },
  { label: '링크', value: 'N' },
  { label: '게시판', value: 'B' },
  { label: '커스텀', value: 'T' },
];

export const pageSizeOptions = [
  { label: '10', value: 10 },
  { label: '20', value: 20 },
  { label: '30', value: 30 },
  { label: '40', value: 40 },
  { label: '50', value: 50 },
];

export const fileAcceptList = [
  // 📸 이미지
  'jpg',
  'jpeg',
  'png',
  // 'gif',
  // 'webp',
  // 'bmp',
  // 'svg',
  // 'tif',
  // 'tiff',

  // // 🎞️ 비디오
  // 'mp4',
  // 'mov',
  // 'webm',
  // 'avi',
  // 'mkv',
  // 'wmv',

  // // 🔊 오디오
  // 'mp3',
  // 'wav',
  // 'ogg',
  // 'm4a',
  // 'aac',
  // 'flac',

  // // 📄 문서
  // 'pdf',
  // 'doc',
  // 'docx',
  // 'xls',
  // 'xlsx',
  // 'ppt',
  // 'pptx',
  // 'txt',
  // 'csv',
  // 'rtf',
  // 'md',
  // 'odt',

  // // 🧱 압축 파일
  // 'zip',
  // 'rar',
  // '7z',
  // 'tar',
  // 'gz',
  // 'bz2',
];
