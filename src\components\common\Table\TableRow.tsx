import React, { cloneElement, isValidElement, TableHTMLAttributes } from 'react';
import joinClassName from '@utils/joinClassName';
import TableCell from './TableCell';

interface TableRowProps extends TableHTMLAttributes<HTMLTableRowElement> {
  cellData: Array<{ text: string | number | React.ReactNode; width?: string }> | Array<string | number | React.ReactNode>;
  tag?: 'td' | 'th';
}

const TableRow: React.FC<TableRowProps> = ({ cellData = [], tag = 'td', className, ...attributes }) => (
  <tr className={joinClassName('c_table_row', className)} {...attributes}>
    {cellData.map((item, idx) => {
      return (
        <TableCell key={idx} tag={tag} width={item.width}>
          {item.text || item}
        </TableCell>
      );
    })}
  </tr>
);

export default TableRow;
