import ErrorMsg from '@components/common/Error/ErrorMsg';
import Checkbox from '@components/common/Input/Checkbox';
import { Controller, useFormContext, RegisterOptions } from 'react-hook-form';

// 체크박스 옵션의 타입 정의
export interface Option {
  value: number; // 체크박스의 값
  label: string; // 체크박스에 표시될 레이블
}

// FormCheckboxList 컴포넌트의 props 타입 정의
interface FormCheckboxListProps {
  name: string; // form 필드 이름
  rules?: RegisterOptions; // react-hook-form의 유효성 검사 규칙
  options: Option[]; // 체크박스 옵션 배열
  hasLabel?: boolean; // 레이블 표시 여부
  // checkedValues: any[];                          // 현재 체크된 값들의 배열
  // setCheckedValues: (values: any[]) => void;     // 체크된 값들을 업데이트하는 함수
}

// 체크박스 리스트 폼 컴포넌트
const FormCheckboxList = ({
  name,
  rules,
  options,
  hasLabel = true,
  // checkedValues,
  // setCheckedValues,
}: FormCheckboxListProps) => {
  // react-hook-form의 컨텍스트에서 필요한 메서드들을 가져옴
  const { control, watch, setValue } = useFormContext();

  const watchValues = watch(name);
  // 체크박스 변경 핸들러
  const handleChange = (value: number) => {
    const newValues = watchValues.includes(value)
      ? watchValues.filter((v) => v !== value) // 이미 체크된 값이면 제거
      : [...watchValues, value]; // 체크되지 않은 값이면 추가

    // 폼 값을 업데이트
    setValue(name, newValues);
  };

  // 체크박스 변경 핸들러
  // const handleChange = (value: number) => {
  //   const newValues = checkedValues.includes(value)
  //     ? checkedValues.filter((v) => v !== value) // 이미 체크된 값이면 제거
  //     : [...checkedValues, value];               // 체크되지 않은 값이면 추가
  //   setCheckedValues(newValues);
  // };

  // // 체크된 값이 변경될 때마다 form 값 업데이트
  // useEffect(() => {
  //   setValue(name, checkedValues);
  // }, [checkedValues, name, setValue]);

  return (
    <Controller
      name={name}
      rules={rules}
      control={control}
      render={({ field, fieldState }) => (
        <div className="c_checkbox_list_wrapper">
          {/* 옵션 배열을 순회하며 체크박스 컴포넌트 렌더링 */}
          {options.map((option) => (
            <Checkbox
              key={option.value}
              {...field}
              label={option.label}
              value={option.value}
              checked={watchValues.includes(option.value)}
              onChange={() => handleChange(option.value)}
              hasLabel={hasLabel}
            />
          ))}
          {/* 유효성 검사 에러 메시지 표시 */}
          <ErrorMsg text={fieldState.error?.message} />
        </div>
      )}
    />
  );
};

export default FormCheckboxList;
