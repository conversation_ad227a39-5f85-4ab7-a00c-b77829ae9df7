import IconButton from '@components/common/Button/IconButton';
import { useCurrentEditor } from '@tiptap/react';
import React, { useState } from 'react';
import DefaultModal from '@components/common/Modal/DefaultModal';
import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import { useForm } from 'react-hook-form';
import { useEditorAPI } from '@components/common/TextEditor/hooks/useEditorAPI';

interface ModalState {
  isOpen: boolean;
  onConfirm?: () => void;
  onCancel?: () => void;
}

const EditorImage = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;
  const methods = useForm({
    defaultValues: {
      url: '',
    },
  });

  const { uploadFile } = useEditorAPI();

  const [tab, setTab] = useState<'file' | 'url'>('file');
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    onConfirm: () => {},
    onCancel: () => {
      setModalState((prev) => ({ ...prev, isOpen: false }));
    },
  });

  const openModal = () => {
    setModalState((prev) => ({ ...prev, isOpen: true }));
  };

  const closeModal = () => {
    setModalState((prev) => ({ ...prev, isOpen: false }));
  };

  const handleSubmitImage = (data: { url: string }) => {
    editor.commands.setImage({ src: data.url, alt: '이미지', title: '이미지' });
    closeModal();
  };

  const handleInsertImage = (type: 'file' | 'url') => {
    if (type === 'file') {
      const calcMaxSize = 5 * 1024 * 1024; // 5MB
      const fileInput = document.createElement('input');
      fileInput.setAttribute('type', 'file');
      fileInput.setAttribute('accept', 'image/*');
      fileInput.setAttribute('max-size', calcMaxSize.toString()); // 5MB 제한
      fileInput.click();

      fileInput.onchange = async (event: Event) => {
        const files = fileInput.files;
        if (!files) return;

        try {
          const response = await uploadFile(files);
          const id = response.id;

          editor
            .chain()
            .focus()
            .setImage({
              src: '/api/files/' + id,
              alt: files[0].name,
              title: files[0].name,
            })
            .run();
        } catch (error) {
          console.error('이미지 업로드 실패:', error);
        }
      };
    } else {
      const url = prompt('이미지 URL을 입력하세요');
      if (!url) return;
      editor.commands.setImage({ src: url, alt: '이미지', title: '이미지' });
    }

    closeModal();
  };

  return (
    <>
      <IconButton
        text="image"
        icon="image"
        iconOnly
        fill="unfilled"
        color="grayscale"
        size='smallest'
        onClick={openModal}
        disabled={editor.isActive('codeBlock')}
      />
      <DefaultModal
        title="이미지 업로드"
        className="image_modal"
        isOpenModal={modalState.isOpen}
        setIsOpenModal={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
        onClickedDim={() => setModalState((prev) => ({ ...prev, isOpen: false }))}
      >
        <>
          <div className="tab_list">
            <Button
              text="파일 업로드"
              onClick={() => setTab('file')}
              fill="unfilled"
              color={tab === 'file' ? 'primary' : 'grayscale'}
            />
            <Button
              text="링크 임베드"
              onClick={() => setTab('url')}
              fill="unfilled"
              color={tab === 'url' ? 'primary' : 'grayscale'}
            />
          </div>
          <div className="tab_panel">
            {tab === 'file' && (
              <>
                <p className="file_upload_text">
                  이미지 파일을 업로드해주세요. <br />
                  최대 5MB까지 업로드 가능합니다. br
                </p>
                <div className="button_wrapper">
                  <Button text="취소" color="grayscale" onClick={closeModal} />
                  <Button text="파일 선택" onClick={() => handleInsertImage('file')} />
                </div>
              </>
            )}
            {tab === 'url' && (
              <Form methods={methods} onSubmit={handleSubmitImage}>
                <FormInput
                  type="url"
                  name="url"
                  placeholder="https://ex.com/img.jpg 형식으로 입력해주세요"
                  rules={{
                    required: 'URL을 입력해주세요.',
                    pattern: { value: /^(https?:\/\/)?[^\s/$.?#].[^\s]*$/, message: '유효한 URL을 입력해주세요.' },
                  }}
                />
                <div className="button_wrapper">
                  <Button text="취소" color="grayscale" onClick={closeModal} />
                  <Button text="URL 업로드" type="submit" />
                </div>
              </Form>
            )}
          </div>
        </>
      </DefaultModal>
    </>
  );
};

export default EditorImage;
