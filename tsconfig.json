{
  "compilerOptions": {
    "target": "ESNext", // 생성되는 JS의 버전 설정
    "module": "ESNext", // 변환되는 JS의 모듈 시스템 설정
    "allowSyntheticDefaultImports": true, //기본 내보내기(export)가 없는 모듈에서 기본 가져오기를 사용하도록 설정합니다.
    "esModuleInterop": true, // typeScript 컴파일러가 파일 및 모듈 이름의 대소문자 일관성을 강제적으로 검사
    "strict": false, // true일 경우 엄격한 타입 검사, ts에서 매개변수는 프로그래머가 직접 지정하도록 권장한다
    "skipLibCheck": true, // 타입 정의 파일(.d.ts)의 타입 검사 생략
    "noEmit": true, // JavaScript 출력파일을 만들지않고 단순히 타입 검사만을 수행
    "jsx": "react-jsx", // TypeScript가 JSX 코드를 React 컴포넌트로 해석하고 타입 검사를 적용
    "allowImportingTsExtensions": true, //TypeScript 파일의 확장자 .ts 및 .tsx를 명시적으로 사용해야 모듈을 가져올 수 있다.
    "isolatedModules": true, // 각 파일을 독립적인 모듈로 취급하고, 파일 간의 상호 의존성을 검사하지 않도록 한다.
    "moduleResolution": "bundler",
    "types": ["vite/client"],
    //** IDE에서 밑줄로 표시 및 빌드 자체 불가.
    "noUnusedLocals": false, // 사용되지 않는 지역 변수를 확인하고 발견되면 오류
    "noUnusedParameters": false, // 함수 선언에서 사용되지 않는 매개변수를 확인하고 발견되면 오류
    "noFallthroughCasesInSwitch": false, //스위치 문(break 또는 return 문이 없는 경우)에서 fallthrough 사례를 확인하고 발견되면 오류
    //**
    /* 경로설정 */
    "baseUrl": "./src",
    "paths": {
      "@api/*": ["api/*"],
      "@assets/*": ["assets/*"],
      "@components/*": ["components/*"],
      "@hooks/*": ["hooks/*"],
      "@page/*": ["page/*"],
      "@styles/*": ["assets/styles/*"],
      "@utils/*": ["utils/*"],
      "@type/*": ["types/*"],
      "@constants/*": ["constants/*"],
      "@store/*": ["store/*"],
      "@route/*": ["route/*"]
    },
    "useDefineForClassFields": true // 클래스 필드를 정의할 때 constructor 내부에서 초기화하는 대신, 클래스 바디에서 직접 초기화할 수 있습니다.
  },
  // "include": [
  //   "src"
  // ],
  "exclude": ["node_modules", "**/*.test.ts"],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}
