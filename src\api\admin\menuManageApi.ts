import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';
import { Menu } from 'types/adminMenuType';

// [관리자] 관리자 메뉴 관리

/**
 * 관리자 메뉴 목록 조회
 */
export const getAdminMenus = async ({ isGetErrorMsg = false }: { isGetErrorMsg?: boolean }) => {
  try {
    const apiUrl = `/api-admin/admin/menus`;
    const response = await api.get(apiUrl);

    return response.data.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 목록 조회');
  }
};

/**
 * 본인 권한 메뉴 목록 조회
 */

export const getMyMenus = async ({ isGetErrorMsg = false }: { isGetErrorMsg?: boolean }) => {
  try {
    const apiUrl = `/api-admin/my/my-menus-new`;
    const response = await api.get(apiUrl);

    return response.data.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '본인 권한 메뉴 목록 조회');
  }
};

/**
 * 관리자 메뉴 상세 조회
 */

interface GetMenuDetailProps {
  categoryId: number;
  isGetErrorMsg?: boolean;
}

export const getMenuDetail = async ({ categoryId, isGetErrorMsg = false }: GetMenuDetailProps) => {
  try {
    const apiUrl = `/api-admin/admin/menus/${categoryId}`;
    const response = await api.get(apiUrl);

    return response.data.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 상세 조회');
  }
};

/**
 * 관리자 메뉴 상세정보 수정
 */

interface EditMenuDetailsParams {
  menuId: number;
  detailData: {
    name: string;
    url: string;
    desc: string;
    icon: string;
    useYn: string;
    delAbleYn: string;
  };
  isGetErrorMsg?: boolean;
}

export const editMenuDetails = async ({ menuId, detailData, isGetErrorMsg = false }: EditMenuDetailsParams) => {
  try {
    const apiUrl = `/api-admin/admin/menus/${menuId}`;
    const response = await api.post(apiUrl, detailData);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 상세정보 수정');
  }
};

/**
 * 관리자 메뉴 삭제
 */

interface DeleteMenuParams {
  ids: number[];
  isGetErrorMsg?: boolean;
}

export const deleteMenu = async ({ ids, isGetErrorMsg = false }: DeleteMenuParams) => {
  try {
    const response = await api.post(`/api-admin/admin/menus/delete`, { ids });
    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 삭제');
  }
};

/**
 * 관리자 하위 메뉴 수정
 */

interface SaveChildMenuParams {
  categoryId: number;
  saveData: { name: string; url: string }[];
  isGetErrorMsg?: boolean;
}

export const saveChildMenu = async ({ categoryId, saveData, isGetErrorMsg = false }: SaveChildMenuParams) => {
  try {
    const apiUrl = `/api-admin/admin/menus/${categoryId}/child-menus`;
    const response = await api.post(apiUrl, saveData);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 하위 메뉴 수정');
  }
};

/**
 *
 * 관리자 메뉴 등록
 */

export const addMenu = async ({ isGetErrorMsg = false }: { isGetErrorMsg?: boolean }) => {
  try {
    const response = await api.post('/api-admin/admin/menus');

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 등록');
  }
};

/**
 *
 * 관리자 메뉴 순서 변경
 */

interface ChangeMenuSortParams {
  menus: Menu[];
  isGetErrorMsg?: boolean;
}

export const changeMenuSort = async ({ menus, isGetErrorMsg = false }: ChangeMenuSortParams) => {
  try {
    const response = await api.post('/api-admin/admin/menus/change-sort', menus);

    return response.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '관리자 메뉴 순서 변경');
  }
};

/**
 *
 * 페이지 옵션 리스트
 */

const isGetErrorMsg = false;
export const getPageOptions = async () => {
  try {
    const response = await api.get(`/api-admin/admin/menus/pages`);
    return response.data.data;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '메뉴관리 페이지옵션 불러오기');
  }
};
