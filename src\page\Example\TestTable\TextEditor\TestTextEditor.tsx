import React, { useEffect, useRef, useState } from 'react';
import TestTable from '../TestTable';
import TextEditor from '@components/common/TextEditor/TextEditor';
import Button from '@components/common/Button/Button';

const TestTextEditor = () => {
  const [content, setContent] = useState('');
  const [viewContent, setViewContent] = useState('');

  const handleSave = () => {
    setViewContent(content);
  };

  return (
    <>
      <TestTable
        compName="TextEditor"
        headChild={
          <>
            <tr>
              <th colSpan={3}>기본 에디터</th>
            </tr>
          </>
        }
        bodyChild={
          <>
            <tr>
              <td>
                <TextEditor content={content} onChange={setContent} />
                <div className="button_wrapper flex justify-center gap-2 mt-5">
                  <Button text="저장" onClick={handleSave} />
                </div>
              </td>
              <td>
                <h4>
                  입력된 HTML: <b>(TextEditorViewer 사용)</b>
                </h4>
                {viewContent && <TextEditor content={viewContent} readOnly />}
              </td>
              <td>
                <div className="w-full max-w-[400px]">
                  <h4>입력된 값:</h4>
                  <pre
                    style={{
                      width: '100%',
                      whiteSpace: 'pre-wrap',
                      overflow: 'auto',
                      margin: '10px auto',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {content}
                  </pre>
                </div>
              </td>
            </tr>
          </>
        }
      />
      <TestTable
        compName='TextEditorReadOnly'
        headChild={
          <>
            <tr>
              <th colSpan={3}>읽기 전용 에디터</th>
            </tr>
          </>
        }
        bodyChild={
          <>
            <tr>
              <td>
                <TextEditor
                  content={
                    '<h2>안녕하세요</h2><p><br></p><blockquote>인용구입니다.ㅋ<span style="background-color: rgb(0, 138, 0);">ㅋㅋㅋㅋㅋ</span>ㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋ<span style="color: rgb(255, 153, 0);">ㅋㅋㅋㅋㅋㅋㅋㅋ</span>ㅋ</blockquote><p><br></p><div class="ql-code-block-container" spellcheck="false"><div class="ql-code-block">코드블럭입니다.</div><div class="ql-code-block"><br></div><div class="ql-code-block">&lt;React /&gt; // 에러안남</div></div>'
                  }
                />
              </td>
              <td>
                <h4>
                  입력된 HTML: <b>(TextEditorViewer 사용)</b>
                </h4>
                <TextEditor
                  readOnly
                  content={
                    '<h2>안녕하세요</h2><p><br></p><blockquote>인용구입니다.ㅋ<span style="background-color: rgb(0, 138, 0);">ㅋㅋㅋㅋㅋ</span>ㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋ<span style="color: rgb(255, 153, 0);">ㅋㅋㅋㅋㅋㅋㅋㅋ</span>ㅋ</blockquote><p><br></p><div class="ql-code-block-container" spellcheck="false"><div class="ql-code-block">코드블럭입니다.</div><div class="ql-code-block"><br></div><div class="ql-code-block">&lt;React /&gt; // 에러안남</div></div>'
                  }
                />
              </td>
              <td>
                <div className="w-full">
                  <h4>입력된 값:</h4>
                  {
                    '<h2>안녕하세요</h2><p><br></p><blockquote>인용구입니다.ㅋ<span style="background-color: rgb(0, 138, 0);">ㅋㅋㅋㅋㅋ</span>ㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋㅋ<span style="color: rgb(255, 153, 0);">ㅋㅋㅋㅋㅋㅋㅋㅋ</span>ㅋ</blockquote><p><br></p><div class="ql-code-block-container" spellcheck="false"><div class="ql-code-block">코드블럭입니다.</div><div class="ql-code-block"><br></div><div class="ql-code-block">&lt;React /&gt; // 에러안남</div></div>'
                  }
                </div>
              </td>
            </tr>
          </>
        }
        />
    </>
  );
};

export default TestTextEditor;
