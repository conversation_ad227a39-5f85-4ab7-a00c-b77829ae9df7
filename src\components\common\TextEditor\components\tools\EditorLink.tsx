import Button from '@components/common/Button/Button';
import IconButton from '@components/common/Button/IconButton';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import DefaultModal from '@components/common/Modal/DefaultModal';
import { useCurrentEditor } from '@tiptap/react';
import { useCallback, useState } from 'react';
import { useForm } from 'react-hook-form';

const EditorLink = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const [isOpenModal, setIsOpenModal] = useState(false);
  const methods = useForm({
    defaultValues: {
      text: '',
      url: '',
    },
  });

  const openModal = () => {
    setIsOpenModal(true);
  };

  const closeModal = () => {
    setIsOpenModal(false);
    methods.reset();
  };

  const setLink = useCallback(
    (data) => {
      const { url, text } = data;

      if (editor.isActive('link')) {
        editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
      } else if (editor.state.selection.empty) {
        editor.chain().focus().setLink({ href: url }).run();
        editor.chain().focus().insertContent(text || url).run();
        editor.chain().focus().enter().run();
      } else {
        editor.chain().focus().setLink({ href: url }).run();
      }

      closeModal();
    },
    [editor]
  );

  const unsetLink = () => {
    editor.chain().focus().extendMarkRange('link').unsetLink().run();
  };

  return (
    <>
      <IconButton
        text="link"
        icon="link"
        iconOnly
        fill={editor.isActive('link') ? 'filled' : 'unfilled'}
        color={editor.isActive('link') ? 'primary' : 'grayscale'}
        size='smallest'
        onClick={editor.isActive('link') ? unsetLink : openModal}
      />
      <DefaultModal
        title="하이퍼링크 추가"
        isOpenModal={isOpenModal}
        className="link_modal"
        setIsOpenModal={setIsOpenModal}
        onClickedDim={closeModal}
      >
        <Form methods={methods} onSubmit={setLink}>
          <FormInput name="text" placeholder="텍스트를 입력하세요." />
          <FormInput
            type="url"
            name="url"
            placeholder="텍스트에 적용할 URL을 입력해주세요."
            rules={{ required: '텍스트에 적용할 URL을 입력해주세요.' }}
          />

          <div className="button_wrapper">
            <Button text="취소" color="grayscale" onClick={() => setIsOpenModal(false)} />
            <Button text="링크 추가" type="submit" />
          </div>
        </Form>
      </DefaultModal>
    </>
  );
};

export default EditorLink;
