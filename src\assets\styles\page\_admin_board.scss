@use '@styles/utils/mixin' as m;

.admin_board {
  // 게시판 관리
  &_contents {
    &_control {
      @include m.flex(center, space-between, row);
      margin-bottom: 0.5rem;

      &_cnt {
        @include m.flex(center, center, row);
        gap: 1rem;
      }

      &_btn {
        @include m.flex(center, center, row);
        gap: 0.25rem;
      }
    }
  }

  // 게시판 등록
  &_add {
    .c_form {
      .admin_board_add_form {
        .c_table {
          &_body {
            tr {
              td {
                .row_content {
                  div {
                    @include m.flex(center);
                    gap: 0.5rem;
                  }
                }
              }
            }
          }
        }
      }
    }

    &_btn {
      @include m.flex(center, space-between, row);
      margin-top: 1rem;

      .btn_right {
        @include m.flex(center, center, row);
        gap: 0.25rem;
      }
    }
  }
}
