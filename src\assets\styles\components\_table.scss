@use '@styles/utils/mixin' as m;

.c_table_container_header {
  @include m.flex(center, space-between);
  margin-bottom: 0.5rem;
}

.c_table_wrapper {
  border-radius: 0.25rem;
  border: 1px solid var(--g_02);
  box-shadow: 0px 0px 10px 0 rgba(black, 0.1);
  // overflow: hidden;

  .c_table {
    table-layout: fixed;
    position: relative;
    width: 100%;

    th {
      background-color: var(--table_header_bg);
      border-bottom: 1px dotted var(--g_03);
      &:not(:last-child) {
        border-right: 1px dotted var(--g_03);
      }
    }
    td {
      background-color: var(--table_cell_bg);
      border-bottom: 1px dotted var(--g_03);
      &:not(:last-child) {
        border-right: 1px dotted var(--g_03);
      }
    }

    thead {
      &.sticky {
        position: sticky;
        top: 0;
        left: 0;
      }
      tr {
        transition: all 0.3s;

        &:first-child {
          > :first-child {
            border-top-left-radius: 0.25rem;
          }
          > :last-child {
            border-top-right-radius: 0.25rem;
          }
        }

        th {
          &:not(:last-child) {
            border-right: 1px solid var(--g_03);
          }
        }
      }
    }

    tbody {
      tr {
        &:first-child {
          > :first-child {
            border-top-left-radius: 8px;
          }
          > :last-child {
            border-top-right-radius: 8px;
          }
        }

        th {
          min-width: 250px;
          width: 300px;

          @include m.bp_large() {
            width: 275px;
          }

          @include m.bp_medium() {
            width: 250px;
          }

          .desc {
            display: block;
            font-size: 0.875rem;

            @include m.bp_medium() {
              font-size: 0.75rem;
              line-height: 0.875rem;
            }
          }
        }

        &.active {
          td {
            background-color: var(--g_03);
          }
        }

        td {
          width: 100%;
          // TableBodyRow.tsx
          .row_content {
            @include m.flex(center);
            gap: 0.5rem;

            min-height: 32px;

            @include m.bp_medium() {
              min-height: 28px;
            }

            .full {
              width: 100%;
            }

            // 셀렉트박스
            .c_select_wrapper {
              min-width: 250px;

              .c_select {
                width: 100%;

                .c_selected_label {
                  width: 100%;
                }
              }
            }

            // input
            .c_input_wrapper {
              min-width: 250px;

              input {
                width: 100%;

                &.error {
                  border: 1px solid var(--error);
                }
              }

              input:focus {
                outline: none;
              }
            }

            // checkbox
            .c_checkbox_list_wrapper {
              .c_checkbox {
                &:not(:first-child) {
                  margin-left: 0.5rem;
                }
              }
            }

            // calendar
            .c_calendar_wrapper {
              min-width: 250px;

              button {
                width: 100%;
              }
            }

            // filebox
            .c_filebox {
              .c_filebox_image_list {
                .c_filebox_image_list_add_box {
                  .desc {
                    position: relative;
                    line-height: normal;
                  }
                }
              }
            }

            .c_error_msg {
              text-align: left;
            }

            .desc {
              padding-left: 0.5rem;
              font-size: 0.875rem;
              color: var(--g_06);
              min-height: 32px;

              @include m.bp_medium() {
                min-height: 28px;
              }
            }

            *:not(.desc) + .desc {
              position: absolute;
              line-height: 32px;

              @include m.bp_medium() {
                line-height: 28px;
              }
            }
          }

          .row_half {
            .c_select_wrapper,
            .c_input_wrapper,
            .c_calendar_wrapper {
              min-width: auto;
              width: 100%;
            }
          }
        }

        &:last-child {
          border: 0;

          > :first-child {
            border-bottom-left-radius: 0.25rem;
          }
          > :last-child {
            border-bottom-right-radius: 0.25rem;
          }
        }
      }
    }

    th,
    td {
      padding: 0.625rem;
      transition: all 0.3s;
      > span {
        display: block;
        margin: 0 auto;
        font-size: 1rem;
        line-height: 1.15rem;
        @include m.ellipsis();
      }

      @include m.bp_large() {
        font-size: 0.875rem;
        line-height: 1rem;
      }

      @include m.bp_medium() {
        font-size: 0.75rem;
        line-height: 0.875rem;
      }
    }
  }
}

.c_list_table_wrapper {
  border-radius: 0.25rem;
  border: 1px solid var(--g_02);
  box-shadow: 0px 0px 10px 0 rgba(black, 0.1);
  width: 100%;

  font-size: 1rem;
  line-height: 1.15rem;
  @include m.ellipsis();

  @include m.bp_large() {
    font-size: 0.875rem;
    line-height: 1rem;
  }

  @include m.bp_medium() {
    font-size: 0.75rem;
    line-height: 0.875rem;
  }

  .c_list_table_title {
    background-color: var(--table_header_bg);
    border-bottom: 1px solid var(--g_03);
    font-weight: bold;
    text-align: center;
    border-radius: 0.25rem 0.25rem 0 0;
    padding: 0.625rem;
  }

  .c_list_table_contents {
    > * {
      @include m.flex(center, start);

      cursor: pointer;
      transition: all 0.3s;
      padding: 0.625rem;
      min-height: 2.75rem;
      background-color: var(--table_cell_bg);
      border-bottom: 1px dotted var(--g_03);

      &:last-child {
        border: 0;
        border-radius: 0 0 0.25rem 0.25rem;
      }

      &.active {
        background-color: var(--g_03);
      }

      &.no_result {
        display: flex;
        align-items: center !important;
        justify-content: center !important;

        cursor: auto;
        height: 100%;
        color: var(--g_06);
      }
    }
  }
}
