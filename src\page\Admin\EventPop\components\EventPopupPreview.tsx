import Button from '@components/common/Button/Button';
import { ResponseFile } from '@components/common/Filebox/Filebox';
import Checkbox from '@components/common/Input/Checkbox';
import React, { useEffect } from 'react';
import { useAlertStore } from '@store/useAlertStore';
import { FieldValues, useFormContext } from 'react-hook-form';
import { UseFormReturn } from 'react-hook-form';
import { Navigation } from 'swiper/modules';
import { Autoplay } from 'swiper/modules';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

interface EventPopupPreviewProps {
  title: string;
  subTitle: string;
  linkUrl: string;
  notOpenDay: { label: string; value: number };
  responseImageList: ResponseFile[];
  imageFiles: FileList;
}

const EventPopupPreview = ({ title, subTitle, linkUrl, notOpenDay, responseImageList, imageFiles }: EventPopupPreviewProps) => {
  const { activeAlert } = useAlertStore();

  const parseFiles = () => {
    return imageFiles ? Array.from(imageFiles) : [];
  };

  const handleClickImage = () => {
    if (linkUrl) {
      activeAlert(`클릭시 "${linkUrl}" 로 이동합니다`);
    } else {
      activeAlert('링크가 없습니다.');
    }
  };


  return (
    <div className="event_popup_preview">
      <div className="c_modal_body">
        <Swiper
          slidesPerView={1}
          spaceBetween={30}
          pagination={{
            clickable: true,
          }}
          centeredSlides={true}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
          }}
          navigation={true}
          modules={[Pagination, Autoplay, Navigation]}
          className="mySwiper"
        >
          <div className="desc">
            <h3>{title || '타이틀이 없습니다.'}</h3>
            <p>{subTitle || '서브 타이틀이 없습니다.'}</p>
          </div>
          {responseImageList &&
            responseImageList.length > 0 &&
            responseImageList.map((file) => (
              <SwiperSlide key={file.id}>
                <figure className="slide_img" onClick={handleClickImage}>
                  <img src={`/api/files/${file.id}`} alt={file.name} />
                </figure>
              </SwiperSlide>
            ))}
          {parseFiles().map((file) => (
            <SwiperSlide key={file.name}>
              <figure className="slide_img" onClick={handleClickImage}>
                <img src={URL.createObjectURL(file)} alt={file.name} />
              </figure>
            </SwiperSlide>
          ))}

          {!responseImageList && parseFiles().length === 0 && (
            <SwiperSlide>
              <div className="no_image">현재 이미지가 없습니다.</div>
            </SwiperSlide>
          )}
        </Swiper>
      </div>
      <div className="c_modal_footer">
        <Checkbox label={`오늘 부터 ${notOpenDay?.value} 일간 다시 보지 않기`} value={''} hasLabel />
        <Button text="닫기" color="grayscale" />
      </div>
    </div>
  );
};

export default EventPopupPreview;
