import joinClassName from '@utils/joinClassName';
import { useEffect, useState } from 'react';
import { useLocation, useMatches } from 'react-router-dom';
import { useMenuStore } from '@store/useMenuStore';

interface TitleProps {
  className?: string;
}

const Title = ({ className }: TitleProps) => {
  const matches = useMatches();
  const location = useLocation();
  const { myMenuList } = useMenuStore();
  const [breadCrumb, setBreadCrumb] = useState<string[]>([]);

  const findBreadCrumb = () => {
    let result = [];

    const pathname = location.pathname.replace('/' + matches[matches.length - 1].params.id, '');

    myMenuList.forEach((myMenu) => {
      const { url, childMenus, childPages } = myMenu;

      // 1depth 체크
      if (pathname.includes(url)) {
        result.push(myMenu.name);
      }

      // 2depth 체크
      if (childMenus && childMenus.length > 0) {
        childMenus.forEach((childMenu) => {
          // 2depth의 url 과 현재경로가 일치할 경우
          if (pathname.includes(childMenu.url)) {
            result.push(childMenu.name);
          }

          // 2depth의 url 과 현재경로가 일치할 경우
          if (pathname.includes(childMenu.url) && childMenu.childPages && childMenu.childPages.length > 0) {
            childMenu.childPages.forEach((childPage) => {
              const { pageTypeCode, pageName } = childPage;
              let pageUrl = '';
              if (pageTypeCode === 'R') pageUrl = `${childMenu.url}/add`;
              if (pageTypeCode === 'M') pageUrl = `${childMenu.url}/edit`;
              if (pageTypeCode === 'P') pageUrl = `${childMenu.url}/detail`;
              if (pageTypeCode === 'T') pageUrl = `${childMenu.url}/test`;

              if (pathname.includes(pageUrl)) {
                result.push(pageName);
              }
            });
          }
        });
      }

      if (pathname.includes(url) && childPages && childPages.length > 0) {
        childPages.forEach((childPage) => {
          const { pageTypeCode, pageName } = childPage;
          let pageUrl = '';
          if (pageTypeCode === 'R') pageUrl = `${url}/add`;
          if (pageTypeCode === 'M') pageUrl = `${url}/edit`;
          if (pageTypeCode === 'P') pageUrl = `${url}/detail`;

          if (pathname.includes(pageUrl)) {
            result.push(pageName);
          }
        });
      }
    });

    setBreadCrumb(result);
  };

  useEffect(() => {
    if (myMenuList.length === 0) return;
    findBreadCrumb();
  }, [matches]);

  return (
    <h1 className={joinClassName('c_title', className)}>
      <p className="c_title_text">{breadCrumb[breadCrumb.length - 1]}</p>
      <p className="c_bread_crumb">
        <span>HOME</span>
        {breadCrumb.map((crumb, index) => (
          <span key={index}>{crumb}</span>
        ))}
      </p>
    </h1>
  );
};

export default Title;
