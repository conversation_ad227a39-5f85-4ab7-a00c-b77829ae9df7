import ErrorMsg from '@components/common/Error/ErrorMsg';
import joinClassName from '@utils/joinClassName';
import { forwardRef, InputHTMLAttributes } from 'react';

interface RadioboxProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  value: string | number;
  hasLabel?: boolean;
  className?: string;
  error?: string;
}
// label option 추가
const Radiobox = forwardRef<HTMLInputElement, RadioboxProps>(
  (
    {
      label,
      value,
      name,
      hasLabel = false,
      className, // 추가 클래스 요소
      error,
      ...attributes
    },
    ref
  ) => {
    const radioboxStyleClass = joinClassName('c_radio', className);

    return (
      <>
        <label className={radioboxStyleClass} >
          <input type="radio" value={value} name={name} {...attributes} />
          <span className={hasLabel ? '' : 'sr-only'}>{label}</span>
        </label>
        <ErrorMsg text={error} />
      </>
    );
  }
);

export default Radiobox;
