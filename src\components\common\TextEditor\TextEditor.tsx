import { <PERSON><PERSON><PERSON><PERSON>, Editor } from '@tiptap/react';
import Heading, { Level } from '@tiptap/extension-heading';
import StarterKit from '@tiptap/starter-kit';
import Highlight from '@tiptap/extension-highlight';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { common, createLowlight } from 'lowlight';
import Text from '@tiptap/extension-text';
import TextStyle from '@tiptap/extension-text-style';
import { FontFamily } from '@tiptap/extension-font-family';
import Color from '@tiptap/extension-color';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Link from '@tiptap/extension-link';
import Table from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';
import TaskList from '@tiptap/extension-task-list';
import TaskItem from '@tiptap/extension-task-item';
import joinClassName from '@utils/joinClassName';
import Placeholder from '@tiptap/extension-placeholder';
import { Caption, Figure } from '@components/common/TextEditor/utils/CustomNode';
import { CustomFontSize } from '@components/common/TextEditor/utils/FontSize';
import { YoutubeResize } from '@components/common/TextEditor/utils/YoutubeResize';
import { Video } from '@components/common/TextEditor/utils/Video';
import { ImageResize } from '@components/common/TextEditor/utils/ImageResize';
import { Indent } from '@components/common/TextEditor/utils/Indent';
import Toolbar from '@components/common/TextEditor/components/Toolbar';
import TableBubble from '@components/common/TextEditor/components/TableBubble';
import { useContext, useEffect } from 'react';
import { EditorContext } from '@tiptap/react';

interface EditorProps {
  content: string;
  onChange?: (html: string) => void;
  readOnly?: boolean;
  placeholder?: string;
  className?: string;
  name?: string;
}

const EditorContentUpdater = ({ content }: { content?: string }) => {
  const { editor } = useContext(EditorContext);

  useEffect(() => {
    if (editor && content && editor.getHTML() !== content) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  return null;
};

const TextEditor = ({
  content,
  onChange,
  readOnly,
  placeholder = '내용을 입력해주세요.',
  className,
  name,
}: EditorProps) => {
  const lowlight = createLowlight(common);

  const handleToggleHeading = (editor: Editor, level: Level) => {
    if (!editor) return;

    // 상태 점검 및 초기화
    if (!editor.isActive('heading', { level })) {
      editor.chain().focus().unsetAllMarks().run();
    }

    editor.chain().focus().toggleHeading({ level }).run();
    return true;
  };

  const CustomHeading = Heading.extend({
    addCommands() {
      return {
        // 헤딩 토글 시 모든 마크 초기화
        toggleHeading:
          (levelObj) =>
          ({ commands }) => {
            if (!this.editor.isActive('heading', levelObj)) {
              this.editor.chain().focus().unsetAllMarks().run();
            }
            return commands.toggleNode('heading', 'paragraph', levelObj);
          },
      };
    },

    addKeyboardShortcuts() {
      return {
        // 헤딩 토글 시 모든 마크 초기화
        'Mod-1': () => handleToggleHeading(this.editor, 1),
        'Mod-2': () => handleToggleHeading(this.editor, 2),
        'Mod-3': () => handleToggleHeading(this.editor, 3),
        'Mod-4': () => handleToggleHeading(this.editor, 4),
        'Mod-5': () => handleToggleHeading(this.editor, 5),
        'Mod-6': () => handleToggleHeading(this.editor, 6),
      };
    },
  });

  const extensions = [
    StarterKit.configure({
      heading: false,
      text: false,
      codeBlock: false,
    }),
    Placeholder.configure({
      placeholder,
    }),
    Figure,
    Caption,
    CustomHeading,
    Text,
    TextStyle.configure({ mergeNestedSpanStyles: true }),
    Highlight.configure({ multicolor: true }),
    Color.configure({ types: ['textStyle'] }),
    Underline,
    TaskList,
    TaskItem.configure({
      HTMLAttributes: {
        class: 'c_checkbox',
      },
    }),
    TextAlign.configure({
      types: ['heading', 'paragraph'],
    }),
    CodeBlockLowlight.configure({
      lowlight,
      defaultLanguage: 'javascript',
    }),
    FontFamily,
    CustomFontSize.configure({
      types: ['textStyle'],
    }),
    YoutubeResize.configure({
      inline: true,
      controls: true,
      nocookie: true,
      allowFullscreen: true,
      autoplay: true,
      enableIFrameApi: true,
      interfaceLanguage: document.documentElement.lang,
    }),
    Video.configure({
      HTMLAttributes: {
        class: 'video-wrapper',
      },
    }),
    Link,
    ImageResize,
    Table.configure({
      resizable: true,
    }),
    TableRow,
    TableHeader,
    TableCell,
    Indent,
  ];

  return (
    <div className={joinClassName('c_editor', readOnly ? 'readonly' : 'editable', className)}>
      <EditorProvider
        slotBefore={!readOnly && <Toolbar />}
        extensions={extensions}
        content={content}
        onUpdate={({ editor }) => {
          try {
            if (onChange) {
              onChange(editor.getHTML());
            }
          } catch (error) {
            console.error('Editor update error:', error);
          }
        }}
        editorContainerProps={{ id: 'tiptap', name: name } as React.HTMLAttributes<HTMLDivElement>}
        editable={!readOnly}
      >
        <TableBubble />
        <EditorContentUpdater content={content} />
      </EditorProvider>
    </div>
  );
};

export default TextEditor;
