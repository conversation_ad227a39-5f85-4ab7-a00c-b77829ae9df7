import Button from '@components/common/Button/Button';
import Form from '@components/common/Form/Form';
import FormCheckboxList from '@components/common/Form/FormCheckboxList';
import FormInput from '@components/common/Form/FormInput';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useAlertStore } from 'store/useAlertStore';
import { useConfirmStore } from 'store/useConfirmStore';
import { AuthData } from '@page/Admin/Role/type';
import {
  getAccountDetail,
  getCheckDuplicateId,
  addAccount,
  editAccount,
  getRoleList,
  postUnlockAccount,
  deleteAccount,
  deleteAccountHard,
  restoreAccount,
} from '@api/admin/accountDetailAPI';
import { useAdminAccountDetailStore } from '@page/Admin/Account/store/useAdminAccountDetailStore';
import { DetailFormValues, pageType, Role } from '@page/Admin/Account/type';
import joinClassName from '@utils/joinClassName';
import AdminAccountPasswordModal from '@page/Admin/Account/components/AdminAccountPasswordModal';
import TableBodyRow from '@components/common/Table/TableBodyRow';

const AdminAccountDetailPage = () => {
  // hooks
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();

  const { roleListData, setRoleListData, checkDuplicateId, setCheckDuplicateId, detailFormValues } =
    useAdminAccountDetailStore();
  const [isOpenModal, setIsOpenModal] = useState(false);
  const { activeAlert } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  // add or edit > add: 등록페이지, edit: 수정페이지 >
  // '/admin/account/add' or '/admin/account/edit/:id'
  const [hasAdminIdError, setHasAdminIdError] = useState<boolean>(false);
  const [hasNameError, setHasNameError] = useState<boolean>(false);
  const [hasPasswordError, setHasPasswordError] = useState<boolean>(false);
  const [hasPasswordChkError, setHasPasswordChkError] = useState<boolean>(false);

  const initPageType = () => {
    if (location.pathname.includes('add')) return 'add';
    if (location.pathname.includes('edit')) return 'edit';
    if (location.pathname.includes('detail')) return 'detail';
  };

  const [pageType, setPageType] = useState<pageType>(initPageType());

  // form
  const methods = useForm<DetailFormValues>({
    defaultValues: detailFormValues,
  });

  const { reset } = methods;

  const setRoleList = async () => {
    const roleList = await getRoleList({});

    const parseData = roleList.map(({ name, id }: AuthData): { label: string; value: number } => ({
      label: name,
      value: id,
    }));
    setRoleListData(parseData);
  };

  const setAccountDetail = async () => {
    const id = params.id;
    const accountDetailData = await getAccountDetail({ id });

    const parseCheckRoles =
      accountDetailData.roles
        .map((role: Role) => role.id) // role.id만 추출
        .filter((id: number | string) => id !== null) || // role.id 가 null 인 경우를 필터링
      []; // roles가 falsy한 값일 경우 빈배열

    reset({
      adminId: accountDetailData.adminId,
      name: accountDetailData.name,
      password: '',
      passwordConfirm: '',
      delYn: accountDetailData.delYn,
      activeYn: accountDetailData.activeYn,
      roleIds: parseCheckRoles,
      loginFailureCnt: accountDetailData.loginFailureCnt,
    });
  };

  useEffect(() => {
    setRoleList();
    // 수정일 때는 데이터를 가져오고, 추가일 때는 초기화

    if (pageType === 'add') initValue();
    if (pageType === 'edit' || pageType === 'detail') setAccountDetail();
  }, []);

  // Function
  const handleOnSubmit = (data: DetailFormValues) => {
    if (pageType === 'add') {
      if (checkDuplicateId) {
        activeAlert('ID 중복 확인을 해주세요.');
        return;
      }
      setConfirmState({
        isOpen: true,
        title: '알림',
        content: '등록하시겠습니까?',
        onCancel: initConfirmState,
        onConfirm: async () => {
          const responseMessage = await addAccount({ data });
          if (responseMessage) {
            initConfirmState();
            activeAlert(responseMessage, () => navigate('/admin/account'));
          }
        },
      });
    } else if (pageType === 'edit') {
      setConfirmState({
        isOpen: true,
        title: '알림',
        content: '수정하시겠습니까?',
        onCancel: initConfirmState,
        onConfirm: async () => {
          const responseMessage = await editAccount({ id: params.id, data });
          if (responseMessage) {
            initConfirmState();
            setPageType('detail');
            activeAlert(responseMessage);
          }
        },
      });
    }
  };

  const initForm = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: (
        <>
          기존에 작성하셨던 내용이 초기화됩니다. <br />
          초기화하시겠습니까?
        </>
      ),
      onCancel: initConfirmState,
      onConfirm: () => {
        pageType === 'add' ? initValue() : setAccountDetail();
        initConfirmState();
      },
    });
  };

  const initValue = () => {
    // input 필드값 초기화
    reset(detailFormValues);
    setCheckDuplicateId(false);
  };

  const handleUnlockAccount = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '계정 잠금 해제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: async () => {
        const responseMessage = await postUnlockAccount({ id: params.id });
        if (responseMessage) {
          initConfirmState();
          activeAlert(responseMessage);
        }
      },
    });
  };

  const handleDeleteDetail = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '삭제하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: async () => {
        const responseMessage = await deleteAccount({ id: params.id });
        if (responseMessage) {
          initConfirmState();
          setAccountDetail();
          activeAlert(responseMessage);
        }
      },
    });
  };

  const handleHardDeleteAccount = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: (
        <>
          영구 삭제한 계정은 복구할 수 없습니다. <br />
          정말 삭제 하시겠습니까?
        </>
      ),
      onCancel: initConfirmState,
      onConfirm: async () => {
        const responseMessage = await deleteAccountHard({ id: params.id });
        if (responseMessage) {
          initConfirmState();
          activeAlert(responseMessage, () => navigate('/admin/account'));
        } else {
          initConfirmState();
        }
      },
    });
  };

  const handleRestoreAccount = () => {
    setConfirmState({
      isOpen: true,
      title: '알림',
      content: '계정을 복구 하시겠습니까?',
      onCancel: initConfirmState,
      onConfirm: async () => {
        const responseMessage = await restoreAccount({ id: params.id });
        if (responseMessage) {
          setAccountDetail();
          initConfirmState();
          activeAlert(responseMessage);
        }
      },
    });
  };

  const handleCheckDuplicateId = async () => {
    const adminId = methods.getValues('adminId');
    if (!adminId) {
      activeAlert('ID는 필수값입니다.');
      return;
    }

    const isDuplicate = await getCheckDuplicateId({ adminId });
    setCheckDuplicateId(isDuplicate);
    if (isDuplicate) {
      activeAlert('중복된 ID 입니다. 다른 ID를 입력해주세요.');
    } else {
      activeAlert('사용가능한 ID 입니다.');
    }
  };

  const handleGetErrorState = async () => {
    await methods.trigger();

    const errorFields = methods.formState.errors;

    errorFields?.password ? setHasPasswordError(true) : setHasPasswordError(false);
    errorFields?.passwordConfirm ? setHasPasswordChkError(true) : setHasPasswordChkError(false);
    errorFields?.adminId ? setHasAdminIdError(true) : setHasAdminIdError(false);
    errorFields?.name ? setHasNameError(true) : setHasNameError(false);
  };

  useEffect(() => {
    const password = methods.getValues('password');
    const passwordCheck = methods.getValues('passwordConfirm');
    const adminId = methods.getValues('adminId');
    const name = methods.getValues('name');

    if (password || passwordCheck || adminId || name) {
      handleGetErrorState();
    }
  }, [methods.watch('password'), methods.watch('passwordConfirm'), methods.watch('adminId'), methods.watch('name')]);

  return (
    <div className="manage_admin_account">
      <Form methods={methods} onSubmit={handleOnSubmit}>
        <TableContainer className="admin_detail_account">
          <TableBody>
            <TableBodyRow
              rowData={{
                title: '관리자 ID',
                required: true,
                contents: (
                  <>
                    {pageType === 'add' ? (
                      <FormInput
                        className={joinClassName(hasAdminIdError && 'error')}
                        name="adminId"
                        placeholder="ID를 입력해주세요"
                        onInput={() => {
                          setCheckDuplicateId(false);
                        }}
                        rules={{
                          required: 'ID는 필수값입니다.',
                        }}
                      />
                    ) : (
                      <span>{methods.getValues('adminId')}</span>
                    )}
                    {pageType === 'add' && (
                      <Button className="check_duplicated" text="중복확인" onClick={handleCheckDuplicateId} />
                    )}
                  </>
                ),
              }}
            />
            <TableBodyRow
              rowData={{
                title: '이름',
                required: true,
                contents: (
                  <>
                    {pageType === 'detail' ? (
                      <span>{methods.getValues('name')}</span>
                    ) : (
                      <FormInput
                        className={joinClassName(hasNameError && 'error')}
                        name="name"
                        placeholder="이름을 입력해주세요"
                        rules={{
                          required: '이름은 필수값입니다.',
                        }}
                      />
                    )}
                  </>
                ),
              }}
            />
            {pageType === 'add' && (
              <>
                <TableBodyRow
                  rowData={{
                    title: '비밀번호',
                    contents: (
                      <div className="password_input">
                        <FormInput
                          className={joinClassName(hasPasswordError && 'error')}
                          name="password"
                          type="password"
                          autoComplete="new-password"
                          placeholder="비밀번호를 입력해주세요"
                          rules={{
                            minLength: {
                              value: 8,
                              message: '최소 8자 이상 입력해주세요.',
                            },
                            validate: (value) => {
                              if (!/^(?=.*[a-zA-Z])(?=.*[!@#$%^*+=-])(?=.*[0-9]).{8,20}$/.test(value)) {
                                return '영문, 숫자, 특수문자를 포함하여 8~20자로 입력해주세요.';
                              }
                            },
                          }}
                        />
                        <span className="desc">
                          비밀번호는 영문, 숫자, 특수문자를 포함하여 8글자 이상 ~20자 이하 길이로 등록해 주세요.
                        </span>
                      </div>
                    ),
                  }}
                />
                <TableBodyRow
                  rowData={{
                    title: '비밀번호 확인',
                    contents: (
                      <div className="password_confirm">
                        <FormInput
                          className={joinClassName(hasPasswordChkError && 'error')}
                          name="passwordConfirm"
                          type="password"
                          autoComplete="new-password"
                          placeholder="비밀번호를 입력해주세요"
                          rules={{
                            minLength: {
                              value: 8,
                              message: '최소 8자 이상 입력해주세요.',
                            },
                            validate: (value) => {
                              if (!/^(?=.*[a-zA-Z])(?=.*[!@#$%^*+=-])(?=.*[0-9]).{8,20}$/.test(value)) {
                                return '영문, 숫자, 특수문자를 포함하여 8~20자로 입력해주세요.';
                              }
                              if (value !== methods.getValues('password')) {
                                return '비밀번호가 일치하지 않습니다.';
                              }
                            },
                          }}
                        />
                        <span className="desc">동일한 패스워드를 한번 더 입력하세요.</span>
                      </div>
                    ),
                  }}
                />
              </>
            )}
            {pageType !== 'add' && (
              <TableBodyRow
                rowData={{
                  title: (
                    <>
                      로그인 실패횟수
                    </>
                  ),
                  contents: <span>{methods.getValues('loginFailureCnt') || 0}</span>,
                }}
              />
            )}
            {methods.getValues('delYn') === 'Y' && (
              <TableBodyRow
                rowData={{
                  title: '계정 삭제 여부',

                  contents: <span>삭제됨</span>,
                }}
              />
            )}
            <TableBodyRow
              rowData={{
                title: '권한',
                contents: (
                  <>
                    {roleListData && pageType !== 'detail' && (
                      <FormCheckboxList
                        name="roleIds"
                        rules={{
                          required: '권한을 선택해주세요',
                        }}
                        options={roleListData}
                      />
                    )}
                    {pageType === 'detail' && (
                      <span>
                        {methods
                          .getValues('roleIds')
                          ?.map((roleId) => roleListData.find((role) => role.value === roleId)?.label)
                          .join(', ')}
                      </span>
                    )}
                  </>
                ),
              }}
            />
          </TableBody>
        </TableContainer>
        <div className="button_wrapper">
          <div className="left">
            <Button
              text={pageType === 'detail' ? '목록' : '취소'}
              onClick={() => {
                if (pageType === 'edit') {
                  navigate(`/admin/account/detail/${params.id}`);
                  setPageType('detail');
                } else {
                  navigate('/admin/account');
                }
              }}
            />
          </div>
          <div className="right">
            {pageType === 'detail' ? (
              <>
                {methods.getValues('delYn') === 'N' ? (
                  <>
                    <Button text="삭제" color="red" onClick={handleDeleteDetail} />
                    {methods.getValues('loginFailureCnt') >= 5 && (
                      <Button text="계정 잠금 해제" color="grayscale" onClick={handleUnlockAccount} />
                    )}
                    <Button text="비밀번호 변경" color="grayscale" onClick={() => setIsOpenModal(true)} />
                    <Button
                      text="수정"
                      onClick={(e) => {
                        e.preventDefault();
                        navigate(`/admin/account/edit/${params.id}`);
                        setPageType('edit');
                      }}
                    />
                  </>
                ) : (
                  <>
                    <Button text="계정 복구" color="grayscale" onClick={handleRestoreAccount} />
                    <Button text="영구 삭제" color="red" onClick={handleHardDeleteAccount} />
                  </>
                )}
              </>
            ) : (
              <>
                <Button text="초기화" color="grayscale" onClick={initForm} />
                <Button type="submit" text={pageType === 'add' ? '등록' : '저장'} />
              </>
            )}
          </div>
        </div>
      </Form>
      <AdminAccountPasswordModal isOpenModal={isOpenModal} setIsOpenModal={setIsOpenModal} id={params.id} />
    </div>
  );
};

export default AdminAccountDetailPage;
