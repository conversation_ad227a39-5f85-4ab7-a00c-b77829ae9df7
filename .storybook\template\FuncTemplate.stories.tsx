/**
 * Template 함수 방식 (CSF 2)
 *  Form, Input, Hook 등 내부에서 상태 변화가 필요한 컴포넌트에 적합
 */

import type { Meta, StoryObj } from '@storybook/react';

// @ts-expect-error: 템플릿용 임시 컴포넌트
import { YourComponent, YourComponentProps } from './YourComponent';

// 문서 참고용 타입 임포트
import type { CustomStoryMeta } from './storybook-meta-config.types';

/**
 * Meta 설정입니다. 자세한 옵션 설명은 {@link CustomStoryMeta} 참고
 */
const meta: Meta<typeof YourComponent> = {
  title: 'Components/YourComponent',
  component: YourComponent,
  parameters: {
    layout: 'centered',
  },
  args: {},
};

export default meta;

type Story = StoryObj<typeof meta>;

const Template = (args: YourComponentProps) => {
  // const gridRef = useRef(null);
  // return <YourComponent {...args} ref={gridRef} />;
};

export const Options: Story = {
  args: {},
};
