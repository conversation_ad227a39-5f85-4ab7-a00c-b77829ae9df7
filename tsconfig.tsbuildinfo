{"root": ["./vite.config.ts", "./src/app.tsx", "./src/main.tsx", "./src/api/api.ts", "./src/api/admin/accountdetailapi.ts", "./src/api/admin/accountlistapi.ts", "./src/api/admin/adminusersapi.ts", "./src/api/admin/boardapi.ts", "./src/api/admin/dynamicboardapi.ts", "./src/api/admin/eventpopupapi.ts", "./src/api/admin/fileapi.ts", "./src/api/admin/logmanageapi.ts", "./src/api/admin/menumanageapi.ts", "./src/api/admin/pageapi.ts", "./src/api/admin/postapi.ts", "./src/api/admin/roleapi.ts", "./src/api/admin/serviceapi.ts", "./src/api/admin/servicegroupapi.ts", "./src/api/admin/systemcodeapi.ts", "./src/api/admin/vocapi.ts", "./src/api/common/useloginapi.ts", "./src/api/user/pageapi.ts", "./src/components/common/chartloading.tsx", "./src/components/common/loading.tsx", "./src/components/common/pagination.tsx", "./src/components/common/button/button.stories.tsx", "./src/components/common/button/button.tsx", "./src/components/common/button/iconbutton.tsx", "./src/components/common/button/types.ts", "./src/components/common/calendar/calendar.tsx", "./src/components/common/calendar/calendarrange.tsx", "./src/components/common/calendar/calendarweeklypicker.tsx", "./src/components/common/chip/actionchip.tsx", "./src/components/common/chip/choicechip.tsx", "./src/components/common/chip/filterchip.tsx", "./src/components/common/chip/inputchip.tsx", "./src/components/common/chip/types.ts", "./src/components/common/controlbox/controlbox.tsx", "./src/components/common/error/errormsg.tsx", "./src/components/common/filebox/filebox.tsx", "./src/components/common/filebox/components/fileimagepreview.tsx", "./src/components/common/filebox/components/filemulti.tsx", "./src/components/common/filebox/components/filemultiimage.tsx", "./src/components/common/filebox/components/filesingle.tsx", "./src/components/common/filebox/store/usefilepreviewer.ts", "./src/components/common/form/form.tsx", "./src/components/common/form/formcheckbox.tsx", "./src/components/common/form/formcheckboxlist.tsx", "./src/components/common/form/forminput.tsx", "./src/components/common/form/formselectbox.tsx", "./src/components/common/form/formtextarea.tsx", "./src/components/common/grid/datagrid.tsx", "./src/components/common/grid/grid.tsx", "./src/components/common/grid/gridloading.tsx", "./src/components/common/grid/gridtooltip.tsx", "./src/components/common/grid/switchcelleitor.tsx", "./src/components/common/grid/stories/column.stories.tsx", "./src/components/common/grid/stories/_griddecorator.tsx", "./src/components/common/grid/type/ag-column-options-types.ts", "./src/components/common/grid/type/ag-grid-options-types.ts", "./src/components/common/grid/type/ag-row-options-types.ts", "./src/components/common/input/checkbox.tsx", "./src/components/common/input/checkboxlist.tsx", "./src/components/common/input/input.tsx", "./src/components/common/input/labelinput.tsx", "./src/components/common/input/radiobox.tsx", "./src/components/common/input/radioboxlist.tsx", "./src/components/common/input/searchbar.tsx", "./src/components/common/input/textarea.tsx", "./src/components/common/input/types.ts", "./src/components/common/log/components/types.ts", "./src/components/common/log/hooks/uselogger.ts", "./src/components/common/log/utils/createtraceid.ts", "./src/components/common/log/utils/getfrontinfo.ts", "./src/components/common/modal/alert.tsx", "./src/components/common/modal/confirm.tsx", "./src/components/common/modal/defaultmodal.tsx", "./src/components/common/modal/eventmodal.tsx", "./src/components/common/modal/modalportal.tsx", "./src/components/common/navigation/navcontainer.tsx", "./src/components/common/navigation/navitem.tsx", "./src/components/common/selectbox/selectbox.tsx", "./src/components/common/selectbox/selectcheckbox.tsx", "./src/components/common/table/listtable.tsx", "./src/components/common/table/tablebody.tsx", "./src/components/common/table/tablebodyrow.tsx", "./src/components/common/table/tablecell.tsx", "./src/components/common/table/tablecontainer.tsx", "./src/components/common/table/tablecontainerheader.tsx", "./src/components/common/table/tableheader.tsx", "./src/components/common/table/tablerow.tsx", "./src/components/common/tabs/tab.tsx", "./src/components/common/tabs/tablist.tsx", "./src/components/common/tabs/tabpanel.tsx", "./src/components/common/tabs/tabscontainer.tsx", "./src/components/common/tabs/index.ts", "./src/components/common/tabs/store/createtabstore.ts", "./src/components/common/texteditor/texteditor.tsx", "./src/components/common/texteditor/components/tablebubble.tsx", "./src/components/common/texteditor/components/toolbar.tsx", "./src/components/common/texteditor/components/tools/colorpallette.tsx", "./src/components/common/texteditor/components/tools/editoralign.tsx", "./src/components/common/texteditor/components/tools/editorblock.tsx", "./src/components/common/texteditor/components/tools/editorimage.tsx", "./src/components/common/texteditor/components/tools/editorindent.tsx", "./src/components/common/texteditor/components/tools/editorlink.tsx", "./src/components/common/texteditor/components/tools/editorlist.tsx", "./src/components/common/texteditor/components/tools/editortable.tsx", "./src/components/common/texteditor/components/tools/editorvideo.tsx", "./src/components/common/texteditor/components/tools/embeds.tsx", "./src/components/common/texteditor/components/tools/fontcolor.tsx", "./src/components/common/texteditor/components/tools/fontfamily.tsx", "./src/components/common/texteditor/components/tools/fontsize.tsx", "./src/components/common/texteditor/components/tools/fontstyle.tsx", "./src/components/common/texteditor/components/tools/heading.tsx", "./src/components/common/texteditor/hooks/useeditorapi.tsx", "./src/components/common/texteditor/utils/customnode.ts", "./src/components/common/texteditor/utils/fontsize.ts", "./src/components/common/texteditor/utils/imageresize.ts", "./src/components/common/texteditor/utils/indent.ts", "./src/components/common/texteditor/utils/video.ts", "./src/components/common/texteditor/utils/youtuberesize.ts", "./src/components/common/texteditor/utils/codeblock.ts", "./src/components/common/texteditor/utils/constant.ts", "./src/components/common/title/boardtitle.tsx", "./src/components/common/title/subtitle.tsx", "./src/components/common/title/title.tsx", "./src/components/common/tooltip/tooltip.stories.tsx", "./src/components/common/tooltip/tooltip.tsx", "./src/components/common/tooltip/types.ts", "./src/components/layout/footer.tsx", "./src/components/layout/header.tsx", "./src/components/layout/main.tsx", "./src/components/layout/navigation/gnb.tsx", "./src/components/layout/navigation/lnb.tsx", "./src/constants/options.ts", "./src/hooks/usealert.ts", "./src/hooks/useclicklog.ts", "./src/hooks/useconfirm.ts", "./src/hooks/usedebounce.ts", "./src/hooks/useeventbus.ts", "./src/hooks/useinfinitescroll.ts", "./src/hooks/usekakaoloader.tsx", "./src/hooks/usemenuapi.ts", "./src/page/error.tsx", "./src/page/admin/account/adminaccountdetail.tsx", "./src/page/admin/account/adminaccountlist.tsx", "./src/page/admin/account/type.ts", "./src/page/admin/account/components/adminaccountpasswordmodal.tsx", "./src/page/admin/account/store/useadminaccountdetailstore.ts", "./src/page/admin/account/store/useadminaccountliststore.ts", "./src/page/admin/board/adminboarddetail.tsx", "./src/page/admin/board/adminboardlist.tsx", "./src/page/admin/board/adminboardupsert.tsx", "./src/page/admin/board/type.ts", "./src/page/admin/eventpop/eventpopupdetail.tsx", "./src/page/admin/eventpop/eventpopuplist.tsx", "./src/page/admin/eventpop/eventtest.tsx", "./src/page/admin/eventpop/components/eventpopuppreview.tsx", "./src/page/admin/menu/adminmenupage.tsx", "./src/page/admin/menu/components/categorylist.tsx", "./src/page/admin/menu/components/menudetail.tsx", "./src/page/admin/page/type.ts", "./src/page/admin/page/page/adminpagecreatepage.tsx", "./src/page/admin/page/page/adminpagedetailpage.tsx", "./src/page/admin/page/page/adminpagelistpage.tsx", "./src/page/admin/page/page/adminpageupdatepage.tsx", "./src/page/admin/page/page/components/pagedetail.tsx", "./src/page/admin/page/page/components/pageupsert.tsx", "./src/page/admin/page/page/components/subpagelist.tsx", "./src/page/admin/page/page/components/subpageupsertmodal.tsx", "./src/page/admin/post/adminpostdetail.tsx", "./src/page/admin/post/adminpostlist.tsx", "./src/page/admin/post/type.ts", "./src/page/admin/post/components/adminposteditor.tsx", "./src/page/admin/post/components/attachment.tsx", "./src/page/admin/role/adminrolepage.tsx", "./src/page/admin/role/type.ts", "./src/page/admin/role/components/authdetail.tsx", "./src/page/admin/role/components/authlist.tsx", "./src/page/admin/role/components/menuauthlist.tsx", "./src/page/admin/role/components/pageauthlist.tsx", "./src/page/admin/role/components/serviceauthlist.tsx", "./src/page/admin/role/hooks/userole.tsx", "./src/page/admin/role/store/useauthstore.ts", "./src/page/admin/role/utils/checkedrows.ts", "./src/page/admin/service/adminservicepage.tsx", "./src/page/admin/service/type.ts", "./src/page/admin/service/components/customfilter.tsx", "./src/page/admin/service/components/servicegroupdetail.tsx", "./src/page/admin/service/components/servicegrouplist.tsx", "./src/page/admin/service/components/servicegroupmodal.tsx", "./src/page/admin/service/components/servicelist.tsx", "./src/page/admin/service/components/servicemodal.tsx", "./src/page/admin/service/hooks/useservice.tsx", "./src/page/admin/service/store/useservicestore.ts", "./src/page/dynamicboard/general/generalboarddetail.tsx", "./src/page/dynamicboard/general/generalboardlist.tsx", "./src/page/dynamicboard/general/generalboardupsert.tsx", "./src/page/dynamicboard/general/type.ts", "./src/page/example/testfilechunkupload.tsx", "./src/page/example/texteditor.tsx", "./src/page/example/toss/testtoss.tsx", "./src/page/example/toss/testtosscheckout.tsx", "./src/page/example/toss/testtossfailure.tsx", "./src/page/example/toss/testtosssuccess.tsx", "./src/page/login/login.tsx", "./src/page/login/logout.tsx", "./src/page/login/qrlogin.tsx", "./src/page/myinfo/myinfo.tsx", "./src/page/myinfo/store/usemyinfostore.ts", "./src/page/system/apilog/apilog.tsx", "./src/page/system/apilog/type.ts", "./src/page/system/code/manageadmincode.tsx", "./src/page/system/code/type.ts", "./src/page/system/code/components/codelist.tsx", "./src/page/system/code/components/groupcodedetail.tsx", "./src/page/system/code/components/groupcodelist.tsx", "./src/page/system/code/components/types.ts", "./src/page/system/code/store/usecodestore.ts", "./src/page/system/file/managefilepage.tsx", "./src/page/system/frontlog/frontlog.tsx", "./src/page/system/frontlog/type.ts", "./src/page/system/loginlog/loginlog.tsx", "./src/page/system/loginlog/type.ts", "./src/page/system/setting/managesettingpage.tsx", "./src/page/system/setting/types.ts", "./src/page/system/setting/components/settinglist.tsx", "./src/page/system/setting/components/settingtypes.tsx", "./src/page/system/setting/hooks/usesettingapi.ts", "./src/page/system/setting/store/usesettingstore.ts", "./src/page/testpage/testcomponents.tsx", "./src/page/testpage/testlayout.tsx", "./src/page/testpage/testtable/testtable.tsx", "./src/page/testpage/testtable/button/testbuttoncompwrapper.tsx", "./src/page/testpage/testtable/button/testfilledbuttoncomp.tsx", "./src/page/testpage/testtable/button/testoutlinedbuttoncomp.tsx", "./src/page/testpage/testtable/button/testunfilledbuttoncomp.tsx", "./src/page/testpage/testtable/checkradio/testcheckradio.tsx", "./src/page/testpage/testtable/chip/testchips.tsx", "./src/page/testpage/testtable/chip/types.ts", "./src/page/testpage/testtable/filebox/testfilebox.tsx", "./src/page/testpage/testtable/input/testinputcompwrapper.tsx", "./src/page/testpage/testtable/input/testsearchbarfield.tsx", "./src/page/testpage/testtable/input/testtextareafield.tsx", "./src/page/testpage/testtable/input/testtextfield.tsx", "./src/page/testpage/testtable/modal/testmodal.tsx", "./src/page/testpage/testtable/pagination/testpagination.tsx", "./src/page/testpage/testtable/selectbox/testselectbox.tsx", "./src/page/testpage/testtable/texteditor/testtexteditor.tsx", "./src/page/user/account/useraccountdetailpage.tsx", "./src/page/user/account/useraccountlistpage.tsx", "./src/page/user/account/useraccountwrap.tsx", "./src/page/user/account/type.ts", "./src/page/user/account/hooks/useuseraccountdetailapi.ts", "./src/page/user/account/hooks/useuseraccountlistapi.ts", "./src/page/user/account/store/useuseraccountdetailstore.ts", "./src/page/user/account/store/useuseraccountliststore.ts", "./src/page/user/menu/usermenupage.tsx", "./src/page/user/menu/components/categorylist.tsx", "./src/page/user/menu/components/menudetail.tsx", "./src/page/user/menu/hooks/useusermenuapi.ts", "./src/page/user/menu/store/useusermenustore.ts", "./src/page/user/page/userpagedetailpage.tsx", "./src/page/user/page/userpagelayout.tsx", "./src/page/user/page/userpagelistpage.tsx", "./src/page/user/page/type.ts", "./src/page/user/page/components/pagedetail.tsx", "./src/page/user/page/components/pagelist.tsx", "./src/page/user/page/components/subpagesformmodal.tsx", "./src/page/user/page/components/subpageslist.tsx", "./src/page/user/page/components/types.ts", "./src/page/user/page/store/usecodestore.ts", "./src/page/user/role/userrolepage.tsx", "./src/page/user/role/type.ts", "./src/page/user/role/components/authdetail.tsx", "./src/page/user/role/components/authlist.tsx", "./src/page/user/role/components/menuauthlist.tsx", "./src/page/user/role/components/serviceauthlist.tsx", "./src/page/user/role/hooks/useuserroleapi.ts", "./src/page/user/role/store/useauthstore.ts", "./src/page/user/service/userservicepage.tsx", "./src/page/user/service/type.ts", "./src/page/user/service/components/servicegroupdetail.tsx", "./src/page/user/service/components/servicegrouplist.tsx", "./src/page/user/service/components/servicegroupmodal.tsx", "./src/page/user/service/components/servicelist.tsx", "./src/page/user/service/components/servicemodal.tsx", "./src/page/user/service/hooks/useuserserviceapi.ts", "./src/page/user/service/hooks/useuserservicegroupapi.ts", "./src/page/user/service/store/useservicestore.ts", "./src/page/voc/vocdetail.tsx", "./src/page/voc/voclist.tsx", "./src/page/voc/components/vocaddmodal.tsx", "./src/page/voc/components/voccard.tsx", "./src/page/voc/components/vocdetailinfoitem.tsx", "./src/page/voc/hooks/usevoc.ts", "./src/page/voc/store/usevocstore.ts", "./src/route/protectedroute.tsx", "./src/route/routingdev.tsx", "./src/store/constant.ts", "./src/store/usealertstore.ts", "./src/store/useconfirmstore.ts", "./src/store/useeventstore.ts", "./src/store/uselayoutstore.ts", "./src/store/useloadingstore.ts", "./src/store/uselogstore.ts", "./src/store/useloginstore.ts", "./src/store/usemenustore.ts", "./src/store/usesavemenustore.ts", "./src/store/usesessiontimeoutstore.ts", "./src/store/usethemestore.ts", "./src/types/adminmenutype.ts", "./src/types/pagemode.ts", "./src/types/pagination.ts", "./src/utils/apierrorhandler.ts", "./src/utils/calcnosort.ts", "./src/utils/date.ts", "./src/utils/extractediconoption.ts", "./src/utils/filedownload.ts", "./src/utils/formatter.ts", "./src/utils/joinclassname.ts", "./src/utils/pagepercountno.ts", "./src/utils/querystring.ts", "./src/utils/regex.ts", "./src/utils/searchbykeywordandkey.ts", "./src/utils/showpopup.ts", "./src/utils/useragent.ts", "./src/utils/validate.ts", "./src/utils/globals/global.d.ts", "./src/utils/globals/setupglobal.ts"], "version": "5.6.3"}