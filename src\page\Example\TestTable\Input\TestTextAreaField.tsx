import React, { useState } from 'react';
import TestTable from '../TestTable';
import TextArea from '@components/common/Input/TextArea';

const TestTextAreaField = () => {
  const [textAreaVal, setTextAreaVal] = useState('');
  const handleTextAreaVal = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextAreaVal(e.target.value);
  };

  return (
    <TestTable
      compName="textarea"
      headChild={
        <>
          <tr>
            <th colSpan={3}>TextArea Type</th>
          </tr>
          <tr>
            <th>Status</th>
            <th>Text Align - Left (Default)</th>
          </tr>
        </>
      }
      bodyChild={
        <>
          <tr className="p_default">
            <th>Default</th>
            <td>
              <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} />
            </td>
          </tr>
          <tr className="p_disabled">
            <th>Disabled</th>
            <td>
              <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} disabled />
            </td>
          </tr>
          <tr className="p_readonly">
            <th>Readonly</th>
            <td>
              <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} readOnly />
            </td>
          </tr>
          <tr className="p_error">
            <th>Error</th>
            <td>
              <div className="c_textarea_wrapper error">
                <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} design="error" />
              </div>
            </td>
          </tr>
          <tr className="p_warning">
            <th>Warning</th>
            <td>
              <div className="c_textarea_wrapper warning">
                <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} design="warning" />
              </div>
            </td>
          </tr>
          <tr className="p_confirm">
            <th>confirm</th>
            <td>
              <div className="c_textarea_wrapper confirm">
                <TextArea placeholder="Placeholder" value={textAreaVal} onChange={handleTextAreaVal} design="confirm" />
              </div>
            </td>
          </tr>
        </>
      }
    />
  );
};

export default TestTextAreaField;
