import { useConfirmStore } from "store/useConfirmStore";

export const useConfirm = () => {
  const { setConfirmState } = useConfirmStore();

  const openConfirm = (content: string | React.ReactNode, onConfirm: () => void = null, confirmType: 'add' | 'edit' | 'delete' | null = null) => {
    setConfirmState({
      isOpen: true,
      content: content,
      confirmType: confirmType,
      onCancel: closeConfirm,
      onConfirm: () => {
        onConfirm && onConfirm();
        closeConfirm();
      },
    });
  };

  const closeConfirm = () => {
    setConfirmState({
      isOpen: false,
      content: '',
      onConfirm: () => {},
    });
  };

  return { openConfirm, closeConfirm };
};