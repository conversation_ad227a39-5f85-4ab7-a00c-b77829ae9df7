import api from '@api/api';
import { defaultHandleError } from '@utils/apiErrorHandler';
import type { Page, PageDTO, DeletePages, SubPageDTO } from '@page/Admin/Page/type';

const isGetErrorMsg = false;

/**
 * 페이지 목록을 조회하는 API 함수
 */
export const getPageList = async (params) => {
  const queryString = utils.buildQueryString(params);

  try {
    const response = await api.get(`/api-admin/admin/group-pages/paging${queryString}`);
    return response.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 목록 조회');
  }
};

/**
 * 페이지 상세를 조회하는 API 함수
 */
export const getPageDetail = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/admin/group-pages/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 상세 조회');
  }
};

/**
 * 게시판옵션을 조회하는 API 함수
 */
export const getTemplateOptions = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/bbs-posts/templets`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '게시판 목록 조회');
  }
};

/**
 * 페이지정보 CRUD
 */

export const createPage = async (data: PageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 생성');
  }
};

export const updatePage = async (pageId, data: PageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages/${pageId}`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '페이지 수정');
  }
};

export const deletePage = async (pageId: Page['id']) => {
  try {
    const response = await api.post(`/api-admin/admin/group-pages/${pageId}/delete`);
    return response.data.message;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '페이지 삭제');
  }
};

/**
 * 하위페이지 CRUD
 */
export const getSubPagesList = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/admin/pages/group/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 목록 조회');
  }
};

export const getSubPageDetail = async (id: Page['id']) => {
  try {
    const response = await api.get(`/api-admin/admin/pages/${id}`);
    return response.data.data;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 상세 조회');
  }
};

export const createSubPage = async (data: SubPageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/pages`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 생성');
  }
};

export const updateSubPage = async (subPageId, data: SubPageDTO) => {
  try {
    const response = await api.post(`/api-admin/admin/pages/${subPageId}`, data);
    return response.data.message;
  } catch (error) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 수정');
  }
};

//단건
export const deleteSubPage = async (pageId: Page['id']) => {
  try {
    const response = await api.post(`/api-admin/admin/pages/${pageId}/delete`);
    return response.data.message;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 삭제');
  }
};

//다건
export const deleteSubPages = async (data: DeletePages) => {
  try {
    const response = await api.post(`/api-admin/admin/pages/delete`, { ...data });
    return response.data.message;
  } catch (error: unknown) {
    defaultHandleError(error, isGetErrorMsg, '하위페이지 삭제');
  }
};
