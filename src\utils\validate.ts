import { birthPattern, emailPattern, idPattern, namePattern, onlyKoreanPattern, onlyNumPattern, passwordPattern, phonePattern, verifyNumPattern } from "./regex";

interface InputValidateProps {
  pattern?: { 
    value: string; // validate 해야하는 정규식
    msg: string; // 정규식에 맞지 않을 때 출력할 메시지
  };
  min?: {
    value: number; // 최소 길이
    msg: string; // 최소 길이 미만일 때 출력할 메시지
  };
  max?: {
    value: number; // 최대 길이
    msg: string; // 최대 길이 초과일 때 출력할 메시지
  };
  required?: { 
    value: boolean; // 필수 입력 여부
    msg: string; // 필수값 true 설정 후 미입력시 출력할 메시지
  };
}

type Msg = string;
type SetMsg = (msg: Msg) => void;

const patterns: { [key: string]: { regex: RegExp, msg: string } } = {
  email: { regex: emailPattern, msg: '이메일 형식이 올바르지 않습니다.' },
  phone: { regex: phonePattern, msg: '휴대폰 번호 형식이 올바르지 않습니다.' },
  password: { regex: passwordPattern, msg: '비밀번호는 8~50자의 영문, 숫자, 특수문자를 포함해야 합니다.' },
  name: { regex: namePattern, msg: '이름은 한글로만 20자리 이내로 입력해주세요.' },
  id: { regex: idPattern, msg: '아이디는 영문, 숫자로만 입력해주세요.' },
  birth: { regex: birthPattern, msg: '생년월일 형식이 올바르지 않습니다.' },
  verifyNum: { regex: verifyNumPattern, msg: '인증번호 형식이 올바르지 않습니다.' },
  onlyNum: { regex: onlyNumPattern, msg: '숫자만 입력해주세요.' },
  onlyKorean: { regex: onlyKoreanPattern, msg: '한글만 입력해주세요.' },
  onlyEnglish: { regex: /^[a-zA-Z]+$/, msg: '영어만 입력해주세요.' },
  onlySpecial: { regex: /^[!@#\$%\^\&*\)\(+=._-]+$/, msg: '특수문자만 입력해주세요.' },
  onlyEnglishNum: { regex: /^[a-zA-Z0-9]+$/, msg: '영어, 숫자만 입력해주세요.' },
  onlyKoreanNum: { regex: /^[가-힣0-9]+$/, msg: '한글, 숫자만 입력해주세요.' },
  onlyEnglishSpecial: { regex: /^[a-zA-Z!@#\$%\^\&*\)\(+=._-]+$/, msg: '영어, 특수문자만 입력해주세요.' },
  onlyKoreanSpecial: { regex: /^[가-힣!@#\$%\^\&*\)\(+=._-]+$/, msg: '한글, 특수문자만 입력해주세요.' },
  onlyNumSpecial: { regex: /^[0-9!@#\$%\^\&*\)\(+=._-]+$/, msg: '숫자, 특수문자만 입력해주세요.' },
  onlyEnglishNumSpecial: { regex: /^[a-zA-Z0-9!@#\$%\^\&*\)\(+=._-]+$/, msg: '영어, 숫자, 특수문자만 입력해주세요.' },
  onlyKoreanNumSpecial: { regex: /^[가-힣0-9!@#\$%\^\&*\)\(+=._-]+$/, msg: '한글, 숫자, 특수문자만 입력해주세요.' },
  onlyEnglishKorean: { regex: /^[a-zA-Z가-힣]+$/, msg: '영어, 한글만 입력해주세요.' },
  onlyEnglishKoreanNum: { regex: /^[a-zA-Z가-힣0-9]+$/, msg: '영어, 한글, 숫자만 입력해주세요.' },
  onlyEnglishKoreanSpecial: { regex: /^[a-zA-Z가-힣!@#\$%\^\&*\)\(+=._-]+$/, msg: '영어, 한글, 특수문자만 입력해주세요.' },
};

export const inputValidate = (value: any, valid: InputValidateProps, setMsg: SetMsg): boolean => {
  if (valid.required?.value && !value) { // required가 true이고 value가 없다면
    setMsg(valid.required.msg || '필수 입력 항목입니다.');
    return false;
  }

  if (valid.min?.value && value.length < valid.min.value) {
    setMsg(valid.min.msg || `최소 ${valid.min.value}자 이상 입력해주세요.`);
    return false;
  }

  if (valid.max?.value && value.length > valid.max.value) {
    setMsg(valid.max.msg || `최대 ${valid.max.value}자 이하로 입력해주세요.`);
    return false;
  }

  if (valid.pattern?.value) {
    const pattern = patterns[valid.pattern.value];
    if (pattern && !pattern.regex.test(value)) {
      setMsg(pattern.msg);
      return false;
    }
  }

  setMsg('');
  return true;
};
