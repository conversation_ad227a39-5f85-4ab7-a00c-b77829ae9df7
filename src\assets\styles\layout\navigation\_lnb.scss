@use '@styles/utils/mixin' as m;

.c_lnb {
  @include m.flex(start, start, column);
  position: fixed;
  top: 0;
  left: 0;
  width: 75px;
  min-height: 100vh;
  transition: all 0.3s;
  padding: 0.625rem;
  z-index: 9999;
  background-color: var(--lnb_bg);

  .c_logo_wrapper {
    @include m.flex(flex-end, start);
    max-height: 59px;
    margin-bottom: 2rem;
    cursor: pointer;

    .c_logo {
      width: 45px;
      height: 45px;
      min-width: 45px;
      min-height: 45px;
      padding: 5px;
      margin: 5px;
      &::before {
        @include m.content('@assets/images/brit_logo.png');
      }
    }
    figcaption {
      width: 0;
      font-weight: 600;
      white-space: nowrap;
      transition: width 0.3s ease-in-out;
      overflow: hidden;
      color: var(--g_09);

      span {
        font-size: 0.75rem;
        font-family: 'govermentTTF', serif;
      }

      p {
        font-size: 1rem;
      }
    }
  }

  .c_nav {
    @include m.flex(center, start, column);

    width: 100%;
    gap: 16px;
    max-height: 100vh;
    overflow-y: auto;
    overflow-x: hidden;

    .c_nav_item {
      max-width: 40px;
      button {
        @include m.flex(center);
        gap: 10px;

        &::before {
          width: 24px;
          height: 24px;
        }

        span {
          font-size: 1.125rem;
          line-height: 1.125rem;
        }

        &:hover {
          background-color: transparent !important;
          opacity: 0.5;
        }
      }

      &.active {
        color: var(--p_05);
      }

      .c_child_nav {
        padding-left: 1.375rem;
        max-height: 0;
        opacity: 0;
        overflow: hidden;
        transition: all 0.3s ease-in-out;
      }

      &.open {
        .c_child_nav {
          max-height: 500px; // 실제 컨텐츠 높이보다 큰 값으로 설정
          opacity: 1;
        }
      }
    }
  }

  &.active {
    width: 250px;

    .c_logo_wrapper {
      gap: 0.125rem;
      figcaption {
        width: 100%;
      }
    }

    .c_nav {
      @include m.flex(start, start, column);

      .c_nav_item {
        max-width: 100%;
      }
    }

    + .c_main_content {
      width: calc(100% - 250px);
      margin-left: 250px;
    }
  }
}

[data-is-open-lnb='false'] {
  .c_lnb {
    .c_nav_item {
      @include m.flex(center, center, column);

      button {
        justify-content: center;
      }
    }
  }
}

@include m.bp_large() {
  .c_lnb {
    width: 65px;
    padding: 0.5rem 0.625rem;

    .c_logo_wrapper {
      width: 100%;
      margin-bottom: 1.75rem;

      figcaption {
        span {
          font-size: 0.625rem;
        }
        p {
          font-size: 0.875rem;
        }
      }
      .c_logo {
        width: 40px;
        height: 40px;
        min-width: 40px;
        min-height: 40px;
        margin: 0;
      }
    }

    .c_nav {
      .c_nav_item {
        button {
          span {
            font-size: 1rem;
            line-height: 1rem;
          }
        }
      }
    }

    &.active {
      width: 225px;

      + .c_main_content {
        width: calc(100% - 225px);
        margin-left: 225px;
      }
    }
  }
}

@include m.bp_medium() {
  .c_lnb {
    width: 55px;
    padding: 0.5rem 0.5rem;

    .c_logo_wrapper {
      margin-bottom: 1.5rem;

      figcaption {
        span {
          font-size: 0.5rem;
        }
        p {
          font-size: 0.75rem;
        }
      }
      .c_logo {
        width: 35px;
        height: 35px;
        min-width: 35px;
        min-height: 35px;
      }
    }

    .c_nav {
      .c_nav_item {
        button {
          span {
            font-size: 0.875rem;
            line-height: 0.875rem;
          }
        }
      }
    }

    &.active {
      width: 200px;

      + .c_main_content {
        width: calc(100% - 200px);
        margin-left: 200px;
      }
    }
  }
}
