import React, { useCallback, useEffect } from 'react';
import { useCurrentEditor } from '@tiptap/react';
import joinClassName from '@utils/joinClassName';
import { useThemeStore } from '@store/useThemeStore';
import { PaletteType } from '@components/common/TextEditor/components/tools/FontColor';

const colors = [
  // 1행 - 가장 밝은 색상들
  { label: 'white_01', value: 'inherit' },
  { label: 'blue_01', value: '#e6f3ff' },
  { label: 'red_01', value: '#ffe6e6' },
  { label: 'yellow_01', value: '#fff9e6' },
  { label: 'green_01', value: '#e6ffe6' },
  { label: 'purple_01', value: '#f2e6ff' },
  { label: 'brown_01', value: '#f2e6d9' },
  // 2행
  { label: 'white_03', value: '#ffffff' },
  { label: 'blue_02', value: '#99ccff' },
  { label: 'red_02', value: '#ffb3b3' },
  { label: 'yellow_02', value: '#fff0b3' },
  { label: 'green_02', value: '#b3ffb3' },
  { label: 'purple_02', value: '#d9b3ff' },
  { label: 'brown_02', value: '#d9b38c' },
  // 3행
  { label: 'white_05', value: '#999999' },
  { label: 'blue_03', value: '#3399ff' },
  { label: 'red_03', value: '#ff6666' },
  { label: 'yellow_03', value: '#ffe680' },
  { label: 'green_03', value: '#66ff66' },
  { label: 'purple_03', value: '#b366ff' },
  { label: 'brown_03', value: '#bf8040' },
  // 4행
  { label: 'white_07', value: '#666666' },
  { label: 'blue_04', value: '#0066ff' },
  { label: 'red_04', value: '#ff1a1a' },
  { label: 'yellow_04', value: '#ffd633' },
  { label: 'green_04', value: '#1aff1a' },
  { label: 'purple_04', value: '#8c1aff' },
  { label: 'brown_04', value: '#995c00' },
  // 5행 - 가장 어두운 색상들
  { label: 'white_10', value: '#000000' },
  { label: 'blue_05', value: '#0033cc' },
  { label: 'red_05', value: '#cc0000' },
  { label: 'yellow_05', value: '#ffcc00' },
  { label: 'green_05', value: '#00cc00' },
  { label: 'purple_05', value: '#6600cc' },
  { label: 'brown_05', value: '#663d00' },
];

interface ColorPalletteProps {
  setIsOpenPallette: (isOpen: boolean) => void;
  optionPosition: { top: number; left: number };
  palletteType: PaletteType;
}

const ColorPallette = ({ palletteType, setIsOpenPallette, optionPosition }: ColorPalletteProps) => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const { themeState } = useThemeStore();

  const handleColorChange = (color: string) => {
    if (color === 'inherit') {
      editor.chain().focus().unsetColor().run();
    } else {
      editor.chain().focus().setColor(color).run();
    }
  };

  const handleHighlightChange = (color: string) => {
    if (color === 'inherit') {
      editor.chain().focus().unsetHighlight().run();
    } else {
      editor.chain().focus().toggleHighlight({ color: color }).run();
    }
  };

  return (
    <div
      className="c_editor_color_pallette"
      style={{ top: `${optionPosition?.top || 0}px`, left: `${optionPosition?.left || 0}px` }}
    >
      <ul className="color-list">
        {colors.map((color) => {
          const isInherit = color.value === 'inherit';

          return (
            <li key={color.value} className={joinClassName('color-item', color.value)}>
              <button
                style={{ backgroundColor: isInherit ? '' : color.value }}
                onClick={() =>
                  palletteType === 'text_color' ? handleColorChange(color.value) : handleHighlightChange(color.value)
                }
                aria-label={color.label}
              />
            </li>
          );
        })}
      </ul>
    </div>
  );
};

export default ColorPallette;
