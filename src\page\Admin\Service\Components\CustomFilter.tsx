import type { ChangeEvent } from 'react';
import { useCallback, useEffect, useState } from 'react';

import type { IAfterGuiAttachedParams, IDoesFilterPassParams } from '@ag-grid-community/core';
import type { CustomFloatingFilterProps } from '@ag-grid-community/react';
import { useGridFilter } from '@ag-grid-community/react';
import Checkbox from '@components/common/Input/Checkbox';
import SelectBox, { OptionType } from '@components/common/SelectBox/SelectBox';

interface CustomFilterProps extends CustomFloatingFilterProps {
  name: string;
  params: string[];
}

export const CustomFilter = ({ model, onModelChange, name, params }: CustomFilterProps) => {
  const [closeFilter, setCloseFilter] = useState<(() => void) | undefined>();
  const [unappliedModel, setUnappliedModel] = useState('all'); // 기본값을 'all'로 설정
  const [options, setOptions] = useState<OptionType[]>(params.map((param) => ({ label: param, value: param })));

  const doesFilterPass = useCallback(
    (params: IDoesFilterPassParams) => {
      if (unappliedModel === 'all') return true;
      return params.data[name] === unappliedModel;
    },
    [name, unappliedModel]
  );

  const afterGuiAttached = useCallback(({ hidePopup }: IAfterGuiAttachedParams) => {
    setCloseFilter(() => hidePopup);
  }, []);

  // 그리드에 필터 핸들러 등록
  useGridFilter({
    doesFilterPass,
    afterGuiAttached,
  });

  useEffect(() => {
    if (model) {
      setUnappliedModel(model);
    } else {
      setUnappliedModel('all');
    }
  }, [model]);

  useEffect(() => {
    if (unappliedModel === 'all') {
      onModelChange(null); // 전체 선택 시 필터 비활성화
    } else if (unappliedModel) {
      onModelChange(unappliedModel);
    }
  }, [unappliedModel, onModelChange]);

  return (
    <div className="custom_filter">
      <div className="custom_filter_checkbox">
        <Checkbox
          hasLabel
          label={'전체'}
          value={'all'}
          checked={unappliedModel === 'all'}
          onChange={(e) => setUnappliedModel(e.target.value)}
        />
        {params.map((param) => (
          <Checkbox
            key={param}
            hasLabel
            label={param}
            value={param}
            checked={unappliedModel === param}
            onChange={(e) => setUnappliedModel(e.target.value)}
          />
        ))}
      </div>
    </div>
  );
};
