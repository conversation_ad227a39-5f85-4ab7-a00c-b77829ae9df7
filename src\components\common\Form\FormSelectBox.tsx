import SelectBox, { OptionType } from '@components/common/SelectBox/SelectBox';
import { Controller, useFormContext } from 'react-hook-form';

interface FormSelectBoxProps {
  name: string;
  label?: string;
  rules?: object;
  options: OptionType[];
  defaultValue?: OptionType;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  optionListClassName?: string;
}

const FormSelectBox = ({
  name,
  rules,
  options,
  defaultValue,
  placeholder,
  disabled,
  className,
  optionListClassName,
  label,
  ...attributes
}: FormSelectBoxProps) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onChange, value, ...restField }, fieldState }) => (
        <>
          {label && <label htmlFor={name}>{label}</label>}
          <div className="c_select_wrapper">
            <SelectBox
              {...restField}
              {...attributes}
              options={options}
              selectedValue={value}
              setSelectedValue={(option: OptionType) => {
                onChange(option);
              }}
              className={className}
              optionListClassName={optionListClassName}
              defaultValue={value || defaultValue}
              placeholder={placeholder}
              error={fieldState.error?.message}
              disabled={disabled}
            />
          </div>
        </>
      )}
    />
  );
};

export default FormSelectBox;
