@use '@styles/utils/mixin' as m;

.c_loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  transition: all 0.5s ease-in-out;

  &[data-loading='false'] {
    display: none;
    opacity: 0;
    pointer-events: none;
  }

  &[data-loading='true'] {
    display: block;
    opacity: 1;
    pointer-events: all;
  }

  .spinner {
    @include m.position_center(absolute);
  }
}

.c_grid_loading, .c_chart_loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99999;
  transition: all 0.5s ease-in-out;
  border-radius: 8px;

  &[data-loading='false'] {
    display: none;
    opacity: 0;
    pointer-events: none;
  }

  &[data-loading='true'] {
    display: block;
    opacity: 1;
    pointer-events: all;
  }

  .spinner {
    @include m.position_center(absolute);
  }
}

.c_chart_loading {
  p {
    @include m.flex(center, center);
    gap: 0.5rem;
    margin-top: 1rem;
    color: #fff;
    font-size: 1.25rem;
    font-weight: 700;
  }
}
