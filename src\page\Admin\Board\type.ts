// 게시판 관리
export interface AdminBoardSearchOptions {
  pageSize: { label: string; value: number }[];
  boardTypes: { label: string; value: string }[];
  boardUseYn: { label: string; value: 'Y' | 'N' | '전체' }[];
}

export interface AdminBoardFormValue {
  boardType: { label: string; value: string };
  boardUseYn: { label: string; value: 'Y' | 'N' | '전체' };
  boardName: string;
}

// 게시판 등록
export interface AdminBoardAddOptions {
  useYn: { label: string; value: 'Y' | 'N' }[];
  boardType: { label: string; value: string }[];
  category: { label: string; value: string | null }[];
}

export interface AdminBoardAddFormValue {
  boardType: { label: string; value: string };
  boardName: string;
  fileUseYn: { label: string; value: 'Y' | 'N' | 'none' };
  commentUseYn: { label: string; value: 'Y' | 'N' | 'none' };
  categoryA: { label: string; value: string };
  categoryB: { label: string; value: string };
  extraInfo: string;
  boardUseYn: { label: string; value: 'Y' | 'N' | 'none' };
}

export interface AdminBoardDetailType {
  boardType : string;
  boardName : string;
  fileUseYn : 'Y' | 'N';
  commentUseYn : 'Y' | 'N';
  categoryA : string | null;
  categoryB : string | null;
  categoryANm : string | null;
  categoryBNm : string | null;
  extraInfo : string;
  boardUseYn : 'Y' | 'N';
}
