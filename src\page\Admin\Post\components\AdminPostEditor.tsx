import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';

import Button from '@components/common/Button/Button';
import TableBody from '@components/common/Table/TableBody';
import TableBodyRow from '@components/common/Table/TableBodyRow';
import TableContainer from '@components/common/Table/TableContainer';
import TextEditor from '@components/common/TextEditor/TextEditor';
import FormInput from '@components/common/Form/FormInput';
import Filebox from '@components/common/Filebox/Filebox';
import FormSelectBox from '@components/common/Form/FormSelectBox';
import Form from '@components/common/Form/Form';
import { AdminPostDetailFormValue, AdminPostDetailOptions, AdminPostDetailType } from '@page/Admin/Post/type';

import { getPostBoardList, getPostDetail, postPosts, PutPost } from '@api/admin/postApi';
import { useAlertStore } from '@store/useAlertStore';
import { getFile } from '@api/admin/fileAPI';

const fileAcceptList = [
  // 📸 이미지
  'jpg',
  'jpeg',
  'png',
  // 'gif',
  // 'webp',
  // 'bmp',
  // 'svg',
  // 'tif',
  // 'tiff',

  // // 🎞️ 비디오
  // 'mp4',
  // 'mov',
  // 'webm',
  // 'avi',
  // 'mkv',
  // 'wmv',

  // // 🔊 오디오
  // 'mp3',
  // 'wav',
  // 'ogg',
  // 'm4a',
  // 'aac',
  // 'flac',

  // // 📄 문서
  // 'pdf',
  // 'doc',
  // 'docx',
  // 'xls',
  // 'xlsx',
  // 'ppt',
  // 'pptx',
  // 'txt',
  // 'csv',
  // 'rtf',
  // 'md',
  // 'odt',

  // // 🧱 압축 파일
  // 'zip',
  // 'rar',
  // '7z',
  // 'tar',
  // 'gz',
  // 'bz2',
];

interface Props {
  type: 'add' | 'edit';
}

const AdminPostEditor = () => {
  const [templetList, setTempletList] = useState<AdminPostDetailOptions['templetList']>([
    { label: '선택이 필요합니다', value: '' },
  ]);

  const notiYn: AdminPostDetailOptions['notiYn'] = [
    { label: '사용', value: 'Y' },
    { label: '미사용', value: 'N' },
  ];

  const initValues: AdminPostDetailFormValue = {
    templetType: { label: '선택이 필요합니다', value: '' },
    templetList: { label: '선택이 필요합니다', value: '' },
    notiYn: { label: '미사용', value: 'N' },
    title: '',
  };

  // -----------------------------------------------------------
  const { id } = useParams();
  const type = id ? "edit" : "add";
  const navigate = useNavigate();
  const { setAlertState, initAlertState } = useAlertStore();

  const [defaultValues, setDefaultValues] = useState(initValues);
  const [dateInfo, setDateInfo] = useState<AdminPostDetailType['dateInfo']>(undefined);
  const [fileList, setFileList] = useState<FileList | null>(null);
  const [originalContents, setOriginalContents] = useState<string>('<p style=""><span></span></p>');
  const [originalFileList, setOriginalFileList] = useState<FileList | null>(null);
  const [originalFileMeta, setOriginalFileMeta] = useState([]);
  const [contents, setContents] = useState<string>('');
  const methods = useForm({ defaultValues });

  const { reset } = methods;

  const getBoardList = async () => {
    const response = await getPostBoardList({});

    setTempletList(
      response?.data?.map((board) => {
        return { label: board.templetNm, value: board.id };
      })
    );
  };

  const getFileObj = async ({ fileId, fileName }: { fileId: string; fileName: string }) => {
    const response = await getFile({ id: fileId });

    const file = new File([response], fileName, { type: response.type });

    return file;
  };

  useEffect(() => {
    if (type === 'add') {
      getBoardList();
    }

    if (type === 'edit') {
      const handleGetPostDetail = async () => {
        const response = await getPostDetail({ id });

        const detailData: AdminPostDetailFormValue = {
          templetType: { label: response.templetTypeNm, value: response.templetTypeCd },
          templetList: { label: response.templetNm, value: response.templetId },
          notiYn: { label: response.notiYn === 'Y' ? '사용' : '미사용', value: response.notiYn },
          title: response.title,
        };

        const files = (await Promise.all(
          (response.attchFiles || []).map((fileData) => getFileObj({ fileId: fileData.id, fileName: fileData.originName }))
        )) as File[];

        const toFileList = (files: File[]): FileList => {
          const dataTransfer = new DataTransfer();
          files.forEach((file) => dataTransfer.items.add(file));
          return dataTransfer.files;
        };

        reset(detailData);
        setDefaultValues(detailData);
        setDateInfo({
          createUser: response.dateInfo.createUser,
          createDate: response.dateInfo.createDate,
          updateUser: response.dateInfo.updateUser,
          updateDate: response.dateInfo.updateDate,
        });
        setContents(response.content);
        setFileList(toFileList(files));
        setOriginalContents(response.content);
        setOriginalFileList(toFileList(files));
        setOriginalFileMeta(response.attchFiles);
      };

      handleGetPostDetail();
    }
  }, []);

  // 수정 시 파일 객체 비교
  const addedFileKey = (file: File) => `${file.name}_${file.size}_${file.lastModified}`;
  const deletedfileKey = (name: string, size: number) => `${name}_${size}`;

  const compareFileLists = () => {
    const originalFiles = Array.from(originalFileList || []);
    const currentFiles = Array.from(fileList || []);
    const originalMeta = Array.from(originalFileMeta || []);

    const currentKeys = new Set(Array.from(currentFiles).map((file) => deletedfileKey(file.name, file.size)));
    const originalKeys = new Set(originalFiles.map(addedFileKey));

    const deletedFileIds = originalMeta
      .filter((meta) => !currentKeys.has(deletedfileKey(meta.originName, meta.size)))
      .map((meta) => meta.id);
    const addedFiles = currentFiles.filter((f) => !originalKeys.has(addedFileKey(f)));

    return {
      deletedFileIds,
      addedFiles,
    };
  };

  // 등록/수정
  // 필수값 체크
  const isValueValid = (value: string | number): boolean => {
    const isValidString = (val: string) => val.trim() !== '' && val !== 'none' && val !== '<p style=""></p>';

    const isValidNumber = (val: number) => val !== 0;

    if (typeof value === 'string') {
      return isValidString(value);
    }

    if (typeof value === 'number') {
      return isValidNumber(value);
    }

    return false;
  };

  const isFieldValid = (data) => {
    const { templetList, notiYn, title, contents } = data;

    const alertMsg = (field: string) => {
      return setAlertState({
        isOpen: true,
        content: field,
        onConfirm: initAlertState,
      });
    };

    const validations = [
      ...(type === 'add' ? [{ value: templetList.value, message: '게시판 명을 선택해 주세요' }] : []),
      { value: title, message: '게시글 제목을 입력해 주세요' },
      ...(type === 'add' ? [{ value: notiYn.value, message: '게시판 유형을 선택해 주세요' }] : []),
      { value: contents, message: '내용을 입력해 주세요' },
    ];

    for (const { value, message } of validations) {
      if (!isValueValid(value)) {
        alertMsg(message);
        return false;
      }
    }

    return true;
  };

  // 저장/수정
  const handleSubmit = async (data: AdminPostDetailFormValue) => {
    const { templetList, notiYn, title } = data;

    if (isFieldValid({ ...data, contents })) {
      const postData = {
        templetId: templetList.value,
        title,
        ...(type === 'add' && { notiYn: notiYn.value }),
        ...(type === 'add' && fileList && { files: fileList }),
        content: contents,
      };

      if (type === 'add') {
        const response = await postPosts(postData);

        if (response === 'CREATE') {
          setAlertState({
            isOpen: true,
            content: '게시글이 등록되었습니다',
            onConfirm: () => {
              navigate(`/admin/post`);
              initAlertState();
            },
          });
        }
      } else if (type === 'edit') {
        const { addedFiles, deletedFileIds } = compareFileLists();

        const editPostData = {
          ...postData,
          ...(type === 'edit' && addedFiles && { saveFiles: addedFiles }),
          ...(type === 'edit' && deletedFileIds && { delFileIds: deletedFileIds }),
        };

        const response = await PutPost({ id, ...editPostData });
        if (response === 'UPDATE') {
          setAlertState({
            isOpen: true,
            content: '데이터를 성공적으로 수정하였습니다',
            onConfirm: () => {
              navigate(`/admin/post/detail/${id}`);
              initAlertState();
            },
          });
        }
      }
    }
  };

  // 초기화
  const handleReset = () => {
    reset(defaultValues);
    setContents(originalContents);
    setFileList(originalFileList);
  };

  return (
    <Form onSubmit={handleSubmit} methods={methods} className="admin_post_detail_wrapper">
      <TableContainer>
        <TableBody>
          {type === 'add' && (
            <TableBodyRow
              rowData={{
                title: '게시판 명',
                required: type === 'add' ? true : false,
                isFullWidth: true,
                contents:
                  type === 'add' ? (
                    <FormSelectBox name="templetList" options={templetList} />
                  ) : (
                    defaultValues.templetList.label
                  ),
              }}
            />
          )}
          {type === 'edit' && (
            <TableBodyRow
              rowData={[
                {
                  title: '게시판 유형',
                  contents: defaultValues.templetType.label,
                },
                {
                  title: '게시판 명',
                  contents: defaultValues.templetList.label,
                },
              ]}
            />
          )}
          <TableBodyRow
            rowData={{
              title: '게시글 제목',
              required: true,
              isFullWidth: true,
              contents: <FormInput name="title" wrapperClassName="full" placeholder="제목을 입력해 주세요" />,
            }}
          />
          {type === 'edit' && (
            <>
              <TableBodyRow
                rowData={[
                  {
                    title: '작성자',
                    contents: dateInfo?.createUser,
                  },
                  {
                    title: '작성 일시',
                    contents: dateInfo?.createDate,
                  },
                ]}
              />
              <TableBodyRow
                rowData={[
                  {
                    title: '수정자',
                    contents: dateInfo?.updateUser,
                  },
                  {
                    title: '수정 일시',
                    contents: dateInfo?.updateDate,
                  },
                ]}
              />
            </>
          )}
          <TableBodyRow
            rowData={[
              {
                title: '공지 여부',
                required: type === 'add' ? true : false,
                isFullWidth: true,
                contents:
                  <FormSelectBox name="notiYn" options={notiYn} />
              },
            ]}
          />
          <TableBodyRow
            rowData={[
              {
                title: '첨부파일',
                isFullWidth: true,
                contents: (
                  <Filebox
                    className="admin_post_filebox"
                    files={fileList}
                    setFiles={setFileList}
                    multiple
                    accept={fileAcceptList}
                  />
                ),
              },
            ]}
          />
        </TableBody>
      </TableContainer>
      <TextEditor
        className={utils.joinClassName('admin_post_detail_contents', type === 'add' && 'add')}
        content={contents}
        onChange={setContents}
      />
      <div className="admin_post_detail_btns">
        <Button text="취소" onClick={() => navigate(type === 'edit' ? `/admin/post/detail/${id}` : '/admin/post')} />
        <div className="admin_post_detail_btns_right">
          <Button text="초기화" color="grayscale" onClick={handleReset} />
          <Button text="저장" type="submit" />
        </div>
      </div>
    </Form>
  );
};

export default AdminPostEditor;
