@use '@styles/utils/mixin' as m;

.manage_user_menu {
  .c_title {
    font-size: 2rem;
    line-height: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
  }

  .c_sub_title {
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .manage_user_contents {
    @include m.flex();

    .manage_user_contents_left {
      min-width: 260px;
      margin-right: 1.25rem;

      // 카테고리 리스트
      .user_menu_category_wrap {
        .user_menu_button {
          @include m.flex(start, flex-end, row);
          gap: 0.5rem;
          margin-bottom: 1rem;

          .c_button {
            .c_button_text {
              font-size: 12px;
            }
          }
        }

        .user_menu_category {
          padding: 16px;
          border-radius: 8px;

          .user_menu_category_top {
            .user_menu_category_parent_wrap {
              padding: 16px 0;

              .user_menu_category_parent {
                padding: 8px;
                font-weight: 600;

                &.clicked {
                  transition: all 0.3s;
                  border-radius: 8px;
                }
              }

              .user_menu_category_child_wrap {
                display: flex;
                flex-direction: column;

                .user_menu_category_child {
                  margin-left: 16px;
                  padding: 8px;
                  display: flex;
                  align-items: center;

                  &::before {
                    margin-right: 8px;
                    width: 16px;
                    height: 16px;
                  }

                  &.clicked {
                    transition: all 0.3s;
                    border-radius: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }

    .manage_user_contents_right {
      padding-left: 20px;
      width: 100%;

      .user_menu_info_wrap {
        position: relative;
        // 조회 내용 없을 경우
        .user_menu_info_nothing {
          position: absolute;
          width: 100%;
          height: 477px; // 유동적으로 수정 필요
          z-index: 1;
          border-radius: 8px;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .user_menu_info {
          // table
          .c_table {
            width: 100%;

            #user_menu_info_table {
              .user_info_title {
                min-width: 200px;
              }
            }

            .c_table_body {
              tr {
                min-height: 53px;
                height: 53px;

                td {
                  width: 100%;

                  input {
                    width: 100%;
                  }

                  .c_select {
                    height: 32px;
                  }
                }
              }
            }
          }
        }

        .user_menu_info_child_menu {
          position: relative;
          margin-top: 24px;

          .user_menu_info_child_menu_button {
            position: absolute;
            right: 0;
            top: 0;
            display: flex;
            flex-direction: row;
            justify-content: flex-end;
            gap: 8px;
            margin-bottom: 16px;
          }

          .c_table_wrapper {
            .c_table {
              width: 100%;

              #user_info_related_table {
                .related_check {
                  width: 5%;
                }

                .related_id {
                  width: 20%;
                }

                .related_page_name {
                  width: 30%;
                }
              }

              .related_table_row {
                text-align: center;

                input {
                  width: 100%;
                }
              }

              .c_table_body {
                .related_no_page {
                  td {
                    text-align: center;
                  }
                }
              }
            }
          }
        }

        .menu_required {
          &::after {
            content: '*';
            margin-left: 4px;
          }
        }

        .menu_save_button {
          margin-top: 16px;
          float: right;
          width: 80px;
        }
      }
    }
  }
}
