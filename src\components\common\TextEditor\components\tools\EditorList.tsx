import IconButton from '@components/common/Button/IconButton';
import { useCurrentEditor } from '@tiptap/react';
import React from 'react';
import TextAlign from '@tiptap/extension-text-align';

const EditorList = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return;

  const listNum = () => {
    editor.chain().focus().toggleOrderedList().run();
  };

  const listBullet = () => {
    editor.chain().focus().toggleBulletList().run();
  };

  const listCheck = () => {
    editor.chain().focus().toggleTaskList().run();
  };

  /**
   * @TODO
   *
   * 1. 리스트의 정렬시 marker 위치 조정
   *
   */

  return (
    <div className="item_box list">
      <IconButton
        text="list"
        icon="list_num"
        iconOnly
        fill={editor.isActive('orderedList') ? 'filled' : 'unfilled'}
        color={editor.isActive('orderedList') ? 'primary' : 'grayscale'}
        onClick={listNum}
        size='smallest'
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('blockquote') ||
          editor.isActive({ textAlign: 'right' }) ||
          editor.isActive({ textAlign: 'left' }) ||
          editor.isActive({ textAlign: 'center' })
        }
      />
      <IconButton
        text="list"
        icon="list_bullet"
        iconOnly
        fill={editor.isActive('bulletList') ? 'filled' : 'unfilled'}
        color={editor.isActive('bulletList') ? 'primary' : 'grayscale'}
        onClick={listBullet}
        size='smallest'
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('blockquote') ||
          editor.isActive({ textAlign: 'right' }) ||
          editor.isActive({ textAlign: 'left' }) ||
          editor.isActive({ textAlign: 'center' })
        }
      />
      <IconButton
        text="list"
        icon="list_check"
        iconOnly
        fill={editor.isActive('taskList') ? 'filled' : 'unfilled'}
        color={editor.isActive('taskList') ? 'primary' : 'grayscale'}
        onClick={listCheck}
        size='smallest'
        disabled={
          editor.isActive('codeBlock') ||
          editor.isActive('blockquote') ||
          editor.isActive({ textAlign: 'right' }) ||
          editor.isActive({ textAlign: 'left' }) ||
          editor.isActive({ textAlign: 'center' })
        }
      />
    </div>
  );
};

export default EditorList;
