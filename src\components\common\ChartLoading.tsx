import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>oa<PERSON> } from 'react-spinners';

const ChartLoading = ({ isLoading }: { isLoading: boolean }) => {
  if (!isLoading) return null;

  return (
    <div className="c_chart_loading" data-loading={isLoading}>
      <div className="spinner">
        <BeatLoader size={40} color="#fff" loading={isLoading} />
        <p>
          Loading <BeatLoader size={5} color="#fff" loading={true} />
        </p>
      </div>
    </div>
  );
};

export default ChartLoading;
