import { deleteEventPopupList, getEventPopupList } from '@api/admin/eventPopupAPI';
import Button from '@components/common/Button/Button';
import CalendarRange, { DateRange } from '@components/common/Calendar/CalendarRange';
import ControlBox from '@components/common/ControlBox/ControlBox';
import FileImagePreview from '@components/common/Filebox/components/FileImagePreview';
import { useFilePreviewer } from '@components/common/Filebox/store/useFilePreviewer';
import Form from '@components/common/Form/Form';
import FormInput from '@components/common/Form/FormInput';
import Grid from '@components/common/Grid/Grid';
import Pagination from '@components/common/Pagination';
import SelectBox from '@components/common/SelectBox/SelectBox';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useAlertStore } from '@store/useAlertStore';
import { useConfirmStore } from '@store/useConfirmStore';
import { calcNoSort } from '@utils/calcNoSort';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';

const EventPopupList = () => {
  const navigate = useNavigate();
  const { activeAlert } = useAlertStore();
  const { setConfirmState, initConfirmState } = useConfirmStore();
  const [eventPopupList, setEventPopupList] = useState<any[]>([]);
  const { setPreviewModalState, previewImageInfo, setPreviewImageInfo } = useFilePreviewer();

  const colDefs = [
    { headerName: 'No.', field: 'sort', width: 70 },
    {
      headerName: '썸네일',
      field: 'thumbnail',
      width: 100,
      onCellClicked: () => {},
      cellRenderer: (params) => {
        const src = params.data.fileInfo.length > 0 ? params.data.fileInfo[0].id : '';

        return src ? (
          <img
            className="grid_thumbnail cursor-pointer"
            src={`/api/files/${src}`}
            alt="썸네일"
            onClick={() => {
              setPreviewImageInfo({
                name: params.data.fileInfo[0].name,
                url: `/api/files/${src}`,
                size: params.data.fileInfo[0].size,
                type: params.data.fileInfo[0].type,
              });
              setPreviewModalState(true);
            }}
          />
        ) : (
          <div className="grid_empty_thumbnail" />
        );
      },
    },
    { headerName: '제목', field: 'title', flex: 1 },
    { headerName: '부제목', field: 'subTitle', flex: 1 },
    {
      headerName: '노출기간',
      field: 'date',
      width: 200,
      cellRenderer: (params) => {
        const exposureStart = new Date(params.data.exposureStart);
        const exposureEnd = new Date(params.data.exposureEnd);
        const formattedExposureStart = dayjs(exposureStart).format('YYYY-MM-DD');
        const formattedExposureEnd = dayjs(exposureEnd).format('YYYY-MM-DD');
        return (
          <>
            {formattedExposureStart} ~ {formattedExposureEnd}
          </>
        );
      },
    },
    {
      headerName: '등록자',
      field: 'dateInfo.createUser',
      width: 90,
    },
    {
      headerName: '등록일',
      field: 'dateInfo.createDate',
      width: 170,
    },
    {
      headerName: '수정자',
      field: 'dateInfo.updateUser',
      width: 90,
    },
    {
      headerName: '수정일',
      field: 'dateInfo.updateDate',
      width: 170,
    },
  ];

  const [listPageInfo, setListPageInfo] = useState({ currentPage: 1, pageSize: 20, totalCount: 0, totalPages: 0 });
  const [selectedRow, setSelectedRow] = useState<any[]>([]);

  const methods = useForm({ defaultValues: { name: '', startDate: null, endDate: null } });

  const getEventPopupListData = async () => {
    const { data, pageInfo } = await getEventPopupList({
      params: {
        page: listPageInfo.currentPage - 1,
        size: listPageInfo.pageSize,
        title: methods.getValues('name'),
        startDate: methods.getValues('startDate'),
        endDate: methods.getValues('endDate'),
      },
    });
    const parsedData = data.map((item, index) => {
      return {
        ...item,
        sort: calcNoSort({
          totalCount: pageInfo.totalCount,
          currentPage: listPageInfo.currentPage,
          pageSize: listPageInfo.pageSize,
          index,
        }),
      };
    });
    setEventPopupList(parsedData);
    setListPageInfo({ ...pageInfo, currentPage: pageInfo.currentPage + 1 });
  };

  // 이벤트 기간 변경
  const handleDateChange = (date: DateRange) => {
    methods.setValue('startDate', date.startDate ? dayjs(date.startDate).format('YYYY-MM-DDT00:00:00') : null);
    methods.setValue('endDate', date.endDate ? dayjs(date.endDate).format('YYYY-MM-DDT00:00:00') : null);
  };

  // 페이지 사이즈 변경
  const handlePageSizeChange = (page) => {
    setListPageInfo({ ...listPageInfo, pageSize: parseInt(page.value) });
  };

  // 페이지 변경
  const handlePageChange = (page: number) => {
    setListPageInfo({ ...listPageInfo, currentPage: page });
  };

  // 행 선택
  const onRowSelected = (event: any) => {
    setSelectedRow((prev) => {
      if (prev.includes(event.data.id)) {
        return prev.filter((id) => id !== event.data.id);
      }
      return [...prev, event.data.id];
    });
  };

  const deleteEventPopup = async () => {
    const response = await deleteEventPopupList({ ids: selectedRow });
    if (response) {
      initConfirmState();
      activeAlert(response.message);
      getEventPopupListData();
    }
  };

  // 삭제
  const handleClickDelete = async () => {
    if (selectedRow.length === 0) {
      activeAlert('선택된 이벤트 팝업이 없습니다. \n 삭제할 이벤트 팝업을 선택해주세요.');
      return;
    }

    setConfirmState({
      isOpen: true,
      confirmType: 'delete',
      title: '알림',
      content: `${selectedRow.length} 건의 이벤트 팝업을 삭제하시겠습니까?`,
      onConfirm: deleteEventPopup,
      onCancel: initConfirmState,
    });
  };

  // 추가
  const handleClickAdd = () => {
    navigate('/admin/event_popup/add');
  };

  const handleGoDetail = (id: string) => {
    navigate(`/admin/event_popup/detail/${id}`);
  };

  const handleClickTest = () => {
    navigate(`/admin/event_popup/test`);
  };

  // 페이지 사이즈, 현재 페이지에 따른 조회
  useEffect(() => {
    getEventPopupListData();
  }, [listPageInfo.pageSize, listPageInfo.currentPage]);

  return (
    <div className="admin_event_popup">
      <div className="admin_event_popup_list">
        <Form onSubmit={getEventPopupListData} methods={methods}>
          <ControlBox>
            <label>이벤트 기간</label>
            <CalendarRange onChange={handleDateChange} />
            <label htmlFor="name">이벤트 팝업 제목</label>
            <FormInput id="name" name="name" placeholder="이벤트 팝업 제목을 입력해주세요." />
            <Button type="submit" text="조회" />
          </ControlBox>
          <TableContainerHeader
            leftChildren={
              <div className="page_size">
                <p>
                  총 <span>{listPageInfo.totalCount}</span>건
                </p>
                <SelectBox
                  selectedValue={{ label: listPageInfo.pageSize.toString(), value: listPageInfo.pageSize.toString() }}
                  defaultValue={{ label: listPageInfo.pageSize.toString(), value: listPageInfo.pageSize.toString() }}
                  setSelectedValue={handlePageSizeChange}
                  options={[
                    { label: '10', value: '10' },
                    { label: '20', value: '20' },
                    { label: '30', value: '30' },
                  ]}
                />
              </div>
            }
            rightChildren={
              <div className="button_wrapper">
                <Button text="삭제" color="red" onClick={handleClickDelete} />
                <Button text="추가" onClick={handleClickAdd} />
                <Button text="이벤트 팝업 테스트" color="grayscale" onClick={handleClickTest} />
              </div>
            }
          />
          <Grid
            columns={colDefs}
            rowData={eventPopupList}
            autoSizeStrategy="onGridSizeChanged"
            defaultColDef={{
              onCellClicked: (event) => handleGoDetail(event.data.id),
            }}
            gridOptions={{
              onRowSelected: onRowSelected,
              rowSelection: { checkboxes: true, headerCheckbox: true, mode: 'multiRow' },
              rowHeight: 100,
            }}
          />
          <Pagination
            totalCount={listPageInfo.totalCount}
            itemCountPerPage={listPageInfo.pageSize}
            currentPage={listPageInfo.currentPage}
            onPageChange={handlePageChange}
          />
        </Form>
        <FileImagePreview />
      </div>
    </div>
  );
};

export default EventPopupList;
