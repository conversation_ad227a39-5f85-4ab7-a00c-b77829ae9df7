import { AgGridReact } from '@ag-grid-community/react';
import Button from '@components/common/Button/Button';
import TableContainerHeader from '@components/common/Table/TableContainerHeader';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  CellValueChangedEvent,
  ColDef,
  GridSizeChangedEvent,
  RowDragEndEvent,
  RowSelectedEvent,
} from '@ag-grid-community/core';
import SubTitle from '@components/common/Title/SubTitle';
import { useConfirmStore } from '@store/useConfirmStore';
import Grid from '@components/common/Grid/Grid';
import { useCodeStore } from '@page/System/Code/store/useCodeStore';
import { deleteCode, getCodeList, postCode } from '@api/admin/systemCodeAPI';
import { useAlertStore } from '@store/useAlertStore';
import DataGrid from '@components/common/Grid/DataGrid';
import SwitchCellEditor from '@components/common/Grid/SwitchCellEitor';
import Tooltip from '@components/common/Tooltip/Tooltip';
import useEventBus from '@hooks/useEventBus';
import { SubPage, Page } from '../type';
import { getSubPagesList } from '@api/user/pageApi';

interface Props {
  pageId: Page['id'];
}

const SubPagesLsit = ({ pageId }: Props) => {
  const [rowData, setRowData] = useState<SubPage[]>([]);

  useEffect(() => {
    if (!pageId) return;
    const fetchData = async () => {
      const res = await getSubPagesList(pageId);
      setRowData(res);
    };

    fetchData();
  }, [pageId]);

  const [columnDefs, setColumnDefs] = useState<ColDef[]>([
    {
      field: 'id',
      headerName: 'No',
      valueSetter: (params) => {
        params.data.id = params.newValue;
        return true;
      },
      valueParser: (params) => Number(params.newValue),
      cellEditor: 'agTextCellEditor',
      cellEditorParams: { maxLength: 10 },
      cellEditorSelector: () => ({ component: 'agTextCellEditor' }),
      cellEditorPopup: true,
      cellEditorPopupPosition: 'under',
      singleClickEdit: true,
      useValueParserForImport: true,
    },
    {
      field: 'pageName',
      headerName: '페이지명',
      cellClass: 'underline text-[0.7rem] cursor-pointer',
      onCellClicked: (event: any) => {
        // 클릭된 셀의 데이터를 가져옵니다
        const { field, value } = event;

        // 클릭한 셀이 'pageName'일 경우 팝업을 띄웁니다
        if (field === 'pageName') {
          alert(`클릭한 페이지명: ${value}`);
        }
      },
    },
    {
      field: 'pageFileName',
      headerName: '파일명',
    },
    {
      field: 'pageTypeCode',
      headerName: '유형',
      valueFormatter: ({ value }) => {
        const reverseMap = {
          L: '목록',
          R: '등록',
          M: '수정',
          P: '상세',
        };
        return reverseMap[value] ?? value;
      },
    },
    {
      field: 'pageDesc',
      headerName: '설명',
    },
    {
      field: 'roleUseYn',
      headerName: '권한 사용 여부',
    },
    {
      field: 'createUser',
      headerName: '등록자',
    },
    {
      field: 'createDate',
      headerName: '등록일',
    },
    {
      field: 'updateUser',
      headerName: '변경자',
    },
    {
      field: 'updateDate',
      headerName: '변경일',
    },
  ]);

  const [isOpenModal, setIsOpenModal] = useState<boolean>(false);

  const [selectedRowData, setSelectedRowData] = useState<SubPage>(null);

  return (
    <>
      {/* 테이블 헤더 영역 */}
      <TableContainerHeader
        leftChildren={<SubTitle>페이지 하위 목록</SubTitle>}
        rightChildren={
          <div className="code_list_btn_wrapper">
            <Button
              className="register_btn"
              text="삭제"
              color="red"
              // disabled={selectedRows.length < 1}
              // onClick={handleDeletePop}
              clickLog={{ buttonSection: '코드 목록' }}
            />
            <Button
              className="register_btn"
              text="저장"
              color="grayscale"
              // disabled={isSameData}
              // onClick={handleSave}
              clickLog={{ buttonSection: '코드 목록' }}
            />
            <Button
              className="register_btn"
              text="행 추가"
              // onClick={addRow}
              clickLog={{ buttonSection: '코드 목록' }}
            />
          </div>
        }
      />
      <div className="code_list">
        <DataGrid
          columnDefs={columnDefs}
          rowData={[
            {
              id: 1,
              pageName: '회원 목록',
              pageFileName: 'MemberListPage.tsx',
              pageTypeCode: 'L', // valueFormatter에 의해 '목록'으로 보일 것
              pageDesc: '회원 목록 페이지',
              bbsUseYn: 'Y',
              roleUseYn: 'N',
              createUser: 'admin',
              createDate: '2024-04-01',
              updateUser: 'editor',
              updateDate: '2024-04-15',
            },
          ]}
        />
      </div>
    </>
  );
};

export default SubPagesLsit;
