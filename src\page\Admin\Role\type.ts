export interface DataData {
  createUser: string;
  createDate: string;
  updateUser: string;
  updateDate: string;
}

export interface AuthData {
  id: number;
  name: string;
  dateInfo: DataData;
}

export interface MenuData {
  id: number;
  name: string;
  desc: string;
  url: string;
  depth: number;
  sortNo: number;
  useYn: string;
  childs: Array<MenuData | null>;
  dateInfo: DataData;
}

export interface PageAuthData {
  id: number;
  parentId: number | null;
  boardTempletId: number | null;
  pageName: string;
  pageFileName: string;
  pageContent: string;
  pageLink: string | null;
  pageTypeCode: string;
  pageDesc: string;
  roleUseYn: string;
  pageRole: string | null;
  dateInfo: DataData;
  filePath: string;
}

export interface ParsedPageAuthData {
  pageId: number;
  pageName?: string;
  // readRole: boolean;
  // regRole: boolean;
  // modRole: boolean;
  // delRole: boolean;
  readRoleYn?: boolean | 'Y' | 'N';
  regRoleYn?: boolean | 'Y' | 'N';
  modRoleYn?: boolean | 'Y' | 'N';
  delRoleYn?: boolean | 'Y' | 'N';
  url?: string | null;
}


export interface ParsedServiceData {
  id: number;
  name: string;
  depth: number;
  childs?: ParsedServiceData[];
  serviceGroupId: number;
  serviceGroupName: string;
  serviceDesc: string;
}

export interface SearchFormValues {
  name: string | null;
}

export interface DetailFormValues {
  name: string;
}
