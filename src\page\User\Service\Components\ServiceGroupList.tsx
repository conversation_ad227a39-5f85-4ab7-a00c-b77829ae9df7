import IconButton from '@components/common/Button/IconButton';
import TableBody from '@components/common/Table/TableBody';
import TableContainer from '@components/common/Table/TableContainer';
import TableHeader from '@components/common/Table/TableHeader';
import { useConfirm } from '@hooks/useConfirm';
import { useUserServiceGroupAPI } from '@page/User/Service/hooks/useUserServiceGroupAPI';
import { useServiceStore } from '@page/User/Service/store/useServiceStore';
import { ServiceGroupData } from '@page/User/Service/type';
import React, { useEffect } from 'react';

const ServiceGroupList = () => {
  const {
    serviceGroupList,
    serviceGroupDetail,
    setServiceGroupDetail,
  } = useServiceStore();

  const { openConfirm } = useConfirm();
  const { getServiceGroupList, deleteServiceGroup } = useUserServiceGroupAPI();

  // functions
  const handleDeleteServiceGroup = (e: React.MouseEvent, id: number) => {
    e.stopPropagation(); // 이벤트 버블링 방지
    const targetGroup = serviceGroupList.find((item) => item.id === id);
    openConfirm(
      <>
        <hr /> <br />
        <b>"{targetGroup?.name}"</b> <br />
        <br />
        <hr /> <br />
        삭제한 그룹은 되돌릴 수 없습니다. <br />
        정말 삭제 하시겠습니까?
      </>
      , () => {
        deleteServiceGroup(id);
      }, 'delete');
  };

  const handleGroupSelect = (item: ServiceGroupData | null) => {
    setServiceGroupDetail(item);
  };

  // 서비스 그룹 아이템 렌더링
  const renderGroupItem = (item: ServiceGroupData) => (
    <tr key={item.id} className={item.id === serviceGroupDetail?.id ? 'active' : ''}>
      <td onClick={() => handleGroupSelect(item)}>
        <div className="td_inner">
          <span>{item.name}</span>
          <IconButton
            text="삭제"
            onClick={(e: React.MouseEvent) => handleDeleteServiceGroup(e, item.id)}
            design="circle"
            size="smallest"
            fill="unfilled"
            color="grayscale"
            icon="close"
            iconOnly
          />
        </div>
      </td>
    </tr>
  );

  useEffect(() => {
    getServiceGroupList();
  }, []);

  return (
    <>
      <TableContainer className="service_group_list">
        <TableHeader>
          <tr>
            <th>서비스 그룹명</th>
          </tr>
        </TableHeader>
        <TableBody>
          {serviceGroupList ? (
            <>
              <tr className={!serviceGroupDetail ? 'active' : ''}>
                <td onClick={() => handleGroupSelect(null)}>전체 그룹</td>
              </tr>
              {serviceGroupList?.map(renderGroupItem)}
            </>
          ) : (
            <tr className='no_result'>
              <td>
                조회된 결과가 없습니다.
              </td>
            </tr>
          )}
        </TableBody>
      </TableContainer>
    </>
  );
};

export default ServiceGroupList;
