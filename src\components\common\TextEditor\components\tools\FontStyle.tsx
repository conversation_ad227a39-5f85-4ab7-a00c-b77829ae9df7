import React from 'react';
import { useCurrentEditor } from '@tiptap/react';
import IconButton from '@components/common/Button/IconButton';
import { IconType } from '@components/common/Button/types';
import { EditorConstant } from '@components/common/TextEditor/utils/constant';

const FontStyle = () => {
  const { editor } = useCurrentEditor();
  if (!editor) return null;

  const { TEXT_STYLES } = EditorConstant;

  return (
    <div className="item_box text_style">
      {TEXT_STYLES.map((style) => (
        <IconButton
          key={style}
          text={style}
          icon={style as IconType}
          iconOnly
          fill="unfilled"
          size='smallest'
          color={editor.isActive(style) ? 'primary' : 'grayscale'}
          isActive={editor.isActive(style)}
          onClick={() => {
            if (style === 'format_clear') {
              editor.chain().focus().unsetAllMarks().run();
            } else {
              editor.chain().focus().toggleMark(style).run();
            }
          }}
        />
      ))}
    </div>
  );
};

export default FontStyle;
