import api from '@api/api';
import { useAlert } from '@hooks/useAlert';
import { useAuthStore } from '@page/User/Role/store/useAuthStore';
import { AuthData, DetailFormValues, MenuData, ParsedServiceData, SearchFormValues } from '@page/User/Role/type';
import { ServiceData } from '@page/User/Service/type';
import { UseFormReturn } from 'react-hook-form';

interface UseUserRoleAPIProps {
  searchMethods?: UseFormReturn<SearchFormValues>; // 검색 폼 메서드
  detailMethods?: UseFormReturn<DetailFormValues>; // 상세 폼 메서드
}

export const useUserRoleAPI = ({ searchMethods, detailMethods }: UseUserRoleAPIProps = {}) => {
  // API 요청 주소
  const USER_ROLE = '/api-admin/user/roles';
  const USER_SERVICE = '/api-admin/user/services';
  const USER_MENU = '/api-admin/user/menus';
  const {
    setAuthListData,
    originalAuthListData,
    setOriginalAuthListData,
    setAuthData,
    setDetailState,
    setMenuListData,
    setServiceListData,
  } = useAuthStore();
  const { openAlert } = useAlert();
  const { reset } = detailMethods || {};

  // 권한 목록 조회
  const getAuthList = async () => {
    try {
      const response = await api.get(`${USER_ROLE}`);
      const authList = response.data.data;
      setAuthListData(authList);
      setOriginalAuthListData(authList);
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 권한 목록 검색
  const searchAuth = () => {
    const searchKeyword = searchMethods?.getValues('name');
    if (searchKeyword === '') {
      setAuthListData(originalAuthListData);
      return;
    }
    const searchResult = originalAuthListData.filter((auth: AuthData) =>
      auth.name.toLowerCase().includes(searchKeyword.toLowerCase())
    );
    setAuthListData(searchResult);
  };

  // 권한 상세 조회
  const getAuthInfoData = async (id: number) => {
    try {
      const response = await api.get(`${USER_ROLE}/${id}`);
      const authData = response.data.data;
      setAuthData(authData);
      reset({
        name: authData.name,
        menuIds: authData.menuIds,
        serviceIds: authData.serviceIds,
      });
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 전체 메뉴 목록과 전체 서비스 목록 조회
  const getMenuAndServiceData = async () => {
    try {
      const menusListResponse = await api.get(USER_MENU);
      const servicesListResponse = await api.get(USER_SERVICE);
      const menuListData = menusListResponse.data.data;
      const serviceListData = servicesListResponse.data.data;
      setMenuListData(sortMenuData(menuListData));
      setServiceListData(parseServiceData(serviceListData));
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 권한 추가
  const addAuth = async (data: DetailFormValues) => {
    try {
      const response = await api.post(USER_ROLE, {
        name: data.name,
        menuIds: data.menuIds,
        serviceIds: data.serviceIds,
      });
      openAlert(response.data.message, () => {
        getAuthList();
      });
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 권한 수정
  const updateAuth = async (id: number, data: DetailFormValues) => {
    try {
      const response = await api.post(`${USER_ROLE}/${id}`, {
        name: data.name,
        menuIds: data.menuIds,
        serviceIds: data.serviceIds,
      });
      openAlert(response.data.message, () => {
        getAuthList();
      });
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 권한 삭제
  const deleteAuth = async (id: number) => {
    try {
      const response = await api.post(`${USER_ROLE}/${id}/delete`);
      openAlert(response.data.message, () => {
        reset({
          name: '',
          menuIds: [],
          serviceIds: [],
        });
        setDetailState(null);
        setAuthData(null);
        getAuthList();
      });
    } catch (error) {
      openAlert(error.response.data.message);
    }
  };

  // 전체 메뉴 정렬 (sortNo 기준으로 순서 정렬)
  const sortMenuData = (menuList: MenuData[]): MenuData[] => {
    return menuList
      .map((menu) => {
        const updatedChildMenus = menu.childs ? sortMenuData(menu.childs) : [];
        return {
          ...menu,
          childs: updatedChildMenus,
        };
      })
      .sort((a, b) => a.sortNo - b.sortNo);
  };

  // 서비스 그룹별로 데이터 그룹화 (서비스 그룹별로 파싱
  const parseServiceData = (serviceList: ServiceData[]): ParsedServiceData[] => {
    // 서비스 그룹별로 데이터 그룹화
    const groupedServices = serviceList.reduce((acc, service) => {
      const groupId = service.serviceGroupId;
      if (!acc[groupId]) {
        acc[groupId] = {
          id: groupId,
          name: service.serviceGroupName,
          depth: 1,
          childs: [],
        };
      }
      acc[groupId].childs.push({
        id: service.id,
        name: service.name,
        depth: 2,
        serviceGroupId: service.serviceGroupId,
        serviceGroupName: service.serviceGroupName,
        serviceDesc: service.serviceDesc,
      });
      return acc;
    }, {});

    // 그룹화된 데이터를 배열로 변환
    return Object.values(groupedServices);
  };

  return { getAuthList, searchAuth, getAuthInfoData, getMenuAndServiceData, addAuth, updateAuth, deleteAuth };
};
